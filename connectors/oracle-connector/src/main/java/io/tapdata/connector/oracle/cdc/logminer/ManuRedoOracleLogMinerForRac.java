package io.tapdata.connector.oracle.cdc.logminer;

import io.tapdata.common.cdc.LogTransaction;
import io.tapdata.common.cdc.RedoLogContent;
import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.cdc.logminer.bean.ArchivedLog;
import io.tapdata.connector.oracle.cdc.logminer.bean.OracleRedoLogBatch;
import io.tapdata.connector.oracle.cdc.logminer.bean.RedoLog;
import io.tapdata.connector.oracle.cdc.logminer.util.JdbcUtil;
import io.tapdata.connector.oracle.cdc.offset.OracleOffset;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.control.HeartbeatEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.exception.TapPdkTerminateByServerEx;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.ErrorKit;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static io.tapdata.connector.oracle.cdc.logminer.constant.OracleSqlConstant.*;

@Deprecated
public class ManuRedoOracleLogMinerForRac extends ManuRedoOracleLogMiner {

    private final AtomicLong lastScnRef;
    private final AtomicLong continuousScn;
    private final AtomicReference<Exception> exceptionRef = new AtomicReference<>();
    private final AtomicLong increaseScn;
    private final Map<Long, Boolean> archivedStates;

    public ManuRedoOracleLogMinerForRac(OracleJdbcContext oracleJdbcContext, String connectorId, Log tapLogger) throws Throwable {
        super(oracleJdbcContext, connectorId, tapLogger);
        //挖掘信号队列
        redoLogBundleForAnalysisQueue = new LinkedBlockingQueue<>(1);
        lastScnRef = new AtomicLong();
        continuousScn = new AtomicLong();
        increaseScn = new AtomicLong();
        archivedStates = new HashMap<>();
    }

    protected void readyForInit() {
        isRunning.set(true);
        initRedoLogQueueAndThread();
        redoLogFactory = new RedoLogFactory(oracleJdbcContext, isRunning);
        redoLogFactory.setOracleInstanceInfos(oracleInstanceInfos);
        //挖掘起始scn
        lastScnRef.set(oracleOffset.getPendingScn() > 0 && oracleOffset.getPendingScn() < oracleOffset.getLastScn() ? oracleOffset.getPendingScn() : oracleOffset.getLastScn());
        continuousScn.set(lastScnRef.get());
        increaseScn.set(lastScnRef.get());
        //生成挖掘信号的定时线程
        scheduledExecutorService.scheduleWithFixedDelay(() -> {
            long length = 30000L;
            while (isRunning.get()) {
                if (!produceSignal(length)) {
                    break;
                }
            }
        }, 1, 20, TimeUnit.SECONDS);
    }

    private boolean produceSignal(long length) {
        OracleRedoLogBatch oracleRedoLogBatch;
        Map<Long, Boolean> newArchivedStates = new HashMap<>();
        try {
            boolean canOffer = false;
            oracleRedoLogBatch = redoLogFactory.produceRacRedoLog(continuousScn.get(), length);
            if (oracleRedoLogBatch.isAllArchived()) {
                tapLogger.info("【rac miner】find all archived redo log, restart mining: {}", oracleRedoLogBatch);
                increaseScn.set(oracleRedoLogBatch.getStopMineSCN());
                newArchivedStates.putAll(oracleRedoLogBatch.getStartScnRedoLogMap().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> EmptyKit.isNull(entry.getValue().getArchivedRedoLog()))));
                canOffer = true;
            } else {
                oracleRedoLogBatch = this.redoLogFactory.produceRacRedoLog(increaseScn.get(), length);
                newArchivedStates.putAll(oracleRedoLogBatch.getStartScnRedoLogMap().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> EmptyKit.isNull(entry.getValue().getArchivedRedoLog()))));

                //如果前后状态一致，除了不停追归档日志的情况可以源源不断地发信号
                if (newArchivedStates.entrySet().stream().allMatch(v -> archivedStates.get(v.getKey()) == v.getValue())) {
                    if (EmptyKit.isNotNull(oracleRedoLogBatch.getStopMineSCN())) {
                        //下次信号按照上次的stopScn发
                        increaseScn.set(oracleRedoLogBatch.getStopMineSCN());
                        canOffer = true;
                    } else {
                        //不发送信号，但是更新startScn
                        increaseScn.set(instanceThreadMindedSCNMap.values().stream().min(Long::compareTo).orElse(lastScnRef.get()));
                    }
                } else {
                    //状态不一致，更新状态，发送信号
                    canOffer = true;
                }
            }
            archivedStates.putAll(newArchivedStates);
            //发送信号
            if (canOffer) {
                AnalysisRedoLogBundle analysisRedoLogBundle = new AnalysisRedoLogBundle(oracleRedoLogBatch, null);
                while (isRunning.get() && !redoLogBundleForAnalysisQueue.offer(analysisRedoLogBundle, 5, TimeUnit.SECONDS)) {
                    // nothing to do
                    tapLogger.debug("Redo log queue for analysis is full, redo log {} waiting to enqueue", oracleRedoLogBatch);
                }
            }
            //全归档的情况下，不需要跳出循环，继续挖掘
            if (EmptyKit.isNull(oracleRedoLogBatch.getStopMineSCN())) {
                return false;
            }
        } catch (Exception e) {
            exceptionRef.set(e);
        }
        return true;
    }

    @Override
    public void startMiner() throws Throwable {
        setSession();
        //不停地拉取信号，挖掘日志
        while (isRunning.get()) {
            final AnalysisRedoLogBundle analysisRedoLogBundle;
            try {
                analysisRedoLogBundle = redoLogBundleForAnalysisQueue.poll(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                break;
            }
            if (analysisRedoLogBundle != null) {
                OracleRedoLogBatch oracleRedoLogBatch = analysisRedoLogBundle.getOracleRedoLogBatch();
                tapLogger.info("Starting analysis redo log {}", analysisRedoLogBundle.getOracleRedoLogBatch());
                doMine(oracleRedoLogBatch, null);
            }
        }
    }

    //挖掘日志
    protected void doMine(OracleRedoLogBatch oracleRedoLogBatch,
                          Consumer<Map<String, Object>> redoLogContentConsumer) throws Exception {
        try {
            boolean onlineRedo = oracleRedoLogBatch.isOnlineRedo();
            boolean canInterrupt = !oracleRedoLogBatch.isAllArchived();
            if (!canInterrupt) {
                if (continuousScn.get() >= oracleRedoLogBatch.getStopMineSCN()) {
                    tapLogger.info("【rac miner】same signal, skip this batch", lastScnRef.get(), oracleRedoLogBatch.getStopMineSCN());
                    return;
                }
                lastScnRef.set(continuousScn.get());
                instanceThreadMindedSCNMap.clear();
            }
            String addLogMinerSql = createAddLogMinerSql(oracleRedoLogBatch);
            statement.execute(addLogMinerSql);
            boolean needToStartAnalysis = true;
            String executeSql = START_LOG_MINOR_SQL;
            cacheRedoLogContent.clear();
            boolean hasError = false;
            while (isRunning.get()) {
                if (hasError) {
                    executeSql = START_LOG_MINOR_SQL;
                    hasError = false;
                    needToStartAnalysis = true;
                }
                if (needToStartAnalysis) {
                    try {
                        tapLogger.info("Start log miner to analysis redo logs size:{}" +
                                        ", start log miner sql: {}, add log file sql {}",
                                oracleRedoLogBatch.getRedoLogs().size(),
                                executeSql,
                                addLogMinerSql
                        );
                        needToStartAnalysis = false;
                        statement.execute(executeSql);
                    } catch (SQLException e) {
                        String message = e.getMessage();
                        if (StringUtils.isNotBlank(message) && message.contains("ORA-00600") && !onlineRedo) {
                            tapLogger.warn("Start oracle log miner failed, will retry with DIC_FROM_REDO_LOGS options; Cause: " + e.getMessage());
                            AtomicReference<ArchivedLog> atomicReference = new AtomicReference<>();
                            oracleJdbcContext.queryWithNext(String.format(LAST_DICT_ARCHIVE_LOG_BY_SCN, lastScnRef.get()),
                                    resultSet -> atomicReference.set(new ArchivedLog(resultSet)));
                            ArchivedLog lastDictArchivedLogByScn = atomicReference.get();
                            if (oracleRedoLogBatch.getRedoLogs().stream().anyMatch(redoLog -> redoLog.getName().equals(lastDictArchivedLogByScn.getName()))) {
                                try {
                                    tapLogger.info("Add log file {} to logminer", lastDictArchivedLogByScn.getName());
                                    statement.execute(String.format(ADD_LOGFILE_SQL, lastDictArchivedLogByScn.getName()));
                                } catch (SQLException ignore) {
                                }
                            }
                            executeSql = START_LOG_MINOR_DIC_FROM_REDO_LOGS_SQL;
                            statement.execute(START_LOG_MINOR_DIC_FROM_REDO_LOGS_SQL);
                            hasError = true;
                        } else {
                            throw e;
                        }
                    }
                }

                statement.setFetchSize(1000);
                boolean needExit = false;
                try {
                    //兜底最小SCN-1开始挖掘
                    long startMindScn = instanceThreadMindedSCNMap.values().stream().min(Long::compareTo).orElse(lastScnRef.get());
                    resultSet = statement.executeQuery(analyzeLogSql(startMindScn - 1));
                    while (resultSet.next() && isRunning.get()) {
                        if (EmptyKit.isNotNull(exceptionRef.get())) {
                            throw exceptionRef.get();
                        }
                        if (canInterrupt && EmptyKit.isNotEmpty(redoLogBundleForAnalysisQueue)) {
                            needExit = true;
                            break;
                        }
                        while (ddlStop.get()) {
                            TapSimplify.sleep(1000);
                        }
                        ResultSetMetaData metaData = resultSet.getMetaData();
                        Map<String, Object> logData = JdbcUtil.buildLogData(
                                metaData,
                                resultSet,
                                oracleConfig.getSysZoneId()
                        );
                        long scn = 0L;
                        if (logData.get("SCN") != null && logData.get("SCN") instanceof BigDecimal) {
                            scn = ((BigDecimal) logData.get("SCN")).longValue();
                            instanceThreadMindedSCNMap.put(((BigDecimal) logData.get("THREAD#")).longValue(), scn);
                            if (oracleRedoLogBatch.getStopMineSCN() != null && scn >= oracleRedoLogBatch.getStopMineSCN()) {
                                needExit = true;
                                break;
                            }
                        }
                        if (scn != lastScnRef.get()) {
                            cacheRedoLogContent.clear();
                        }
                        String redoLogContentId = RedoLogContent.id(
                                ((BigDecimal) logData.get("THREAD#")).intValue(),
                                (String) logData.get("XID"),
                                ((BigDecimal) logData.get("SCN")).longValue(),
                                (String) logData.get("RS_ID"),
                                ((BigDecimal) logData.get("SSN")).longValue(),
                                ((BigDecimal) logData.get("CSF")).intValue(),
                                (String) logData.get("SQL_REDO")
                        );
                        if (cacheRedoLogContent.containsKey(redoLogContentId)) {
                            continue;
                        }
                        cacheRedoLogContent.put(redoLogContentId, "");
                        analyzeLog(logData);
                        lastScnRef.set(scn != 0L ? scn : lastScnRef.get());
                    }
                } catch (SQLException e) {
                    if (e.getErrorCode() == 310 || e.getErrorCode() == 334) {
                        tapLogger.warn("switch log file error, will drop this batch: {}", oracleRedoLogBatch);
                    }
                } finally {
                    EmptyKit.closeQuietly(resultSet);
                }

                if (EmptyKit.isNotNull(oracleRedoLogBatch.getStopMineSCN()) || needExit) {
                    if (oracleRedoLogBatch.isAllArchived()) {
                        continuousScn.set(oracleRedoLogBatch.getStopMineSCN());
                        tapLogger.info("【rac miner】all archived, stop mine scn: {}", continuousScn.get());
                    }
                    break;
                }
            }
        } catch (Exception e) {
            if (EmptyKit.isNotBlank(e.getMessage()) && e.getMessage().contains("Closed Statement")) {
                throw new TapPdkTerminateByServerEx("oracle", ErrorKit.getLastCause(e));
            }
            throw e;
        } finally {
            ErrorKit.ignoreAnyError(() -> statement.execute(END_LOG_MINOR_SQL));
        }
    }

    public String createAddLogMinerSql(OracleRedoLogBatch oracleRedoLogBatch) {
        StringBuilder sb = new StringBuilder();
        if (oracleRedoLogBatch != null) {
            sb.append("BEGIN");
            int count = 0;

            Set<String> addedRedoLogNames = new HashSet<>();
            for (RedoLog redoLog : oracleRedoLogBatch.getRedoLogs()) {
                String name = redoLog.getName();
                if (!addedRedoLogNames.contains(name)) {
                    if (count == 0) {
                        sb.append(" SYS.dbms_logmnr.add_logfile(logfilename=>'").append(name).append("',options=>SYS.dbms_logmnr.NEW);");
                    } else {
                        sb.append(" SYS.dbms_logmnr.add_logfile(logfilename=>'").append(name).append("',options=>SYS.dbms_logmnr.ADDFILE);");
                    }
                    addedRedoLogNames.add(name);
                    count++;
                }
            }

            sb.append("END;");
        }
        return sb.toString();
    }

    @Override
    protected void submitEvent(RedoLogContent redoLogContent, List<TapEvent> eventList) {
        OracleOffset oracleOffset = new OracleOffset();
        if (EmptyKit.isNull(redoLogContent)) {
            return;
        }
        oracleOffset.setLastScn(redoLogContent.getScn());
        long minScn = continuousScn.get();
        Iterator<LogTransaction> iterator = transactionBucket.values().iterator();
        if (iterator.hasNext()) {
            oracleOffset.setPendingScn(Math.min(iterator.next().getScn(), minScn));
        } else {
            oracleOffset.setPendingScn(minScn);
        }
        oracleOffset.setTimestamp(referenceTime);
        if (eventList.size() > 0) {
            consumer.accept(eventList, oracleOffset);
        } else {
            consumer.accept(Collections.singletonList(new HeartbeatEvent().init().referenceTime(referenceTime)), oracleOffset);
        }
    }

    protected RedoLogContent wrapRedoLogContent(Object logData) throws SQLException {
        if (csfLogContent == null) {
            return buildRedoLogContent(logData);
        } else {
            if (logData instanceof Map) {
                if (!RedoLogContent.id(
                        ((BigDecimal) ((Map) logData).get("THREAD#")).intValue(),
                        (String) ((Map) logData).get("XID"),
                        ((BigDecimal) ((Map) logData).get("SCN")).longValue(),
                        (String) ((Map) logData).get("RS_ID"),
                        ((BigDecimal) ((Map) logData).get("SSN")).longValue(), 0, "").equals(
                        RedoLogContent.id(
                                csfLogContent.getThread(),
                                csfLogContent.getXid(),
                                csfLogContent.getScn(),
                                csfLogContent.getRsId(),
                                csfLogContent.getSsn(), 0, ""))) {
                    csfLogContent = null;
                    return buildRedoLogContent(logData);
                }
            }
            return appendRedoAndUndoSql(logData);
        }
    }
}
