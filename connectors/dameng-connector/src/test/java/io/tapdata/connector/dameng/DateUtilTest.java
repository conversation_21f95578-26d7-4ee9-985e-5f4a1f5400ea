package io.tapdata.connector.dameng;

import io.tapdata.connector.dameng.cdc.logminer.util.DateUtil;
import org.apache.commons.codec.DecoderException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.Instant;

public class DateUtilTest {

    @Test
    void testParseDateTime() {
        Instant actual = DateUtil.parseDateTime("2024-06-11 22:57:23.000000 +08:00", true);
        Assertions.assertTrue(1718117843 == actual.getEpochSecond());
    }

    @Test
    void testParseTimeTrue() {
        Object actualData = DateUtil.parseTime("23:23:23 +08:00", true);
        Assertions.assertTrue("15:23:23".equals(actualData.toString()));

    }

    @Test
    void testParseTimeFalse() throws DecoderException {
        String str = "0x222222222220";
        str = str.substring(2, str.length());
        int length = str.length() / 2;
        byte[] byteArray = new byte[length];
        for (int i = 0; i < length; i++) {
            String byteStr = str.substring(i * 2, i * 2 + 2);
            byteArray[i] = (byte) Integer.parseInt(byteStr, 16);
        }
        // 输出字节数组的16进制表示，以验证转换结果
        for (byte b : byteArray) {
            System.out.format("%02X ", b);
        }


    }

//    public static byte[] hexStringToByteArray(String hexStrings) {
//        byte[] byteArray = new byte[hexStrings.length];
//        for (int i = 0; i < hexStrings.length; i++) {
//            byteArray[i] = (byte) Integer.parseInt(hexStrings[i], 16);
//        }
//        return byteArray;
//    }

//    private static String extractStringInPairs(String input) {
//        StringBuilder result = new StringBuilder();
//        for (int i = 1; i < input.length(); i += 2) {
//            int endIndex = Math.min(i + 2, input.length());
//            result.append(input.substring(i, endIndex));
//        }
//        return result;
//    }

}
