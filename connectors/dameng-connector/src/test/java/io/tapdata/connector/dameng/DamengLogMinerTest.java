package io.tapdata.connector.dameng;

import dm.jdbc.driver.DmdbResultSetMetaData;
import io.tapdata.common.ResultSetConsumer;
import io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner;
import io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner;
import io.tapdata.entity.logger.TapLog;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.Collections;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class DamengLogMinerTest {

    @Test
    void testGetDataObjMap() throws Throwable {
        DamengContext damengContext =mock(DamengContext.class);
        DamengConfig damengConfig = new DamengConfig();
        damengConfig.setTimezone("+08:00");
        when(damengContext.getConfig()).thenReturn(damengConfig);
        DamengLogMiner damengLogMiner= new SingleDamengLogMiner(damengContext,"testId",new TapLog());
        ReflectionTestUtils.setField(damengLogMiner,"tableList", Collections.singletonList("test"));
        ResultSet rs =mock(ResultSet.class);
        when(rs.next()).thenReturn(true).thenReturn(false);
        when(rs.getString("OBJECT_ID")).thenReturn("test1");
        when(rs.getString("OBJECT_NAME")).thenReturn("test2");
        doAnswer(a -> {
            ResultSetConsumer argument = a.getArgument(1, ResultSetConsumer.class);
            argument.accept(rs);
            return null;
        }).when(damengContext).query(anyString(), any(ResultSetConsumer.class));
        ReflectionTestUtils.invokeMethod(damengLogMiner,"getDataObjMap");
        Map<String, String> dataObjMap = (Map<String, String>) ReflectionTestUtils.getField(damengLogMiner,"dataObjMap");
        Assertions.assertTrue(dataObjMap.containsKey("test1"));

    }


    @Test
    void testGetColumnType() throws Throwable {
        DamengContext damengContext =mock(DamengContext.class);
        DamengConfig damengConfig = new DamengConfig();
        damengConfig.setTimezone("+08:00");
        when(damengContext.getConfig()).thenReturn(damengConfig);
        DamengLogMiner damengLogMiner= new SingleDamengLogMiner(damengContext,"testId",new TapLog());
        ReflectionTestUtils.setField(damengLogMiner,"tableList", Collections.singletonList("test"));
        ResultSet rs =mock(ResultSet.class);
        ResultSetMetaData resultSetMetaData = mock(DmdbResultSetMetaData.class);
        when(resultSetMetaData.getColumnCount()).thenReturn(3);
        when(rs.getMetaData()).thenReturn(resultSetMetaData);
        when(rs.next()).thenReturn(true).thenReturn(false);
        when(resultSetMetaData.getColumnCount()).thenReturn(3);

        when(resultSetMetaData.getColumnType(1)).thenReturn(1111);
        when(resultSetMetaData.getColumnName(1)).thenReturn("test1");
        when(resultSetMetaData.getColumnTypeName(1)).thenReturn("DATETIME WITH TIME ZONE");

        when(resultSetMetaData.getColumnType(2)).thenReturn(1111);
        when(resultSetMetaData.getColumnName(2)).thenReturn("test2");
        when(resultSetMetaData.getColumnTypeName(2)).thenReturn("TIMESTAMP WITH TIME ZONE");

        when(resultSetMetaData.getColumnType(3)).thenReturn(1111);
        when(resultSetMetaData.getColumnName(3)).thenReturn("test3");
        when(resultSetMetaData.getColumnTypeName(3)).thenReturn("TIME WITH TIME ZONE");

        doAnswer(a -> {
            ResultSetConsumer argument = a.getArgument(1, ResultSetConsumer.class);
            argument.accept(rs);
            return null;
        }).when(damengContext).queryWithNext(anyString(), any(ResultSetConsumer.class));
        ReflectionTestUtils.invokeMethod(damengLogMiner,"getColumnType");
        Map<String, String> actualData = (Map<String, String>) ReflectionTestUtils.getField(damengLogMiner,"dateTimeTypeMap");
        Assertions.assertTrue(actualData.containsValue("TIME WITH TIME ZONE")
                && actualData.size() == 3);

    }
}
