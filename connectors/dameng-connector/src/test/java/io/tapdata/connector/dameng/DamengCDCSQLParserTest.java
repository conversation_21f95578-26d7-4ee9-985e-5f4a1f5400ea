package io.tapdata.connector.dameng;

import io.tapdata.connector.dameng.cdc.logminer.sqlparser.DamengCDCSQLParser;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class DamengCDCSQLParserTest {

    @Test
    void testNormalAndParseSQL() {
        DamengCDCSQLParser parser = new DamengCDCSQLParser();
        parser.from("UPDATE \"SYSDBA\".\"BMSQL_CUSTOMER\" SET \"C_BALANCE\" = 27823.79, \"C_DELIVERY_CNT\" = 2 WHERE \"C_W_ID\" = 7 AND \"C_D_ID\" = 8 AND \"C_ID\" = 535 AND \"C_DISCOUNT\" = 0.1997 AND \"C_CREDIT\" = 'GC' AND \"C_LAST\" = 'ESEPRIPRES' AND \"C_FIRST\" = 'WVPcx0IoYx' AND \"C_CREDIT_LIM\" = -50000 AND \"C_BALANCE\" = -10 AND \"C_YTD_PAYMENT\" = 10 AND \"C_PAYMENT_CNT\" = 1 AND \"C_DELIVERY_CNT\" = 1 AND \"C_STREET_1\" = 'mfyuJtN' AND \"C_STREET_2\" = 'rJCTxds' AND \"C_CITY\" = 'Utfn3YO' AND \"C_STATE\" = 'EY' AND \"C_ZIP\" = '*********' AND \"C_PHONE\" = '8250969689399228' AND \"C_SINCE\" = '2024-06-30 09:08:53.498' AND \"C_MIDDLE\" = 'OE' AND \"C_DATA\" = 'HiunJ2'", false);
    }

    @Test
    void testNormalInsertSQL() {
        DamengCDCSQLParser parser = new DamengCDCSQLParser();
        parser.from("INSERT INTO public.aa0011(a1 ,a2 ,a3 ,a4 ,a5 ,a6 ,a7) VALUES(3 ,'2024' ,'2024-07-01' ,'01:00:05' ,'2024-07-01 15:31:53' ,'2024-07-01 15:31:57' ,'2024-07-01 21:32:08+08') ", false);
    }

    @Test
    void testNormalUpdateSQL() {
        DamengCDCSQLParser parser = new DamengCDCSQLParser();
        parser.from("UPDATE public.aa0011 SET a2='2233' WHERE a1=1 AND a2='2024' AND a3='2024-07-01' AND a4='01:00:05' AND a5='2024-07-01 15:31:53' AND a6='2024-07-01 15:31:57' AND a7='2024-07-01 19:32:08+08'", false);
    }

    @Test
    void testInvalidUpdateSQL() {
        DamengCDCSQLParser parser = new DamengCDCSQLParser();
        Assertions.assertThrows(Exception.class, () -> parser.from("UPDATE public.testsqlserver SET  WHERE a1=7", false));
    }

    @Test
    void testNormalDeleteSQL() {
        DamengCDCSQLParser parser = new DamengCDCSQLParser();
        parser.from("DELETE FROM public.aa0033 WHERE a1=10", false);
    }

}


