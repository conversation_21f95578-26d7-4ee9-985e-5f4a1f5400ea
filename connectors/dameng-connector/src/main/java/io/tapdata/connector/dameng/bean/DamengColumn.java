package io.tapdata.connector.dameng.bean;

import io.tapdata.common.CommonColumn;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.kit.EmptyKit;


/**
 * <AUTHOR>
 */
public class DamengColumn extends CommonColumn {

    public DamengColumn(DataMap dataMap) {
        this.columnName = dataMap.getString("COLUMN_NAME");
        this.dataType = getDataType(dataMap);
        this.nullable = dataMap.getString("NULLABLE");
        this.remarks = dataMap.getString("COMMENTS");
        this.columnDefaultValue = null;
    }

    @Override
    public TapField getTapField() {
        return new TapField(this.columnName, this.dataType).nullable(this.isNullable()).
                defaultValue(columnDefaultValue).comment(this.remarks);
    }

    @Override
    protected Boolean isNullable() {
        return "Y".equals(this.nullable);
    }

    private String getDataType(DataMap dataMap) {
        String dataType = dataMap.getString("DATA_TYPE");
        String dataLength = dataMap.getString("DATA_LENGTH");
        String dataPrecision = dataMap.getString("DATA_PRECISION");
        String dataScale = dataMap.getString("DATA_SCALE");
        if (dataType.contains("(")) {
            return dataType;
        } else {
            switch (dataType) {
                case "CHAR":
                case "CHARACTER":
                case "VARCHAR":
                case "VARCHAR2":
                case "BINARY":
                case "VARBINARY":
                    return dataType + "(" + dataLength + ")";
                case "NUMBER":
                    if (EmptyKit.isNull(dataPrecision) && EmptyKit.isNull(dataScale)) {
                        return "NUMBER";
                    } else {
                        return "NUMBER(" + dataPrecision + "," + dataScale + ")";
                    }
                case "NUMERIC":
                    if (EmptyKit.isNull(dataPrecision) && EmptyKit.isNull(dataScale)) {
                        return "NUMERIC";
                    } else {
                        return "NUMERIC(" + dataPrecision + "," + dataScale + ")";
                    }
                case "DECIMAL":
                    if (EmptyKit.isNull(dataPrecision) && EmptyKit.isNull(dataScale)) {
                        return "DECIMAL";
                    } else {
                        return "DECIMAL(" + dataPrecision + "," + dataScale + ")";
                    }
                case "DEC":
                    if (EmptyKit.isNull(dataPrecision) && EmptyKit.isNull(dataScale)) {
                        return "DEC";
                    } else {
                        return "DEC(" + dataPrecision + "," + dataScale + ")";
                    }
                case "TIMESTAMP":
                case "DATETIME":
                case "TIME":
                    return dataType + "(" + dataScale + ")";
                case "DATETIME WITH TIME ZONE":
                    return "DATETIME(" + dataScale + ") WITH TIME ZONE";
                case "TIME WITH TIME ZONE":
                    return "TIME(" + dataScale + ") WITH TIME ZONE";
                default:
                    return dataType;
            }
        }
    }
}
