package io.tapdata.connector.dameng;

import io.tapdata.common.CommonDbConfig;
import io.tapdata.kit.EmptyKit;

import java.io.Serializable;
import java.time.ZoneId;
import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
public class DamengConfig extends CommonDbConfig implements Serializable {

    private String pdb = "";
    private Integer concurrency = 1;
    private String workDir = "cacheQueue";

    private long bigTransactionSize = 10000L;
    private Integer transactionAliveMinutes = 720;

    public DamengConfig() {
        setDbType("dm");
        setJdbcDriver("dm.jdbc.driver.DmDriver");
    }


    @Override
    public DamengConfig load(Map<String, Object> map) {
        DamengConfig config = (DamengConfig) super.load(map);
        if (EmptyKit.isEmpty(config.getSchema())) {
            config.setSchema(getUser().toUpperCase());
        }
        return config;
    }


    @Override
    public String getConnectionString() {
        String connectionString = getHost() + ":" + getPort();
        if (EmptyKit.isNotBlank(getSchema())) {
            connectionString += "/" + getSchema();
        }
        return connectionString;
    }

    @Override
    public String getDatabaseUrl() {
        if (EmptyKit.isNull(this.getExtParams())) {
            this.setExtParams("");
        }
        if (EmptyKit.isNotEmpty(this.getExtParams()) && !this.getExtParams().startsWith("?")) {
            this.setExtParams("?" + this.getExtParams());
        }

        return String.format(this.getDatabaseUrlPattern(), this.getHost(), this.getPort(), this.getDatabase(), this.getExtParams());

    }

    public Integer getConcurrency() {
        return concurrency;
    }

    public void setConcurrency(Integer concurrency) {
        this.concurrency = concurrency;
    }


    public String getWorkDir() {
        return workDir;
    }

    public void setWorkDir(String workDir) {
        this.workDir = workDir;
    }

    public String getPdb() {
        return pdb;
    }

    public void setPdb(String pdb) {
        this.pdb = pdb;
    }

    public long getBigTransactionSize() {
        return bigTransactionSize;
    }

    public void setBigTransactionSize(long bigTransactionSize) {
        this.bigTransactionSize = bigTransactionSize;
    }

    public Integer getTransactionAliveMinutes() {
        return transactionAliveMinutes;
    }

    public void setTransactionAliveMinutes(Integer transactionAliveMinutes) {
        this.transactionAliveMinutes = transactionAliveMinutes;
    }
}
