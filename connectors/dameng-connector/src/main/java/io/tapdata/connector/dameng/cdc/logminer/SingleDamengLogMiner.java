package io.tapdata.connector.dameng.cdc.logminer;

import io.tapdata.common.cdc.RedoLogContent;
import io.tapdata.connector.dameng.DamengConfig;
import io.tapdata.connector.dameng.DamengContext;
import io.tapdata.connector.dameng.cdc.logminer.util.JdbcUtil;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import org.apache.commons.collections.CollectionUtils;

import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

import static io.tapdata.connector.dameng.cdc.logminer.constant.DamengSqlConstant.END_LOG_MINOR_SQL;
import static io.tapdata.connector.dameng.cdc.logminer.constant.DamengSqlConstant.START_LOG_MINOR_CONTINUOUS_MINER_SQL;

public class SingleDamengLogMiner extends DamengLogMiner {

    private AtomicLong lastScn;
    private boolean first = true;

    private List<String> alreadyAddFile = new ArrayList<>();

    public SingleDamengLogMiner(DamengContext damengContext, String connectorId, Log tapLogger) throws Throwable {
        super(damengContext, tapLogger);
        lastScn = new AtomicLong(0L);
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        isRunning.set(true);
        initRedoLogQueueAndThread();
        lastScn.set(damengOffset.getPendingScn() > 0 && damengOffset.getPendingScn() < damengOffset.getLastScn() ? damengOffset.getPendingScn() : damengOffset.getLastScn());
        this.setLargeTransactionUpperLimit(((DamengConfig)damengContext.getConfig()).getBigTransactionSize());

    }

    @Override
    public void startMiner() throws Throwable {
        setSession();
        try {
            while (isRunning.get()) {
                if(threadException.get()!=null){
                    throw threadException.get();
                }
                if (first) {
                    first = false;
                } else {
                    statement.execute(END_LOG_MINOR_SQL);
                }
                String[] logNameAndNextChange = checkLogNameWithScn(lastScn.get());
                if (CollectionUtils.isEmpty(alreadyAddFile) || (alreadyAddFile.size() > 0 &&
                        !alreadyAddFile.contains(logNameAndNextChange[0]))) {
                    alreadyAddFile.clear();
                    alreadyAddFile.add(logNameAndNextChange[0]);
                    tapLogger.info(ADD_LOG_MINER_SQL_LOG, logNameAndNextChange[0]);
                }
                statement.execute(String.format(" SYS.dbms_logmnr.add_logfile(logfilename=>'%s',options=>SYS.dbms_logmnr.ADDFILE)", logNameAndNextChange[0]));
                statement.execute(String.format(START_LOG_MINOR_CONTINUOUS_MINER_SQL, lastScn.get()));
                resultSet = statement.executeQuery(analyzeLogSql(lastScn.get()));
                while (resultSet.next() && isRunning.get()) {
                    while (ddlStop.get()) {
                        TapSimplify.sleep(1000);
                    }
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    Map<String, Object> logData = JdbcUtil.buildLogData(
                            metaData,
                            resultSet,
                            damengConfig.getSysZoneId()
                    );
                    logData.put("ROLLBACK", logData.get("ROLL_BACK"));
                    logData.put("XID", bytesToHexString((byte[]) logData.get("XID")));
                    logData.put("STATUS", 0);
                    analyzeLog(logData);
                    if (logData.get("SCN") != null && logData.get("SCN") instanceof Long) {
                        lastScn.set(((Long) logData.get("SCN")));
                    }
                }
                lastScn.set(Long.parseLong(logNameAndNextChange[1]));
                TapSimplify.sleep(300);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String[] checkLogNameWithScn(Long startScn) {
        String[] logNameAndNextChange = new String[2];
        while (isRunning.get()) {
            try (
                    Connection connection = damengContext.getConnection();
                    PreparedStatement preparedStatement = connection.prepareStatement(SINGLE_LOG_FILE_CHECK)
            ) {
                preparedStatement.setLong(1, startScn);
                try (
                        ResultSet resultSet = preparedStatement.executeQuery()
                ) {
                    if (resultSet.next()) {
                        logNameAndNextChange[0] = resultSet.getString("NAME");
                        logNameAndNextChange[1] = resultSet.getString("NEXT_CHANGE#");
                        return logNameAndNextChange;
                    }
                }
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            TapSimplify.sleep(2000);
            RedoLogContent redoLogContent =new RedoLogContent();
            redoLogContent.setScn(startScn);
            Long referenceTime = new Date().getTime();
            redoLogContent.setTimestamp(new Timestamp(referenceTime));
            submitEvent(redoLogContent, Collections.singletonList(TapSimplify.heartbeatEvent(referenceTime)));
        }
        return logNameAndNextChange;

    }

    private final String SINGLE_LOG_FILE_CHECK = "select * from v$archived_log where next_change#>?";
    private static final String ADD_LOG_MINER_SQL_LOG = "【single miner】add log miner sql: {}";

    private static String bytesToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder(bytes.length);

        for (int index = 0; index < bytes.length; ++index) {
            byte aByte = bytes[index];
            String temp = Integer.toHexString(255 & aByte);
            if (temp.length() < 2) {
                sb.append(0);
            }

            sb.append(temp);
        }

        return sb.toString();

    }

    @Override
    public void stopMiner() throws Throwable {
        super.stopMiner();
    }
}
