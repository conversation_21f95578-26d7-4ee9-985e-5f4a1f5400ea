package io.tapdata.connector.dameng.cdc.logminer;

import io.tapdata.common.cdc.*;
import io.tapdata.common.ddl.DDLFactory;
import io.tapdata.common.ddl.type.DDLParserType;
import io.tapdata.connector.dameng.DamengConfig;
import io.tapdata.connector.dameng.DamengContext;
import io.tapdata.connector.dameng.cdc.DamengOffset;
import io.tapdata.connector.dameng.cdc.logminer.handler.UnicodeStringColumnHandler;
import io.tapdata.connector.dameng.cdc.logminer.sqlparser.DamengCDCSQLParser;
import io.tapdata.connector.dameng.cdc.logminer.util.ByteUtil;
import io.tapdata.constant.SqlConstant;
import io.tapdata.constant.TapLog;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.ErrorKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.util.DateUtil;
import oracle.sql.BLOB;
import oracle.sql.CLOB;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.*;
import java.time.*;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static io.tapdata.connector.dameng.cdc.logminer.constant.DamengSqlConstant.*;


public abstract class DamengLogMiner extends LogMiner implements ILogMiner {

    protected final static int TIMESTAMP_TZ_TYPE = -101;

    protected final DateTimeColumnHandler dateTimeColumnHandler;
    protected final DamengContext damengContext;
    protected final DamengConfig damengConfig;
    protected Connection connection;
    protected Statement statement;
    protected ResultSet resultSet;
    protected final String version;
    private final Map<String, Map<String, String>> dataFormatMap = new HashMap<>();
    //Map<table.column, java.dataType>
    protected Map<String, Integer> columnTypeMap = new HashMap<>();

    //Map<table.column, db.dataType>
    protected Map<String, String> dateTimeTypeMap = new HashMap<>();

    protected DamengOffset damengOffset;
    protected Map<String, String> dataObjMap = new HashMap<>();

    private Connection backFindLobConnection;

    private final Map<String, PreparedStatement> backFindLobPsmtMap = new HashMap<>();

    protected Map<String, TapTable> tables = new HashMap<>(); //table those have lob type


    private Long lastEventTimestamp = 0L;

    public DamengLogMiner(DamengContext damengContext, Log tapLogger) {
        this.damengContext = damengContext;
        this.tapLogger = tapLogger;
        damengConfig = (DamengConfig) damengContext.getConfig();
        dateTimeColumnHandler = new DateTimeColumnHandler(TimeZone.getTimeZone("GMT" + damengConfig.getTimezone()));
        version = damengContext.queryVersion();
        ddlParserType = DDLParserType.DAMENG_CCJ_SQL_PARSER;
        if (damengContext.queryCaseSensitive()) {
            DDL_WRAPPER_CONFIG.caseSensitive(true);
        }
    }

    // init with pdk params
    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        makeOffset(offsetState);
        getColumnType();
        getDataObjMap();
        makeTables();
    }

    protected void makeTables() {
        tableList.forEach(table -> {
            TapTable tapTable = tableMap.get(table);
            if (null == tapTable || null == tapTable.getNameFieldMap()) {
                return;
            }
            tables.put(table, tapTable);
        });
    }

    // get dataObjMap
    private void getDataObjMap() {
        try {
            damengContext.query(String.format("select OBJECT_ID, OBJECT_NAME from ALL_OBJECTS where owner='%s' and OBJECT_TYPE='TABLE' and OBJECT_NAME in ('%s')", damengConfig.getSchema(), String.join("','", tableList)),
                    resultSet -> {
                        while (resultSet.next()) {
                            dataObjMap.put(resultSet.getString("OBJECT_ID"), resultSet.getString("OBJECT_NAME"));
                        }
                    });
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    // store dataType in Map
    private void getColumnType() {
        tableList.forEach(table -> {
            try {
                damengContext.queryWithNext("SELECT * FROM \"" + damengConfig.getSchema() + "\".\"" + table + "\"", resultSet -> {
                    ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
                    for (int i = 1; i <= resultSetMetaData.getColumnCount(); i++) {

                        int colType = resultSetMetaData.getColumnType(i);
                        if ("DATETIME WITH TIME ZONE".equals(resultSetMetaData.getColumnTypeName(i)) ||
                                "TIMESTAMP WITH TIME ZONE".equals(resultSetMetaData.getColumnTypeName(i))) {
                            colType = Types.TIMESTAMP_WITH_TIMEZONE;
                        } else if ("TIME WITH TIME ZONE".equals(resultSetMetaData.getColumnTypeName(i))) {
                            colType = Types.TIME_WITH_TIMEZONE;
                        }
                        columnTypeMap.put(table + "." + resultSetMetaData.getColumnName(i), colType);
                        if (colType == Types.DATE || colType == Types.TIME || colType == Types.TIMESTAMP ||
                                colType == Types.TIMESTAMP_WITH_TIMEZONE || colType == Types.TIME_WITH_TIMEZONE) {
                            dateTimeTypeMap.put(table + "." + resultSetMetaData.getColumnName(i), resultSetMetaData.getColumnTypeName(i));
                        }
                    }
                });
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * find current scn of database
     *
     * @return scn Long
     * @throws Throwable SQLException
     */
    private long findCurrentScn() throws Throwable {
        AtomicLong currentScn = new AtomicLong();
        String sql = CHECK_CURRENT_SCN;
        damengContext.queryWithNext(sql, resultSet -> currentScn.set(resultSet.getLong(1)));
        return currentScn.get();
    }


    private void makeOffset(Object offsetState) throws Throwable {
        if (EmptyKit.isNull(offsetState)) {
            damengOffset = new DamengOffset();
            long currentScn = findCurrentScn();
            damengOffset.setLastScn(currentScn);
            damengOffset.setPendingScn(currentScn);
        } else {
            damengOffset = (DamengOffset) offsetState;
            if (EmptyKit.isNull(damengOffset.getLastScn())) {
                long currentScn = findCurrentScn();
                damengOffset.setLastScn(currentScn);
                damengOffset.setPendingScn(currentScn);
            }
        }
    }


    protected void setSession() throws SQLException {
        connection = damengContext.getConnection();
        statement = connection.createStatement();
        if (EmptyKit.isNotBlank(damengConfig.getPdb())) {
            tapLogger.info("database is containerised, switching...");
            statement.execute(SWITCH_TO_CDB_ROOT);
        }

    }

    protected void initRedoLogQueueAndThread() {
        if (redoLogConsumerThreadPool == null) {
            redoLogConsumerThreadPool = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
            redoLogConsumerThreadPool.submit(() -> {
                RedoLogContent redoLogContent;
                while (isRunning.get()) {
                    while (ddlStop.get()) {
                        TapSimplify.sleep(1000);
                    }
                    try {
                        redoLogContent = logQueue.poll(1, TimeUnit.SECONDS);
                        if (redoLogContent == null) {
                            continue;
                        }
                    } catch (Exception e) {
                        break;
                    }
                    try {
                        // parse sql
                        if (canParse(redoLogContent)) {
                            RedoLogContent.OperationEnum operationEnum = RedoLogContent.OperationEnum.fromOperationCode(redoLogContent.getOperationCode());
                            String sqlRedo = redoLogContent.getSqlRedo();
                            if (operationEnum == RedoLogContent.OperationEnum.DELETE || operationEnum == RedoLogContent.OperationEnum.UPDATE) {
                                sqlRedo = sqlRedo + " ";
                            }
                            redoLogContent.setRedoRecord(new DamengCDCSQLParser().from(sqlRedo, false).getData());
                            redoLogContent.setUndoRecord(new DamengCDCSQLParser().from(sqlRedo, true).getData());
                            redoLogContent.setSqlUndo(".");
                            convertStringToObject(redoLogContent);
                        }
                        // process and callback
                        processOrBuffRedoLogContent(redoLogContent, this::sendTransaction);

                    } catch (Throwable e) {
                        threadException.set(e);
                        consumer.streamReadEnded();
                    }
                }
            });

            redoLogConsumerThreadPool.submit(() -> {
                try {
                    while (isRunning.get()) {
                        Iterator<String> iterator = transactionBucket.keySet().iterator();
                        while (iterator.hasNext()) {
                            String xid = iterator.next();
                            LogTransaction transaction = transactionBucket.get(xid);
                            if (lastEventTimestamp - transaction.getFirstTimestamp() < damengConfig.getTransactionAliveMinutes() * 60 * 1000L) {
                                break;
                            } else {
                                tapLogger.warn("Uncommitted transaction {} with {} events will be dropped", xid, transaction.getSize());
                                transaction.clearRedoLogContents();
                                iterator.remove();
                            }
                        }
                        int sleep = 60;
                        try {
                            while (isRunning.get() && (sleep-- > 0)) {
                                TapSimplify.sleep(1000);
                            }
                        } catch (Exception ignore) {
                        }
                    }
                } catch (Exception e) {
                    threadException.set(e);
                }
            });
        }
    }

    /**
     * convert log redo string to Object(jdbc)
     *
     * @param redoLogContent oracle log content
     */
    protected void convertStringToObject(RedoLogContent redoLogContent) {
        String table = redoLogContent.getTableName();
        if (EmptyKit.isNotNull(redoLogContent.getRedoRecord())) {
            redoLogContent.getRedoRecord().remove("ROWID");
            for (Map.Entry<String, Object> stringObjectEntry : redoLogContent.getRedoRecord().entrySet()) {
                parseKeyValue(table, stringObjectEntry);
            }
        }
        if (EmptyKit.isNotNull(redoLogContent.getUndoRecord())) {
            redoLogContent.getUndoRecord().remove("ROWID");
            for (Map.Entry<String, Object> stringObjectEntry : redoLogContent.getUndoRecord().entrySet()) {
                parseKeyValue(table, stringObjectEntry);
            }
        }

    }

    protected void parseKeyValue(String table, Map.Entry<String, Object> stringObjectEntry) {
        Object value = stringObjectEntry.getValue();
        String column = stringObjectEntry.getKey();
        Integer columnType = columnTypeMap.get(table + "." + column);
        if (EmptyKit.isNull(value) || !(value instanceof String) || EmptyKit.isNull(columnType)) {
            return;
        }
        switch (columnType) {
            case Types.BIGINT:
                stringObjectEntry.setValue(new BigDecimal((String) value).longValue());
                break;
            case Types.BINARY:
            case Types.LONGVARBINARY:
            case Types.VARBINARY:
                try {
                    stringObjectEntry.setValue(ByteUtil.parseHex((String) value));
                } catch (Exception e) {
                    tapLogger.warn(TapLog.W_CONN_LOG_0014.getMsg(), value, columnType, e.getMessage());
                }
                break;
            case Types.BIT:
            case Types.BOOLEAN:
                stringObjectEntry.setValue(Boolean.valueOf((String) value));
                break;
            case Types.CHAR:
            case Types.LONGNVARCHAR:
            case Types.LONGVARCHAR:
            case Types.VARCHAR:
            case Types.ROWID:
            case Types.ARRAY:
            case Types.DATALINK:
            case Types.DISTINCT:
            case Types.JAVA_OBJECT:
            case Types.NULL:
            case Types.REF:
            case Types.REF_CURSOR:
            case Types.SQLXML:
            case Types.STRUCT:
            case Types.CLOB:
                break;
            case Types.NCHAR:
            case Types.NVARCHAR:
                stringObjectEntry.setValue(UnicodeStringColumnHandler.getUnicdeoString((String) value));
                break;
            case Types.DECIMAL:
            case Types.NUMERIC:
            case Types.DOUBLE:
                stringObjectEntry.setValue(new BigDecimal((String) value).doubleValue());
                break;
            case Types.FLOAT:
            case Types.REAL:
                stringObjectEntry.setValue(new BigDecimal((String) value).floatValue());
                break;
            case Types.INTEGER:
                stringObjectEntry.setValue(new BigDecimal((String) value).intValue());
                break;
            case Types.SMALLINT:
            case Types.TINYINT:
                stringObjectEntry.setValue(new BigDecimal((String) value).shortValue());
                break;
            case Types.DATE:
                stringObjectEntry.setValue(LocalDate.parse((String) value).atStartOfDay());
                break;
            case Types.TIME:
                stringObjectEntry.setValue(LocalTime.parse((String) value).minusHours(damengConfig.getZoneOffsetHour()).atDate(LocalDate.ofYearDay(1970, 1)));
                break;
            case Types.TIMESTAMP: {
                Map<String, String> dataFormat = dataFormatMap.get(table + "." + column);
                if (EmptyKit.isNull(dataFormat)) {
                    dataFormat = new HashMap<>();
                    dataFormatMap.put(table + "." + column, dataFormat);
                }
                DateUtil.mergeDateFormat((String) value, dataFormat);
                stringObjectEntry.setValue(DateUtil.parseInstantWithHour((String) value, dataFormat, damengConfig.getZoneOffsetHour()));
                break;
            }
            case Types.TIME_WITH_TIMEZONE: {
                Map<String, String> dataFormat = dataFormatMap.get(table + "." + column);
                if (EmptyKit.isNull(dataFormat)) {
                    dataFormat = new HashMap<>();
                    dataFormatMap.put(table + "." + column, dataFormat);
                }
                value = "1970-01-01 " + value;
                DateUtil.mergeDateFormat((String) value, dataFormat);
                stringObjectEntry.setValue(DateUtil.parseInstantWithZone((String) value, dataFormat, ZoneId.systemDefault()));
                break;
            }
            case Types.TIMESTAMP_WITH_TIMEZONE:
            case TIMESTAMP_TZ_TYPE: {
                Map<String, String> dataFormat = dataFormatMap.get(table + "." + column);
                if (EmptyKit.isNull(dataFormat)) {
                    dataFormat = new HashMap<>();
                    dataFormatMap.put(table + "." + column, dataFormat);
                }
                DateUtil.mergeDateFormat((String) value, dataFormat);
                stringObjectEntry.setValue(((Instant) DateUtil.parseInstant((String) value, dataFormat)).atZone(ZoneOffset.UTC));
                break;
            }
            case Types.BLOB:
                if (value instanceof String) {
                    try {
                        stringObjectEntry.setValue(ByteUtil.parseHex((String) value));
                    } catch (Exception e) {
                        tapLogger.warn(TapLog.W_CONN_LOG_0014.getMsg(), value, columnType, e.getMessage());
                    }
                }
                break;
        }

    }

    public boolean handleClobAndBlob(RedoLogContent redoLogContent) throws Throwable {
        String table = redoLogContent.getTableName();
        String schema = damengConfig.getSchema();
        if (redoLogContent.getRedoRecord().containsValue("OUT_CLOB_DAMENG") ||
                redoLogContent.getRedoRecord().containsValue("OUT_BLOB_DAMENG")) {
            Collection<String> unique = tables.get(table).primaryKeys(true);
            Map<String, Object> redo = redoLogContent.getRedoRecord();
            if (EmptyKit.isNull(backFindLobConnection) || !backFindLobConnection.isValid(3)) {
                if (EmptyKit.isNotNull(backFindLobConnection)) {
                    backFindLobConnection.close();
                }
                backFindLobConnection = damengContext.getConnection();
            }
            PreparedStatement backFindLobPsmt = backFindLobPsmtMap.get(table);
            if (EmptyKit.isNull(backFindLobPsmt)) {
                if (EmptyKit.isNotEmpty(unique)) {
                    backFindLobPsmt = backFindLobConnection.prepareStatement(
                            "SELECT * FROM \"" + schema + "\".\"" + redoLogContent.getTableName() + "\" WHERE " + unique.stream().map(v -> "\"" + v + "\"=?").collect(Collectors.joining(" AND "))
                    );
                } else {
                    backFindLobPsmt = backFindLobConnection.prepareStatement(
                            "SELECT * FROM \"" + schema + "\".\"" + redoLogContent.getTableName() + "\" WHERE ROWID = ?");
                }
                backFindLobPsmtMap.put(table, backFindLobPsmt);
            } else {
                backFindLobPsmt.clearParameters();
            }
            if (EmptyKit.isNotEmpty(unique)) {
                int pos = 1;
                for (String field : unique) {
                    backFindLobPsmt.setObject(pos++, redo.get(field));
                }
            } else {
                backFindLobPsmt.setObject(1, redoLogContent.getRowId());
            }
            try (ResultSet rs = backFindLobPsmt.executeQuery()) {
                if (rs.next()) {
                    DataMap dataMap = DbKit.getRowFromResultSet(rs, DbKit.getColumnsFromResultSet(rs));
                    if (EmptyKit.isNotEmpty(dataMap)) {
                        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                            Object o = entry.getValue();
                            if (o instanceof CLOB) {
                                entry.setValue(((CLOB) entry.getValue()).stringValue());
                            } else if (o instanceof BLOB) {
                                entry.setValue(DbKit.blobToBytes((BLOB) o));
                            }
                        }
                    }
                    redoLogContent.setRedoRecord(dataMap);
                } else {
                    for (Map.Entry<String, Object> entry : redoLogContent.getRedoRecord().entrySet()) {
                        if ("OUT_CLOB_DAMENG".equals(entry.getValue()) || "OUT_BLOB_DAMENG".equals(entry.getValue())) {
                            entry.setValue(null);
                        }
                    }
                }
            }
            redoLogContent.getUndoRecord().entrySet().removeIf(entry -> "OUT_CLOB_DAMENG".equals(entry.getValue()) || "OUT_BLOB_DAMENG".equals(entry.getValue()));
            return true;
        }
        return false;

    }

    private boolean canParse(RedoLogContent redoLogContent) {
        if (redoLogContent == null) {
            return false;
        }
        if (redoLogContent.getUndoRecord() != null || redoLogContent.getRedoRecord() != null) {
            return false;
        }
        switch (redoLogContent.getOperation()) {
            case SqlConstant.REDO_LOG_OPERATION_LOB_TRIM:
            case SqlConstant.REDO_LOG_OPERATION_LOB_WRITE:
            case SqlConstant.REDO_LOG_OPERATION_SEL_LOB_LOCATOR:
                return false;
            default:
                break;
        }
        String sqlRedo = redoLogContent.getSqlRedo();
        String sqlUndo = redoLogContent.getSqlUndo();
        if (StringUtils.isAllBlank(sqlRedo, sqlUndo)) {
            return false;
        }
        String operation = redoLogContent.getOperation();
        if (!StringUtils.equalsAny(operation,
                SqlConstant.REDO_LOG_OPERATION_INSERT,
                SqlConstant.REDO_LOG_OPERATION_UPDATE,
                SqlConstant.REDO_LOG_OPERATION_DELETE)) {
            return false;
        }

        return true;
    }

    @Override
    protected void ddlFlush() {
        columnTypeMap.clear();
        dateTimeTypeMap.clear();
        getColumnType();
        makeLobTables();
    }

    @Override
    protected void submitEvent(RedoLogContent redoLogContent, List<TapEvent> eventList) {
        DamengOffset oracleOffset = new DamengOffset();
        assert redoLogContent != null;
        oracleOffset.setLastScn(redoLogContent.getScn());
        oracleOffset.setPendingScn(redoLogContent.getScn());
        oracleOffset.setTimestamp(redoLogContent.getTimestamp().getTime());
        if (eventList.size() > 0) {
            consumer.accept(eventList, oracleOffset);
        }
    }

    @Override
    protected void batchCreateEvents(List<RedoLogContent> redoLogContentList, AtomicReference<List<TapEvent>> eventList, AtomicReference<RedoLogContent> lastRedoLogContent, LogTransaction logTransaction) {
        boolean hasPartRollback = EmptyKit.isNotEmpty(logTransaction.getPartRollbackMap());
        for (RedoLogContent redoLogContent : redoLogContentList) {
            lastRedoLogContent.set(redoLogContent);
            if ("DDL".equals(Objects.requireNonNull(redoLogContent).getOperation())) {
                try {
                    ddlStop.set(true);
                    TapSimplify.sleep(5000);
                    ddlFlush();
                    ddlStop.set(false);
                } catch (Throwable e) {
                    throw new RuntimeException(e);
                }
                try {
                    long referenceTime = redoLogContent.getTimestamp().getTime();
                    DDLFactory.ddlToTapDDLEvent(ddlParserType, redoLogContent.getSqlRedo(),
                            DDL_WRAPPER_CONFIG,
                            tableMap,
                            tapDDLEvent -> {
                                tapDDLEvent.setTime(System.currentTimeMillis());
                                tapDDLEvent.setReferenceTime(referenceTime);
                                eventList.get().add(tapDDLEvent);
                            });
                } catch (Throwable e) {
                    throw new RuntimeException(e);
                }
            } else {
                if (EmptyKit.isNull(Objects.requireNonNull(redoLogContent).getRedoRecord())) {
                    continue;
                }
                Map<String, Object> streamOffset = new HashMap<>();
                streamOffset.put("scn", redoLogContent.getScn());
                streamOffset.put("commitTime", redoLogContent.getTimestamp().getTime());
                TapRecordEvent recordEvent;
                if (hasPartRollback && logTransaction.getPartRollbackMap().containsKey(redoLogContent.generateRollbackKey())) {
                    logTransaction.decreasePartRollback(redoLogContent.generateRollbackKey());
                    continue;
                }
                try {
                    handleClobAndBlob(redoLogContent);
                } catch (Throwable e) {
                    tapLogger.warn("handle clob and blob error, Redo: {}", redoLogContent, e);
                }
                switch (Objects.requireNonNull(redoLogContent).getOperation()) {
                    case "INSERT": {
                        recordEvent = new TapInsertRecordEvent().init()
                                .table(redoLogContent.getTableName())
                                .after(redoLogContent.getRedoRecord());
                        break;
                    }
                    case "UPDATE": {
                        recordEvent = new TapUpdateRecordEvent().init()
                                .table(redoLogContent.getTableName())
                                .after(redoLogContent.getRedoRecord())
                                .before(redoLogContent.getUndoRecord());
                        break;
                    }
                    case "DELETE": {
                        recordEvent = new TapDeleteRecordEvent().init()
                                .table(redoLogContent.getTableName())
                                .before(redoLogContent.getRedoRecord());
                        break;
                    }
                    default:
                        continue;
                }
                recordEvent.setReferenceTime(redoLogContent.getTimestamp().getTime());
                recordEvent.addInfo("streamOffset", streamOffset);
                eventList.get().add(recordEvent);
            }
        }
    }

    protected String analyzeLogSql(Long scn) {
        String sql = GET_REDO_LOG_RESULT_ORACLE_LOG_COLLECT_SQL;
        sql = String.format(
                sql,
                scn,
                " DATA_OBJ# IN ('" + String.join("','", dataObjMap.keySet()) + "')"
        );
        return sql;
    }

    protected void analyzeLog(Object logData) throws SQLException {
        RedoLogContent redoLogContent = wrapRedoLogContent(logData);
        if (!validateRedoLogContent(redoLogContent)) {
            return;
        }
        if (csfRedoLogProcess(logData, redoLogContent)) {
            return;
        }
        String operation = redoLogContent.getOperation();
        if (SqlConstant.REDO_LOG_OPERATION_LOB_TRIM.equals(operation)
                || SqlConstant.REDO_LOG_OPERATION_LOB_WRITE.equals(operation)) {
            return;
        }
        if (SqlConstant.REDO_LOG_OPERATION_UNSUPPORTED.equals(operation)) {
            return;
        }
        lastEventTimestamp = redoLogContent.getTimestamp().getTime();
        enqueueRedoLogContent(redoLogContent);
    }

    private RedoLogContent wrapRedoLogContent(Object logData) throws SQLException {
        if (csfLogContent == null) {
            return buildRedoLogContent(logData);
        } else {
            return appendRedoAndUndoSql(logData);
        }
    }

    private RedoLogContent buildRedoLogContent(Object logData) throws SQLException {
        RedoLogContent redoLogContent;
        if (logData instanceof ResultSet) {
            redoLogContent = new RedoLogContent(resultSet, damengConfig.getSysZoneId());

        } else if (logData instanceof Map) {
            redoLogContent = new RedoLogContent((Map) logData);
        } else {
            redoLogContent = null;
        }
        return redoLogContent;
    }

    private RedoLogContent appendRedoAndUndoSql(Object logData) throws SQLException {
        if (logData == null) {
            return null;
        }

        String redoSql = "";
        String undoSql = "";

        if (logData instanceof ResultSet) {
            redoSql = ((ResultSet) logData).getString("SQL_REDO");
            undoSql = ((ResultSet) logData).getString("SQL_UNDO");
        } else if (logData instanceof Map) {
            Object sqlRedoObj = ((Map) logData).getOrDefault("SQL_REDO", "");
            if (sqlRedoObj != null) {
                redoSql = sqlRedoObj.toString();
            }
            final Object sqlUndoObj = ((Map) logData).getOrDefault("SQL_UNDO", "");
            if (sqlUndoObj != null) {
                undoSql = sqlUndoObj.toString();
            }
        }
        if (StringUtils.isNotBlank(redoSql)) {
            csfLogContent.setSqlRedo(csfLogContent.getSqlRedo() + redoSql);
        }

        if (StringUtils.isNotBlank(undoSql)) {
            csfLogContent.setSqlUndo(csfLogContent.getSqlUndo() + undoSql);
        }

        RedoLogContent redoLogContent = new RedoLogContent();
        beanUtils.copyProperties(csfLogContent, redoLogContent);
        return redoLogContent;
    }

    private boolean validateRedoLogContent(RedoLogContent redoLogContent) {
        if (redoLogContent == null) {
            return false;
        }

        if (!StringUtils.equalsAnyIgnoreCase(redoLogContent.getOperation(),
                SqlConstant.REDO_LOG_OPERATION_COMMIT, SqlConstant.REDO_LOG_OPERATION_ROLLBACK)) {
            // check owner
            if (StringUtils.isNotBlank(redoLogContent.getSegOwner())
                    && !damengConfig.getSchema().equals(redoLogContent.getSegOwner())) {
                return false;
            }
            // check table name
            return !EmptyKit.isNotBlank(redoLogContent.getTableName()) || tableList.contains(redoLogContent.getTableName());
        }

        return true;
    }

    private boolean csfRedoLogProcess(Object logData, RedoLogContent redoLogContent) {
        // handle continuation redo/undo sql
        if (isCsf(logData)) {
            if (csfLogContent == null) {
                csfLogContent = new RedoLogContent();
                beanUtils.copyProperties(redoLogContent, csfLogContent);
            }
            return true;
        } else {
            csfLogContent = null;
        }
        return false;
    }

    private static boolean isCsf(Object logData) {
        if (logData != null) {
            try {
                Integer csf = null;

                if (logData instanceof ResultSet) {
                    csf = ((ResultSet) logData).getInt("CSF");
                } else if (logData instanceof Map) {
                    csf = Integer.valueOf(((Map) logData).get("CSF").toString());
                }

                if (csf != null) {
                    return csf.equals(1);
                } else {
                    return false;
                }
            } catch (Exception e) {
                return false;
            }
        }

        return false;
    }

    @Override
    public abstract void startMiner() throws Throwable;

    @Override
    public void stopMiner() throws Throwable {
        super.stopMiner();
        ErrorKit.ignoreAnyError(() -> {
            if (EmptyKit.isNotNull(statement)) {
                statement.execute(END_LOG_MINOR_SQL);
                statement.close();
            }
        });
        if (EmptyKit.isNotNull(resultSet)) {
            resultSet.close();
            resultSet = null;
        }
        if (EmptyKit.isNotNull(connection)) {
            EmptyKit.closeQuietly(connection);
        }
        backFindLobPsmtMap.forEach((key, value) -> ErrorKit.ignoreAnyError(value::close));
        if (EmptyKit.isNotNull(backFindLobConnection)) {
            ErrorKit.ignoreAnyError(backFindLobConnection::close);
        }
    }
}
