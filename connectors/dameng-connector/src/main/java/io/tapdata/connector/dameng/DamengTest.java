package io.tapdata.connector.dameng;

import io.tapdata.common.CommonDbTest;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TestItem;
import io.tapdata.pdk.apis.exception.testItem.TapTestStreamReadEx;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

import static io.tapdata.base.ConnectorBase.testItem;

/**
 * <AUTHOR>
 */
public class DamengTest extends CommonDbTest {
    protected ConnectionOptions connectionOptions;

    public DamengTest(DamengConfig damengConfig, Consumer<TestItem> consumer) {
        super(damengConfig, consumer);
        jdbcContext = new DamengContext(damengConfig);
    }

    public DamengTest(DamengConfig damengConfig, Consumer<TestItem> consumer, ConnectionOptions connectionOptions) {
        this(damengConfig, consumer);
        this.connectionOptions = connectionOptions;
    }


    @Override
    public Boolean testReadPrivilege() {
        return true;
    }

    @Override
    public Boolean testStreamRead() {
        AtomicBoolean flag = new AtomicBoolean(false);
        try {
            if (queryArchini()) {
                jdbcContext.query(DM_CHECK_ARCH_SWL, resultSet -> {
                    while (resultSet.next()) {
                        int value = resultSet.getInt(1);
                        if (value > 0) {
                            flag.set(true);
                        } else {
                            consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY_WITH_WARN,
                                    " Dameng arch log did not create, will not be able to use the incremental sync feature "));
                            break;
                        }
                    }
                });
            }
        } catch (Exception e) {
            flag.set(false);
            consumer.accept(new TestItem(TestItem.ITEM_READ_LOG, new TapTestStreamReadEx(e), TestItem.RESULT_SUCCESSFULLY_WITH_WARN));
        }

        if (flag.get() == true) {
            consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY));
        }
        return true;
    }


    public boolean queryArchini() throws Exception {
        AtomicBoolean flag = new AtomicBoolean(false);
        jdbcContext.query(DM_STREAM_READ_SQL, resultSet -> {
            while (resultSet.next()) {
                if ("ARCH_INI".equalsIgnoreCase(resultSet.getString(1))) {
                    int value = resultSet.getInt(2);
                    if (value == 1) {
                        flag.set(true);
                    } else {
                        consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY_WITH_WARN,
                                " Dameng redo log is close, will not be able to use the incremental sync feature "));
                        break;
                    }
                }
            }
        });
        return flag.get();
    }

    @Override
    protected Boolean testDatasourceInstanceInfo() {
        buildDatasourceInstanceInfo(connectionOptions);
        return true;
    }

    private static final String DM_STREAM_READ_SQL = "select para_name, para_value from v$dm_ini where para_name in ('ARCH_INI','RLOG_APPEND_LOGIC')";

    private static final String DM_CHECK_ARCH_SWL = "select count(1) from v$archived_log";


}
