package io.tapdata.connector.dameng.exception;

import io.tapdata.common.exception.AbstractExceptionCollector;
import io.tapdata.common.exception.ExceptionCollector;
import io.tapdata.exception.TapPdkViolateUniqueEx;
import io.tapdata.kit.ErrorKit;

import java.sql.SQLException;
import java.util.List;

public class DamengExceptionCollector extends AbstractExceptionCollector implements ExceptionCollector {

    protected String getPdkId() {
        return "dameng";
    }

    @Override
    public void collectTerminateByServer(Throwable cause) {

    }

    @Override
    public void collectUserPwdInvalid(String username, Throwable cause) {

    }

    @Override
    public void collectOffsetInvalid(Object offset, Throwable cause) {

    }

    @Override
    public void collectReadPrivileges(Object operation, List<String> privileges, Throwable cause) {

    }

    @Override
    public void collectWritePrivileges(Object operation, List<String> privileges, Throwable cause) {

    }

    @Override
    public void collectWriteType(String targetFieldName, String targetFieldType, Object data, Throwable cause) {

    }

    @Override
    public void collectWriteLength(String targetFieldName, String targetFieldType, Object data, Throwable cause) {

    }

    @Override
    public void collectViolateUnique(String targetFieldName, Object data, Object constraint, Throwable cause) {
        if (cause instanceof SQLException) {
            if (((SQLException) cause).getErrorCode() == -6602 && ((SQLException) cause).getSQLState().equals("23000")) {
                throw new TapPdkViolateUniqueEx(getPdkId(), targetFieldName, data, null, ErrorKit.getLastCause(cause));
            }
        }
    }

    @Override
    public void collectViolateNull(String targetFieldName, Throwable cause) {

    }
}
