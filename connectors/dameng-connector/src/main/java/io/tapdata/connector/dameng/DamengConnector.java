package io.tapdata.connector.dameng;

import dm.jdbc.driver.*;
import io.tapdata.common.CommonDbConnector;
import io.tapdata.common.CommonSqlMaker;
import io.tapdata.common.SqlExecuteCommandFunction;
import io.tapdata.common.ddl.DDLSqlGenerator;
import io.tapdata.connector.dameng.bean.DamengColumn;
import io.tapdata.connector.dameng.cdc.DamengCdcRunner;
import io.tapdata.connector.dameng.cdc.DamengOffset;
import io.tapdata.connector.dameng.cdc.logminer.util.ByteUtil;
import io.tapdata.connector.dameng.ddl.DamengDDLSqlGenerator;
import io.tapdata.connector.dameng.dml.DamengRecordWriter;
import io.tapdata.connector.dameng.exception.DamengExceptionCollector;
import io.tapdata.entity.codec.TapCodecsRegistry;
import io.tapdata.entity.event.ddl.table.*;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapIndex;
import io.tapdata.entity.schema.TapIndexField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.value.*;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.simplify.pretty.BiClassHandlers;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.annotations.TapConnectorClass;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.context.TapConnectionContext;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.*;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.connection.ConnectionCheckItem;
import io.tapdata.pdk.apis.functions.connector.target.CreateTableOptions;
import oracle.sql.TIMESTAMP;
import org.apache.commons.lang3.StringUtils;

import java.sql.Date;
import java.sql.*;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static io.tapdata.connector.dameng.cdc.logminer.constant.DamengSqlConstant.*;

/**
 * <AUTHOR>
 */
@TapConnectorClass("spec_dameng.json")
public class DamengConnector extends CommonDbConnector {

    private DamengConfig damengConfig;

    private DamengContext damengContext;

    private DamengTest damengTest;

    private String damengVersion;

    private BiClassHandlers<TapFieldBaseEvent, TapConnectorContext, List<String>> fieldDDLHandlers;
    private DDLSqlGenerator ddlSqlGenerator;

    private static final int BATCH_ADVANCE_READ_LIMIT = 1000;

    private DamengCdcRunner cdcRunner;


    @Override
    public void onStart(TapConnectionContext connectionContext) {
        exceptionCollector = new DamengExceptionCollector();
        damengConfig = new DamengConfig().load(connectionContext.getConnectionConfig());
        damengTest = new DamengTest(damengConfig, testItem -> {
        });
        isConnectorStarted(connectionContext, tapConnectorContext -> damengConfig.load(tapConnectorContext.getNodeConfig()));
        damengContext = new DamengContext(damengConfig);
        String sysTimezone = damengContext.querySysTimezone();
        if (sysTimezone != null) {
            damengConfig.setSysZoneId(ZoneId.of(sysTimezone));
        } else {
            damengConfig.setSysZoneId(damengConfig.getZoneId());
        }
        commonDbConfig = damengConfig;
        jdbcContext = damengContext;
        damengVersion = damengContext.queryVersion();
        ddlSqlGenerator = new DamengDDLSqlGenerator();
        commonSqlMaker = new CommonSqlMaker();
        tapLogger = connectionContext.getLog();
        fieldDDLHandlers = new BiClassHandlers<>();
        fieldDDLHandlers.register(TapNewFieldEvent.class, this::newField);
        fieldDDLHandlers.register(TapAlterFieldAttributesEvent.class, this::alterFieldAttr);
        fieldDDLHandlers.register(TapAlterFieldNameEvent.class, this::alterFieldName);
        fieldDDLHandlers.register(TapDropFieldEvent.class, this::dropField);
    }


    @Override
    public void onStop(TapConnectionContext connectionContext) throws Throwable {

        if (EmptyKit.isNotNull(cdcRunner)) {
            cdcRunner.closeCdcRunner();
        }

        if (EmptyKit.isNotNull(damengTest)) {
            damengTest.close();
        }
        if (EmptyKit.isNotNull(damengContext)) {
            damengContext.close();
        }


    }

    @Override
    public void registerCapabilities(ConnectorFunctions connectorFunctions, TapCodecsRegistry codecRegistry) {
        //test
        connectorFunctions.supportConnectionCheckFunction(this::checkConnection);
        connectorFunctions.supportErrorHandleFunction(this::errorHandle);
        //target
        connectorFunctions.supportWriteRecord(this::writeRecord);
        connectorFunctions.supportCreateTableV2(this::createTableV2);
        connectorFunctions.supportClearTable(this::clearTable);
        connectorFunctions.supportDropTable(this::dropTable);
        //ddl
        connectorFunctions.supportNewFieldFunction(this::fieldDDLHandler);
        connectorFunctions.supportAlterFieldNameFunction(this::fieldDDLHandler);
        connectorFunctions.supportAlterFieldAttributesFunction(this::fieldDDLHandler);
        connectorFunctions.supportDropFieldFunction(this::fieldDDLHandler);
        //source
        connectorFunctions.supportBatchCount(this::batchCount);
        connectorFunctions.supportBatchRead(this::batchReadWithoutOffset);
        connectorFunctions.supportStreamRead(this::streamRead);
        connectorFunctions.supportTimestampToStreamOffset(this::timestampToStreamOffset);
        //query
        connectorFunctions.supportQueryByFilter(this::queryByFilter);
        connectorFunctions.supportQueryByAdvanceFilter(this::queryByAdvanceFilterWithOffsetV2);
        connectorFunctions.supportCountByPartitionFilterFunction(this::countByAdvanceFilterV2);
        connectorFunctions.supportGetTableNamesFunction(this::getTableNames);

        connectorFunctions.supportExecuteCommandFunction((a, b, c) -> SqlExecuteCommandFunction.executeCommand(a, b, () -> damengContext.getConnection(), this::isAlive, c));
        connectorFunctions.supportRunRawCommandFunction(this::runRawCommand);


        codecRegistry.registerFromTapValue(TapMapValue.class, "CLOB", tapMapValue -> {
            if (tapMapValue != null && tapMapValue.getValue() != null) {
                return toJson(tapMapValue.getValue());
            }
            return "null";
        });
        codecRegistry.registerFromTapValue(TapArrayValue.class, "CLOB", tapValue -> {
            if (tapValue != null && tapValue.getValue() != null) {
                return toJson(tapValue.getValue());
            }
            return "null";
        });
        codecRegistry.registerFromTapValue(TapBooleanValue.class, "INTEGER", tapValue -> {
            if (tapValue != null && tapValue.getValue() != null) {
                return tapValue.getValue() ? 1 : 0;
            }
            return 0;
        });

        codecRegistry.registerToTapValue(TIMESTAMP.class, (value, tapType) -> {
            try {
                return new TapDateTimeValue(new DateTime(((TIMESTAMP) value).toLocalDateTime().atZone(ZoneOffset.UTC)));
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });

        codecRegistry.registerToTapValue(DmdbClob.class, (value, tapType) -> new TapStringValue(((DmdbClob) value).data));
        codecRegistry.registerToTapValue(DmdbNClob.class, (value, tapType) -> {
            String str = ByteUtil.getReader(((DmdbNClob) value).getCharacterStream());
            if (EmptyKit.isNotNull(str)) {
                return new TapStringValue(str);
            } else {
                return new TapStringValue("");
            }
        });

        codecRegistry.registerToTapValue(DmdbIntervalDT.class, (value, tapType) -> new TapStringValue(((DmdbIntervalDT) value).getDTString()));
        codecRegistry.registerToTapValue(DmdbIntervalYM.class, (value, tapType) -> new TapStringValue(((DmdbIntervalYM) value).toString()));
        codecRegistry.registerToTapValue(DmdbBlob.class, (value, tapType) -> new TapBinaryValue(DbKit.blobToBytes((DmdbBlob) value)));

        codecRegistry.registerFromTapValue(TapDateTimeValue.class, tapDateTimeValue -> {
            if (EmptyKit.isNotNull(tapDateTimeValue.getValue().getTimeZone())) {
                return tapDateTimeValue.getValue().toInstant().atZone(tapDateTimeValue.getValue().getTimeZone().toZoneId());
            } else {
                if (damengConfig.getOldVersionTimezone()) {
                    return tapDateTimeValue.getValue().toTimestamp();
                } else {
                    return tapDateTimeValue.getValue().toInstant().atZone(damengConfig.getZoneId());
                }
            }
        });
        codecRegistry.registerFromTapValue(TapYearValue.class, "CHAR(4)", TapValue::getOriginValue);
        codecRegistry.registerFromTapValue(TapTimeValue.class, tapTimeValue -> {
            if (damengConfig.getOldVersionTimezone()) {
                return tapTimeValue.getValue().toTime();
            } else if (EmptyKit.isNotNull(tapTimeValue.getValue().getTimeZone())) {
                return tapTimeValue.getValue().toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
            } else {
                return tapTimeValue.getValue().toInstant().atZone(damengConfig.getZoneId()).toLocalTime();
            }
        });
        codecRegistry.registerFromTapValue(TapDateValue.class, tapDateValue -> tapDateValue.getValue().toSqlDate());

    }

    protected void getTableNames(TapConnectionContext tapConnectionContext, int batchSize, Consumer<List<String>> listConsumer) {
        damengContext.queryAllTables(list(), batchSize, listConsumer);
    }

    private void writeRecord(TapConnectorContext connectorContext, List<TapRecordEvent> tapRecordEvents, TapTable tapTable, Consumer<WriteListResult<TapRecordEvent>> writeListResultConsumer) throws SQLException {
        String insertDmlPolicy = connectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_INSERT_POLICY);
        if (insertDmlPolicy == null) {
            insertDmlPolicy = ConnectionOptions.DML_INSERT_POLICY_UPDATE_ON_EXISTS;
        }
        String updateDmlPolicy = connectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_UPDATE_POLICY);
        if (updateDmlPolicy == null) {
            updateDmlPolicy = ConnectionOptions.DML_UPDATE_POLICY_IGNORE_ON_NON_EXISTS;
        }
        new DamengRecordWriter(damengContext, tapTable).setVersion(damengVersion)
                .setInsertPolicy(insertDmlPolicy)
                .setUpdatePolicy(updateDmlPolicy)
                .write(tapRecordEvents, writeListResultConsumer, this::isAlive);
    }

    protected CreateTableOptions createTableV2(TapConnectorContext connectorContext, TapCreateTableEvent createTableEvent) {
        TapTable tapTable = createTableEvent.getTable();
        CreateTableOptions createTableOptions = new CreateTableOptions();
        if (damengContext.queryAllTables(Collections.singletonList(tapTable.getId())).size() > 0) {
            createTableOptions.setTableExists(true);
            return createTableOptions;
        }

        Map<String, TapField> fieldMap = tapTable.getNameFieldMap();
        for (String field : fieldMap.keySet()) {
            String fieldDeault = (String) fieldMap.get(field).getDefaultValue();
            if (StringUtils.isNotEmpty(fieldDeault)) {
                if (fieldDeault.contains("'")) {
                    fieldDeault = fieldDeault.replace("'", "");
                    fieldMap.get(field).setDefaultValue(fieldDeault);
                }
            }
        }

        Collection<String> primaryKeys = tapTable.primaryKeys();
        String sql = "CREATE TABLE \"" + damengConfig.getSchema() + "\".\"" + tapTable.getId() + "\"(";
        String column = new CommonSqlMaker().buildColumnDefinition(tapTable, false);
        // 达梦不支持 unsigned
        if (column.contains("unsigned") || column.contains("UNSIGNED")) {
            column = column.replaceAll("unsigned", "");
            column = column.replaceAll("UNSIGNED", "");
        }
        sql += column;
        if (EmptyKit.isNotEmpty(tapTable.primaryKeys())) {
            sql += "," + " PRIMARY KEY (\"" + String.join("\",\"", primaryKeys) + "\" )";
        }
        sql += ")";
        try {
            List<String> sqls = TapSimplify.list();
            sqls.add(sql);
            //comment on table and column
            if (EmptyKit.isNotNull(tapTable.getComment())) {
                sqls.add("COMMENT ON TABLE \"" + damengConfig.getSchema() + "\".\"" + tapTable.getId() + "\" IS '" + tapTable.getComment() + "'");
            }
            for (String fieldName : fieldMap.keySet()) {
                String fieldComment = fieldMap.get(fieldName).getComment();
                if (EmptyKit.isNotNull(fieldComment)) {
                    fieldComment = fieldComment.replaceAll("'", "''");
                    sqls.add("COMMENT ON COLUMN \"" + damengConfig.getSchema() + "\".\"" + tapTable.getId() + "\".\"" + fieldName + "\" IS '" + fieldComment + "'");
                }
            }
            damengContext.batchExecute(sqls);
        } catch (Throwable e) {
            throw new RuntimeException("Create Table " + tapTable.getId() + " Failed! " + e.getMessage());
        }
        createTableOptions.setTableExists(false);
        return createTableOptions;
    }

    protected void clearTable(TapConnectorContext tapConnectorContext, TapClearTableEvent tapClearTableEvent) {
        try {
            if (damengContext.queryAllTables(Collections.singletonList(tapClearTableEvent.getTableId())).size() == 1) {
                damengContext.execute("TRUNCATE TABLE \"" + damengConfig.getSchema() + "\".\"" + tapClearTableEvent.getTableId() + "\"");
            }
        } catch (Throwable e) {
            e.printStackTrace();
            throw new RuntimeException("TRUNCATE Table " + tapClearTableEvent.getTableId() + " Failed! \n ");
        }
    }

    protected void dropTable(TapConnectorContext tapConnectorContext, TapDropTableEvent tapDropTableEvent) {
        try {
            if (damengContext.queryAllTables(Collections.singletonList(tapDropTableEvent.getTableId())).size() == 1) {
                damengContext.execute("DROP TABLE \"" + damengConfig.getSchema() + "\".\"" + tapDropTableEvent.getTableId() + "\"");
            }
        } catch (Throwable e) {
            e.printStackTrace();
            throw new RuntimeException("Drop Table " + tapDropTableEvent.getTableId() + " Failed! \n ");
        }
    }

    protected long batchCount(TapConnectorContext tapConnectorContext, TapTable tapTable) throws Throwable {
        AtomicLong count = new AtomicLong(0);
        String sql = "SELECT COUNT(1) FROM \"" + damengConfig.getSchema() + "\".\"" + tapTable.getId() + "\"";
        damengContext.queryWithNext(sql, resultSet -> count.set(resultSet.getLong(1)));
        return count.get();
    }

    protected void queryByFilter(TapConnectorContext connectorContext, List<TapFilter> filters, TapTable tapTable, Consumer<List<FilterResult>> listConsumer) {
        Set<String> columnNames = tapTable.getNameFieldMap().keySet();
        List<FilterResult> filterResults = new LinkedList<>();
        for (TapFilter filter : filters) {
            String sql = "SELECT * FROM \"" + damengConfig.getSchema() + "\".\"" + tapTable.getId() + "\" WHERE " + new CommonSqlMaker().buildKeyAndValue(filter.getMatch(), "AND", "=");
            FilterResult filterResult = new FilterResult();
            try {
                damengContext.queryWithNext(sql, resultSet -> filterResult.setResult(DbKit.getRowFromResultSet(resultSet, columnNames)));
            } catch (Throwable e) {
                filterResult.setError(e);
            } finally {
                filterResults.add(filterResult);
            }
        }
        listConsumer.accept(filterResults);
    }

    @Override
    public void discoverSchema(TapConnectionContext connectionContext, List<String> tables, int tableSize, Consumer<List<TapTable>> consumer) {
        //get table info
        List<DataMap> tableList = damengContext.queryAllTables(tables);
        multiThreadDiscoverSchema(tableList, tableSize, consumer);
    }

    @Override
    protected void singleThreadDiscoverSchema(List<DataMap> subList, Consumer<List<TapTable>> consumer) {
        List<TapTable> tapTableList = TapSimplify.list();
        List<String> subTableNames = subList.stream().map(v -> v.getString("TABLE_NAME")).collect(Collectors.toList());
        List<DataMap> columnList = damengContext.queryAllColumns(subTableNames);
        List<DataMap> indexList = damengContext.queryAllIndexes(subTableNames);
        subList.forEach(subTable -> {
            //2、table name/comment
            String table = subTable.getString("TABLE_NAME");
            TapTable tapTable = table(table);
            tapTable.setComment(subTable.getString("COMMENTS"));
            //3、primary key and table index
            List<String> primaryKey = TapSimplify.list();
            List<TapIndex> tapIndexList = TapSimplify.list();
            Map<String, List<DataMap>> indexMap = indexList.stream().filter(idx -> table.equals(idx.getString("TABLE_NAME")))
                    .collect(Collectors.groupingBy(idx -> idx.getString("INDEX_NAME"), LinkedHashMap::new, Collectors.toList()));
            indexMap.forEach((key, value) -> {
                if (value.stream().anyMatch(v -> (Integer) v.get("IS_PK") == 1)) {
                    primaryKey.addAll(value.stream().map(v -> v.getString("COLUMN_NAME")).collect(Collectors.toList()));
                }
                TapIndex index = new TapIndex();
                index.setName(key);
                List<TapIndexField> fieldList = TapSimplify.list();
                value.forEach(v -> {
                    TapIndexField field = new TapIndexField();
                    field.setFieldAsc("ASC".equals(v.getString("DESCEND")));
                    field.setName(v.getString("COLUMN_NAME"));
                    fieldList.add(field);
                });
                index.setUnique(value.stream().anyMatch(v -> "UNIQUE".equals(v.getString("UNIQUENESS"))));
                index.setPrimary(value.stream().anyMatch(v -> ((Integer) v.get("IS_PK")) == 1));
                index.setIndexFields(fieldList);
                tapIndexList.add(index);
            });
            //4、table columns info
            AtomicInteger keyPos = new AtomicInteger(0);
            columnList.stream().filter(col -> table.equals(col.getString("TABLE_NAME")))
                    .forEach(col -> {
                        TapField tapField = new DamengColumn(col).getTapField();
                        tapField.setPos(keyPos.incrementAndGet());
                        tapField.setPrimaryKey(primaryKey.contains(tapField.getName()));
                        tapField.setPrimaryKeyPos(primaryKey.indexOf(tapField.getName()) + 1);
                        tapTable.add(tapField);
                    });
            tapTable.setIndexList(tapIndexList);
            tapTableList.add(tapTable);
        });
        syncSchemaSubmit(tapTableList, consumer);
    }

    @Override
    public ConnectionOptions connectionTest(TapConnectionContext connectionContext, Consumer<TestItem> consumer) {
        damengConfig = new DamengConfig().load(connectionContext.getConnectionConfig());
        ConnectionOptions connectionOptions = ConnectionOptions.create();
        connectionOptions.connectionString(damengConfig.getConnectionString());
        try (
                DamengTest damengTest = new DamengTest(damengConfig, consumer, connectionOptions)
        ) {
            damengTest.testOneByOne();
        }

        return connectionOptions;
    }

    @Override
    public int tableCount(TapConnectionContext connectionContext) {
        return damengContext.queryAllTables(null).size();
    }

    private void streamRead(TapConnectorContext nodeContext, List<String> tableList, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {

        cdcRunner = new DamengCdcRunner(damengContext, nodeContext.getId(), tapLogger).init(
                tableList,
                nodeContext.getTableMap(),
                offsetState,
                recordSize,
                consumer
        );
        cdcRunner.startCdcRunner();

    }

    public long findCurrentScn() throws Throwable {
        DamengOffset damengOffset = new DamengOffset();
        AtomicLong currentScn = new AtomicLong();
        try {
            damengContext.queryWithNext(String.format(DM_PENDING_SCN, damengConfig.getSchema(), damengConfig.getSchema()), resultSet -> {
                if (resultSet.getLong(1) > 0) {
                    tapLogger.warn("Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: {}", resultSet.getLong(1));
                    damengContext.queryWithNext(String.format(DM_PENDING_SCN_HALF_HOUR, damengConfig.getSchema(), damengConfig.getSchema()), rs -> {
                        if (rs.getLong(1) > 0) {
                            damengOffset.setLastScn(rs.getLong(1) - 1);
                        }
                    });
                }
            });
        } catch (Exception e) {
            tapLogger.warn("Query pending transaction failed, maybe no permission, please check if there are any earlier transactions to prevent data loss", e);
        }
        if (EmptyKit.isNull(damengOffset.getLastScn()) || damengOffset.getLastScn() == 0) {
            damengContext.queryWithNext(CHECK_CURRENT_SCN, resultSet -> currentScn.set(resultSet.getLong(1)));
            return currentScn.get();
        }
        return damengOffset.getLastScn();
    }

    public long findScnWithTimestamp(long timestamp) throws Throwable {
        try (
                Connection connection = damengContext.getConnection();
                Statement statement = connection.createStatement();
        ) {
            try (
                    ResultSet resultSet = statement.executeQuery(String.format("select * from v$archived_log where UNIX_TIMESTAMP(first_time)<=%s and UNIX_TIMESTAMP(next_time)>%s", timestamp, timestamp));
            ) {
                if (resultSet.next()) {
                    String logName = resultSet.getString("NAME");
                    statement.execute(String.format(" SYS.dbms_logmnr.add_logfile(logfilename=>'%s',options=>SYS.dbms_logmnr.ADDFILE)", logName));
                    statement.execute("SYS.DBMS_LOGMNR.START_LOGMNR()");
                }
            }
            try (
                    ResultSet resultSet = statement.executeQuery(String.format("select max(scn) from v$logmnr_contents where UNIX_TIMESTAMP(timestamp)<%s", timestamp));
            ) {
                if (resultSet.next()) {
                    return resultSet.getLong(1);
                }
            } finally {
                statement.execute("SYS.DBMS_LOGMNR.END_LOGMNR()");
            }
        } catch (Exception ignored) {
        }
        tapLogger.warn("Can't find scn with timestamp: {}, use current scn", timestamp);
        return findCurrentScn();
    }

    private Object timestampToStreamOffset(TapConnectorContext connectorContext, Long offsetStartTime) throws Throwable {
        DamengOffset damengOffset = new DamengOffset();
        long lsn;
        if (EmptyKit.isNotNull(offsetStartTime)) {
            lsn = findScnWithTimestamp(offsetStartTime / 1000);
        } else {
            lsn = findCurrentScn();
        }
        damengOffset.setLastScn(lsn);
        damengOffset.setPendingScn(lsn);
        return damengOffset;
    }

    protected void fieldDDLHandler(TapConnectorContext tapConnectorContext, TapFieldBaseEvent tapFieldBaseEvent) throws SQLException {
        List<String> sqls = fieldDDLHandlers.handle(tapFieldBaseEvent, tapConnectorContext);
        if (null == sqls) {
            return;
        }
        damengContext.batchExecute(sqls);
    }

    protected List<String> alterFieldAttr(TapFieldBaseEvent tapFieldBaseEvent, TapConnectorContext tapConnectorContext) {
        if (!(tapFieldBaseEvent instanceof TapAlterFieldAttributesEvent)) {
            return null;
        }
        TapAlterFieldAttributesEvent tapAlterFieldAttributesEvent = (TapAlterFieldAttributesEvent) tapFieldBaseEvent;
        return ddlSqlGenerator.alterColumnAttr(damengConfig, tapAlterFieldAttributesEvent);
    }

    protected List<String> dropField(TapFieldBaseEvent tapFieldBaseEvent, TapConnectorContext tapConnectorContext) {
        if (!(tapFieldBaseEvent instanceof TapDropFieldEvent)) {
            return null;
        }
        TapDropFieldEvent tapDropFieldEvent = (TapDropFieldEvent) tapFieldBaseEvent;
        return ddlSqlGenerator.dropColumn(damengConfig, tapDropFieldEvent);
    }

    protected List<String> alterFieldName(TapFieldBaseEvent tapFieldBaseEvent, TapConnectorContext tapConnectorContext) {
        if (!(tapFieldBaseEvent instanceof TapAlterFieldNameEvent)) {
            return null;
        }
        TapAlterFieldNameEvent tapAlterFieldNameEvent = (TapAlterFieldNameEvent) tapFieldBaseEvent;
        return ddlSqlGenerator.alterColumnName(damengConfig, tapAlterFieldNameEvent);
    }

    protected List<String> newField(TapFieldBaseEvent tapFieldBaseEvent, TapConnectorContext tapConnectorContext) {
        if (!(tapFieldBaseEvent instanceof TapNewFieldEvent)) {
            return null;
        }
        TapNewFieldEvent tapNewFieldEvent = (TapNewFieldEvent) tapFieldBaseEvent;
        return ddlSqlGenerator.addColumn(damengConfig, tapNewFieldEvent);
    }

    private void checkConnection(TapConnectionContext connectionContext, List<String> items, Consumer<ConnectionCheckItem> consumer) {
        ConnectionCheckItem testPing = damengTest.testPing();
        consumer.accept(testPing);
        if (testPing.getResult() == ConnectionCheckItem.RESULT_FAILED) {
            return;
        }
        ConnectionCheckItem testConnection = damengTest.testConnection();
        consumer.accept(testConnection);
    }

    @Override
    protected void processDataMap(DataMap dataMap, TapTable tapTable) {
        if (!damengConfig.getOldVersionTimezone()) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                Object value = entry.getValue();
                if (!tapTable.getNameFieldMap().containsKey(entry.getKey())) {
                    continue;
                }
                if (value instanceof DmdbTimestamp) {
                    entry.setValue(((DmdbTimestamp) value).toLocalDateTime().minusHours(((DmdbTimestamp) value).getTimezone() / 60).atZone(ZoneOffset.UTC));
                } else if (value instanceof Timestamp) {
                    entry.setValue(((Timestamp) value).toLocalDateTime().minusHours(damengConfig.getZoneOffsetHour()));
                } else if (value instanceof Date) {
                    entry.setValue(Instant.ofEpochMilli(((Date) value).getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime());
                } else if (value instanceof Time) {
                    entry.setValue(Instant.ofEpochMilli(((Time) value).getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime().minusHours(damengConfig.getZoneOffsetHour()));
                }
            }
        }
    }
}
