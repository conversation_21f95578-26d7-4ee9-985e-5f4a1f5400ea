package io.tapdata.connector.dameng.ddl.ccj;

import io.tapdata.common.ddl.alias.DbDataTypeAlias;
import io.tapdata.common.ddl.ccj.CCJBaseDDLWrapper;
import io.tapdata.kit.StringKit;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.alter.Alter;

public abstract class Dameng<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends CCJBaseDDLWrapper {

    @Override
    protected String getDataTypeFromAlias(String alias) {
        return new DbDataTypeAlias(alias).toDataType();
    }

    @Override
    protected String getTableName(Alter ddl) {
        Table table = ddl.getTable();
        return StringKit.removeHeadTail(table.getName(), ccjddlWrapperConfig.getSplit(), ccjddlWrapperConfig.getCaseSensitive());
    }

    @Override
    protected String getSchemaName(Alter ddl) {
        String schemaName = ddl.getTable().getSchemaName();
        return StringKit.removeHeadTail(schemaName, ccjddlWrapperConfig.getSplit(), ccjddlWrapperConfig.getCaseSensitive());
    }

}
