package io.tapdata.connector.dameng.cdc.logminer.util;

import java.io.IOException;
import java.io.Reader;

public class ByteUtil {

    public static byte[] parseHex(String hexStr) {
        if (!hexStr.contains("0x")) {
            return null;
        }
        hexStr = hexStr.substring(2, hexStr.length());
        int length = hexStr.length() / 2;
        byte[] byteArray = new byte[length];
        for (int i = 0; i < length; i++) {
            String byteStr = hexStr.substring(i * 2, i * 2 + 2);
            byteArray[i] = (byte) Integer.parseInt(byteStr, 16);
        }

        return byteArray;

    }

    public static String getReader(Reader reader){
        StringBuilder stringBuilder = new StringBuilder();
        try {
            // 创建一个字符数组用于缓存数据
            char[] buffer = new char[1024];
            int numberOfCharRead; // 每次调用read()方法读取的字符数
            // 读取数据并存入字符数组，直到读完文件
            while ((numberOfCharRead = reader.read(buffer)) != -1) {
                // 将字符数组转换为字符串输出
                stringBuilder.append(new String(buffer, 0, numberOfCharRead));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            // 确保在结束后关闭reader
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return stringBuilder.toString();
    }
}
