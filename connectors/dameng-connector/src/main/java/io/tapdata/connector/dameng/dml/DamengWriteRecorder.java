package io.tapdata.connector.dameng.dml;

import dm.jdbc.driver.DmdbBlob;
import dm.jdbc.driver.DmdbConnection;
import dm.jdbc.driver.DmdbNClob;
import io.tapdata.common.WriteRecorder;
import io.tapdata.common.dml.MergeWriteRecorder;
import io.tapdata.common.dml.NormalRecordWriter;
import io.tapdata.common.dml.NormalWriteRecorder;
import io.tapdata.connector.dameng.DamengConnector;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.StringKit;
import io.tapdata.pdk.apis.entity.WriteListResult;
import oracle.jdbc.OracleConnection;
import oracle.sql.BLOB;
import oracle.sql.CLOB;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.stream.Collectors;

public class DamengWriteRecorder extends MergeWriteRecorder {

    public static final String DATATYPE_CLOB = "CLOB";
    public static final String DATATYPE_BLOB = "BLOB";
    public DamengWriteRecorder(Connection connection, TapTable tapTable, String schema) {
        super(connection, tapTable, schema);
    }

    @Override
    protected Object filterValue(Object obj, String dataType) throws SQLException {
        if (EmptyKit.isNull(obj)) {
            return null;
        }
        if (obj instanceof String && ((String) obj).length() > 4000 && !DATATYPE_CLOB.equals(dataType)) {
            DmdbNClob clob = DmdbNClob.newInstance((String)obj,connection.unwrap(DmdbConnection.class));
            return clob;
        } else if (DATATYPE_BLOB.equals(dataType) && obj instanceof String) {
            return obj.toString();
        } else if (!DATATYPE_BLOB.equals(dataType) && obj instanceof byte[] && ((byte[]) obj).length > 4000) {
            DmdbBlob blob = DmdbBlob.newInstanceOfLocal(((byte[]) obj),connection.unwrap(DmdbConnection.class));
            return blob;
        } else {
            return obj;
        }
    }
}
