## **Connection Configuration Help**
### **1. DM Installation Instructions**
Please follow the instructions below to ensure that the DM database is successfully added and used in Tapdata. Note: DM real-time synchronization is based on DM Redo Log, so configuration needs to be performed in advance.
### **2. Supported versions**
DM 7.x, 8.x

### **3. Deployment architectures supported by DM**
Single node
### **4. Data types supported by DM**
- CHAR
- CHARACTER
- VARCHAR2
- LONGVARCHAR
- INTEGER
- BIGINT
- NUMBER
- FLOAT
- DOUBLE
- REAL
- BIT
- DECIMAL
- BYTE
- TIME
- TIME WITH TIME ZONE
- DATE
- TIMESTAMP
- TIMESTAMP WITH TIME ZONE
- TIMESTAMP WITH LOCAL TIME ZONE
- CLOB
- NCLOB
- BINARY
- VARBINARY
- LONGVARBINARY
- BLOB
- IMAGE
- TEXT

### **5. Prerequisites**
#### **As a source**

- Enable database transaction logs
  - Use with Log in to the database as a user with DBA privileges
  - Check whether the database is archived and the archive log is enabled
  ``` sh
    SELECT PARA_NAME, PARA_VALUE FROM V$DM_INI WHERE PARA_NAME IN ('ARCH_INI','RLOG_APPEND_LOGIC');
    # ARCH_INI archive log 0 is not enabled 1 is enabled
    # RLOG_APPEND_LOGIC append log
  ```
  - Enable archive log operation:
  ``` sh
  # Open data configuration status
  ALTER DATABASE MOUNT;
  # TYPE=archive type:
  # Archive file storage directory (local/remote), if the local directory pointed to does not exist, it will be automatically created.
  # Remote real-time archive (REALTIME)
  # Remote asynchronous archive (ASYNC)
  # Remote synchronous archive (SYNC)
  # Local archive (LOCAL)
  # MPP remote archive (MARCH)
  # It is recommended to choose LOCAL
  # DEST: address of archive log: #Create archived files on the database server, for example: /opt/dm/archlog
  # FILE_SIZE: size of a single REDO log archive file:
  # Value range (64 MB~2048 MB), default 128 MB
  # SPACE_LIMIT: space limit for REDO log archive:
  # When all local archive files reach the limit,
  # the system automatically deletes the oldest archive file. 0 means no space limit,
  # Value range (1024 MB~4294967294 MB), default is 0.
  # It is recommended to set it to 0 and use the time-based cleanup method to clean up the archive log.
  ALTER DATABASE ADD ARCHIVELOG 'TYPE=LOCAL,DEST=/path/archlog,FILE_SIZE=128,SPACE_LIMIT=0';
  # Enable archive log
  ALTER DATABASE ARCHIVELOG;
  # Open database
  ALTER DATABASE OPEN;
  ```

  - Enable RLOG_APPEND_LOGIC additional log:
  ``` sh
  # 0 Disable
  # 1 If there is a primary key column, only the primary key column information is included when recording update and delete operations. If there is no primary key column, all column information is included.
  # 2 Regardless of whether there is a primary key column, all column information is included when recording update and delete operations.
  # 3 When recording updates, the updated column information and rowid are included. When recording deletes, only the rowid is included.
  # It is recommended to set it to 2.
  ALTER SYSTEM SET 'RLOG_APPEND_LOGIC'=2 MEMORY;
  ```
- Create a user account
    ``` sh
    # Create a user
    CREATE USER <username> IDENTIFIED BY <password>;
    # RESOURCE has object definition permissions (create tables, indexes, views, etc.) in the current mode
    GRANT "RESOURCE" to "username";
    ```
- Assign incremental read permissions to the created user
    ``` sh
    # Authorize to view the database initialization load information record table
    GRANT SELECT ON V$DM_INI TO username;
    # Authorize to view archive log information
    GRANT SELECT ON V$ARCHIVED_LOG TO username;
    # Authorize to view archive log overall information
    GRANT SELECT ON V$RLOG TO username;
    # Authorize to view archive log analysis view
    GRANT SELECT ON V$LOGMNR_CONTENTS TO username;
    # Authorize to view the session view
    GRANT SELECT ON V$SESSIONS TO username;
    # Authorize to view the transaction view
    GRANT SELECT ON V$TRX TO username;
    ```
#### **As a target**
- Create a user account
    ``` sh
    # Create user
    CREATE USER <username> IDENTIFIED BY <password>;
    # Authorize to create index
    GRANT CREATE INDEX TO username；
    # Authorize to create table
    GRANT CREATE TABLE TO username；
    ```
### **6. Extended reading**

- Clean up archive logs
    ``` sh
    # Clean up archives before the set time. It is recommended to keep archive logs for one day
    SF_ARCHIVELOG_DELETE_BEFORE_TIME(time datetime);
    ```