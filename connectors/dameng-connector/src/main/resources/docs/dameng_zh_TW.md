## **連線設定說明**
### **1. DM 安裝說明**
請遵循以下說明以確保在 Tapdata 中成功新增和使用DM資料庫，注意：DM 即時同步基於DM Redo Log，因此需要事先執行配置。
### **2. 支援版本**
DM 7.x,8.x

### **3. DM支援的部署架構**
單節點
### **4. DM支援的資料型別**
- CHAR
- CHARACTER
- VARCHAR2
- LONGVARCHAR
- INTEGER
- BIGINT
- NUMBER
- FLOAT
- DOUBLE
- REAL
- BIT
- DECIMAL
- BYTE
- TIME
- TIME WITH TIME ZONE
- DATE
- TIMESTAMP
- TIMESTAMP WITH TIME ZONE
- TIMESTAMP WITH LOCAL TIME ZONE
- CLOB
- NCLOB
- BINARY
- VARBINARY
- LONGVARBINARY
- BLOB
- IMAGE
- TEXT

### **5. 先決條件**
#### **作為來源**

- 開啟資料庫事務日誌
  - 以具有 DBA 權限的使用者登入資料庫
  - 查看資料庫的是否開啟歸檔以及歸檔日誌
  ``` sh
   SELECT PARA_NAME, PARA_VALUE FROM V$DM_INI WHERE PARA_NAME IN ('ARCH_INI','RLOG_APPEND_LOGIC');
   # ARCH_INI 歸檔日誌 0為未開啟 1為開啟
   # RLOG_APPEND_LOGIC 附加日誌
   ```
- 開啟歸檔日誌操作：
   ``` sh
   # 開啟資料配置狀態
   ALTER DATABASE MOUNT;
   # TYPE=歸檔類型:
   # 歸檔檔案存放目錄(本地/遠端), 如果所指向的本機目錄不存在會自動建立.
   # 遠端即時歸檔(REALTIME)
   # 遠端非同步歸檔(ASYNC)
   # 遠端同步歸檔(SYNC)
   # 本地歸檔(LOCAL)
   # MPP遠端歸檔(MARCH)
   #建議選擇LOCAL
   # DEST:歸檔日誌的位址:#在資料庫伺服器建立歸檔的檔案例如：/opt/dm/archlog
   # FILE_SIZE:單一 REDO 日誌歸檔檔案大小:
   # 取值範圍（64 MB~2048 MB），預設值128 MB
   # SPACE_LIMIT:REDO日誌歸檔空間限制:
   # 當所有本機歸檔檔案達到限制值時，
   # 系統會自動刪除最舊的歸檔檔案。 0 表示無空間限制，
   # 取值範圍（1024 MB~4294967294 MB），缺省為 0。
   # 建議設定為0，利用設定時間的清理方式清理歸檔日誌
   ALTER DATABASE ADD ARCHIVELOG 'TYPE=LOCAL,DEST=/path/archlog,FILE_SIZE=128,SPACE_LIMIT=0';
   # 開啟歸檔日誌
   ALTER DATABASE ARCHIVELOG;
   # 開啟資料庫
   ALTER DATABASE OPEN;
   ```

    - 開啟 RLOG_APPEND_LOGIC 附加日誌:
   ``` sh
   # 0 不啟用
   # 1 若有主鍵列，記錄update和delete操作時只包含主鍵列信息，若沒有主鍵列則包含所有列信息
   # 2 不論是否有主鍵列，記錄update和delete操作時都包含所有列的資訊
   # 3 記錄update時包含更新列的資訊以及rowid，記錄delete時只有rowid
   # 建議設定為2.
   ALTER SYSTEM SET 'RLOG_APPEND_LOGIC'=2 MEMORY;
   ```
- 建立使用者帳號
   ``` sh
   # 建立用戶
   CREATE USER <username> IDENTIFIED BY <password>;
   # RESOURCE具有在目前模式下物件定義權限（建立表格、索引、檢視等）
   GRANT "RESOURCE" to "username";
   ```
- 為建立使用者指派增量讀取權限
   ``` sh
   # 授權查看資料庫初始化載入資訊記錄表
   GRANT SELECT ON V$DM_INI TO username;
   # 授權查看歸檔日誌訊息
   GRANT SELECT ON V$ARCHIVED_LOG TO username;
   # 授權查看歸檔日誌總體信息
   GRANT SELECT ON V$RLOG TO username;
   # 授權查看歸檔日誌分析視圖
   GRANT SELECT ON V$LOGMNR_CONTENTS TO username;
   # 授權查看回話視圖
   GRANT SELECT ON V$SESSIONS TO username;
   # 授權查看事務視圖
   GRANT SELECT ON V$TRX TO username;
   ```
#### **作為目標**
- 建立使用者帳號
   ``` sh
   # 建立用戶
   CREATE USER <username> IDENTIFIED BY <password>;
   # 授權建立索引
   GRANT CREATE INDEX TO username；
   # 授權建立表
   GRANT CREATE TABLE TO username；
   ```
### **6. 延伸閱讀**

- 清理歸檔日誌
   ``` sh
   # 清理設定設定時間之前的歸檔，建議歸檔日誌保留一天
   SF_ARCHIVELOG_DELETE_BEFORE_TIME(time datetime);
   ```