## **连接配置帮助**
### **1. DM 安装说明**
请遵循以下说明以确保在 Tapdata 中成功添加和使用DM数据库，注意：DM 实时同步基于DM Redo Log，因此需要提前执行配置。
### **2. 支持版本**
DM 7.x,8.x

### **3. DM支持的部署架构**
单节点
### **4. DM支持的数据类型**
- CHAR
- CHARACTER
- VARCHAR2
- LONGVARCHAR
- INTEGER
- BIGINT
- NUMBER
- FLOAT
- DOUBLE
- REAL
- BIT
- DECIMAL
- BYTE
- TIME
- TIME WITH TIME ZONE
- DATE
- TIMESTAMP
- TIMESTAMP WITH TIME ZONE
- TIMESTAMP WITH LOCAL TIME ZONE
- CLOB
- NCLOB
- BINARY
- VARBINARY
- LONGVARBINARY
- BLOB
- IMAGE
- TEXT

### **5. 先决条件**
#### **作为源**

- 开启数据库事务日志
   - 以具有 DBA 权限的用户身份登录数据库
   - 查看数据库的是否开启归档以及归档日志
   ``` sh
    SELECT PARA_NAME, PARA_VALUE FROM V$DM_INI WHERE PARA_NAME IN ('ARCH_INI','RLOG_APPEND_LOGIC');
    # ARCH_INI 归档日志 0为未开启 1为开启
    # RLOG_APPEND_LOGIC 附加日志
   ```
   - 开启归档日志操作：
   ``` sh
    # 打开数据配置状态
    ALTER DATABASE MOUNT;
    # TYPE=归档类型: 
      # 归档文件存放目录(本地/远程), 如果所指向的本地目录不存在会自动创建.
      # 远程实时归档(REALTIME)
      # 远程异步归档(ASYNC)
      # 远程同步归档(SYNC)
      # 本地归档(LOCAL)
      # MPP远程归档(MARCH)
      #建议选择LOCAL
    # DEST:归档日志的地址:#在数据库服务器创建归档的文件比如：/opt/dm/archlog
    # FILE_SIZE:单个 REDO 日志归档文件大小:
      # 取值范围（64 MB~2048 MB），缺省128 MB
    # SPACE_LIMIT:REDO日志归档空间限制:
      # 当所有本地归档文件达到限制值时，
      # 系统自动删除最老的归档文件。0 表示无空间限制，
      # 取值范围（1024 MB~4294967294 MB），缺省为 0。
      # 建议设置为0，利用设定时间的清理方式清理归档日志
    ALTER DATABASE ADD ARCHIVELOG 'TYPE=LOCAL,DEST=/path/archlog,FILE_SIZE=128,SPACE_LIMIT=0';
    # 开启归档日志
    ALTER DATABASE ARCHIVELOG;
    # 打开数据库
    ALTER DATABASE OPEN;
    ```
    
    - 开启 RLOG_APPEND_LOGIC 附加日志:
    ``` sh
     # 0 不启用
     # 1 如果有主键列，记录update和delete操作时只包含主键列信息，若没有主键列则包含所有列信息
     # 2 不论是否有主键列，记录update和delete操作时都包含所有列的信息
     # 3 记录update时包含更新列的信息以及rowid，记录delete时只有rowid
     # 建议设置为2.
     ALTER SYSTEM SET 'RLOG_APPEND_LOGIC'=2 MEMORY;
    ```
- 创建用户账号
  ```  sh
   # 创建用户
   CREATE USER <username> IDENTIFIED BY <password>;
   # RESOURCE具有在当前模式下对象定义权限（创建表、索引、视图等）
   GRANT "RESOURCE" to "username";
   ```
- 为创建用户分配增量读取权限
   ``` sh
    # 授权查看数据库初始化加载信息记录表
    GRANT SELECT ON V$DM_INI TO username;
    # 授权查看归档日志信息
    GRANT SELECT ON V$ARCHIVED_LOG TO username;
    # 授权查看归档日志总体信息
    GRANT SELECT ON V$RLOG TO username;
    # 授权查看归档日志分析视图
    GRANT SELECT ON V$LOGMNR_CONTENTS TO username;
    # 授权查看回话视图
    GRANT SELECT ON V$SESSIONS TO username;
    # 授权查看事务视图
    GRANT SELECT ON V$TRX TO username;

  ```
#### **作为目标**
- 创建用户账号
  ``` sh
   # 创建用户
   CREATE USER <username> IDENTIFIED BY <password>;
   # 授权创建索引
   GRANT CREATE INDEX  TO username；
   # 授权创建表
   GRANT CREATE TABLE  TO username；
  ```
### **6. 扩展阅读**

- 清理归档日志
  ``` sh
   # 清理设置设定时间之前的归档，建议归档日志保留一天
   SF_ARCHIVELOG_DELETE_BEFORE_TIME(time datetime);
  ```





