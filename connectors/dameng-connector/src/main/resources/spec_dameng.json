{"properties": {"name": "<PERSON><PERSON>", "icon": "icons/dameng.png", "id": "dameng", "doc": "${doc}", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists", "just_insert"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}], "supportDDL": {"events": ["new_field_event", "alter_field_name_event", "alter_field_attributes_event", "drop_field_event"]}, "connection": {"type": "object", "properties": {"host": {"type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 1, "required": true}, "port": {"type": "string", "title": "${port}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-index": 2, "required": true}, "schema": {"required": true, "type": "string", "title": "<PERSON><PERSON><PERSON>", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_owner", "x-index": 4}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 5, "required": true}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 6}, "addtionalString": {"type": "string", "title": "${addtionalString}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "additionalString", "x-index": 7}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-index": 8, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}, "node": {"type": "object", "properties": {"bigTransactionSize": {"required": true, "type": "string", "title": "${bigTransactionSize}", "default": 10000, "x-index": 8, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 100, "max": 100000}, "x-decorator-props": {"tooltip": "${bigTransactionSizeTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "transactionAliveMinutes": {"required": true, "type": "string", "title": "${transactionAliveMinutes}", "default": 720, "x-index": 9, "x-decorator": "FormItem", "x-component": "InputNumber", "x-decorator-props": {"tooltip": "${transactionAliveMinutesTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}}}}, "messages": {"default": "en_US", "en_US": {"host": "Host", "port": "Port", "database": "database", "user": "user", "password": "password", "addtionalString": "Connection Parameter String", "timezone": "timezone", "doc": "docs/dameng_en_US.md", "schema": "schema", "bigTransactionSize": "Large transaction event boundary", "bigTransactionSizeTooltip": "When the transaction event exceeds this value, it will enter the large transaction logic. If it is set too large, it may affect the memory. After a large transaction, there will be local disk cache. When the task is abnormal, you need to pay attention to disk cleaning", "transactionAliveMinutes": "Uncommitted transaction lifetime (minutes)", "transactionAliveMinutesTooltip": "Transactions that have not been committed for a long time will cause each start/stop task to start mining from this transaction, which will affect performance. Therefore, transactions that have not been committed for a long time will be cleaned up"}, "zh_CN": {"host": "地址", "port": "端口", "database": "数据库", "user": "账号", "password": "密码", "addtionalString": "连接参数", "timezone": "时区", "doc": "docs/dameng_zh_CN.md", "bigTransactionSize": "大事务事件界限", "bigTransactionSizeTooltip": "当事务事件超过该数值会进入大事务逻辑，设置过大有可能会影响内存，大事务后会有本地磁盘缓存，在任务出现异常时需要注意磁盘清理", "transactionAliveMinutes": "未提交事务生命时长（分钟）", "transactionAliveMinutesTooltip": "长时间未提交的事务会导致每次启停任务将从该事务开始挖掘，会影响性能，因此超过该时长未提交的事务将被清理"}, "zh_TW": {"host": "地址", "port": "端口", "database": "數據庫", "user": "賬號", "password": "密碼", "addtionalString": "連接參數", "timezone": "時區", "doc": "docs/dameng_zh_TW.md", "bigTransactionSize": "大事務事件界限", "bigTransactionSizeTooltip": "當事務事件超過該數值會進入大事務邏輯，設定過大有可能會影響記憶體，大事務後會有本地磁片緩存，在任務出現異常時需要注意磁片清理", "transactionAliveMinutes": "未提交事務生命時長（分鐘）", "transactionAliveMinutesTooltip": "長時間未提交的事務會導致每次啟停任務將從該事務開始挖掘，會影響效能，囙此超過該時長未提交的事務將被清理"}}, "dataTypes": {"CHAR[($byte)]": {"to": "TapString", "byte": 32767, "defaultByte": 1, "fixed": true}, "CHARACTER[($byte)]": {"to": "TapString", "byte": 32767, "defaultByte": 1, "fixed": true}, "VARCHAR($byte)": {"to": "TapString", "byte": 32767, "defaultByte": 8188}, "VARCHAR2($byte)": {"to": "TapString", "byte": 32767, "defaultByte": 8188}, "DATE": {"to": "TapDate", "range": ["1000-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd", "priority": 1}, "TIME[($fraction)]": {"range": ["00:00:00", "23:59:59"], "pattern": "HH:mm:ss", "fraction": [0, 6], "defaultFraction": 0, "priority": 1, "to": "TapTime", "withTimeZone": false}, "TIME[($fraction)] with time zone": {"range": ["00:00:00", "23:59:59"], "pattern": "HH:mm:ss", "fraction": [0, 6], "withTimeZone": true, "defaultFraction": 0, "priority": 1, "to": "TapTime"}, "TIMESTAMP[($fraction)]": {"to": "TapDateTime", "range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 6, "withTimeZone": false}, "DATETIME[($fraction)]": {"to": "TapDateTime", "range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 6, "withTimeZone": false}, "DATETIME[($fraction)] WITH TIME ZONE": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 6, "priority": 2, "withTimeZone": true, "to": "TapDateTime"}, "TIMESTAMP[($fraction)] WITH LOCAL TIME ZONE": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "withTimeZone": true, "defaultFraction": 6, "priority": 3, "to": "TapDateTime"}, "BIT": {"bit": 1, "priority": 1, "to": "TapBoolean"}, "NUMBER[($precision,$scale)]": {"precision": [1, 38], "scale": [-84, 127], "fixed": true, "defaultPrecision": 38, "defaultScale": 0, "priority": 1, "to": "TapNumber"}, "NUMERIC[($precision,$scale)]": {"precision": [1, 38], "scale": [-84, 127], "queryOnly": true, "fixed": true, "defaultPrecision": 38, "defaultScale": 0, "priority": 2, "to": "TapNumber"}, "DECIMAL[($precision,$scale)]": {"scale": [-84, 127], "precision": [1, 38], "queryOnly": true, "fixed": true, "preferPrecision": 20, "preferScale": 8, "defaultPrecision": 38, "defaultScale": 0, "priority": 2, "to": "TapNumber"}, "DEC[($precision,$scale)]": {"scale": [-84, 127], "precision": [1, 38], "queryOnly": true, "fixed": true, "preferPrecision": 20, "preferScale": 8, "defaultPrecision": 38, "defaultScale": 0, "priority": 2, "to": "TapNumber"}, "BIGINT": {"to": "TapNumber", "bit": 64, "precision": 19, "value": [-9223372036854775808, 9223372036854775807]}, "INTEGER": {"bit": 32, "priority": 1, "value": [-2147483648, 2147483647], "to": "TapNumber"}, "INT": {"bit": 32, "priority": 1, "value": [-2147483648, 2147483647], "to": "TapNumber"}, "TINYINT": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [-128, 127]}, "BYTE": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [-128, 127]}, "SMALLINT[($zerofill)]": {"to": "TapNumber", "bit": 16, "value": [-32768, 32767], "precision": 5}, "DOUBLE": {"value": ["-1.7976931348623157E+308", "1.7976931348623157E+308"], "precision": [1, 126], "scale": 125, "preferScale": 8, "fixed": false, "defaultPrecision": 126, "priority": 2, "to": "TapNumber"}, "DOUBLE PRECISION": {"priority": 2, "precision": [1, 17], "preferPrecision": 11, "preferScale": 4, "scale": [0, 17], "fixed": false, "to": "TapNumber"}, "REAL": {"value": ["-3.402823466E+38", "3.402823466E+38"], "preferScale": 24, "preferPrecision": 12, "priority": 3, "to": "TapNumber"}, "TEXT": {"to": "TapString", "byte": "99g", "pkEnablement": false}, "LONG": {"to": "TapString", "byte": "100g-1", "pkEnablement": false}, "LONGVARCHAR": {"to": "TapString", "byte": "99g", "pkEnablement": false}, "BINARY[($byte)]": {"to": "TapBinary", "byte": 255, "defaultByte": 1, "fixed": true}, "VARBINARY[($byte)]": {"to": "TapBinary", "byte": 32767, "defaultByte": 8188}, "RAW($byte)": {"queryOnly": true, "to": "TapBinary", "byte": 32767, "defaultByte": 8188}, "IMAGE": {"to": "TapBinary", "byte": "99g", "queryOnly": true}, "LONGVARBINARY": {"to": "TapBinary", "byte": "99g", "queryOnly": true}, "BLOB": {"byte": "99g", "priority": 2, "to": "TapBinary"}, "CLOB": {"byte": "99g", "to": "TapString"}, "BFILE": {"queryOnly": true, "pkEnablement": false, "to": "TapBinary"}}}