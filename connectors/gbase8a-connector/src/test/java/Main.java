import io.tapdata.connector.gbase8a.Gbase8aJdbcContext;
import io.tapdata.connector.gbase8a.config.Gbase8aConfig;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Statement;


public class Main {
    public static void main(String[] args) {
        Gbase8aConfig gbase8aConfig = new Gbase8aConfig();
        gbase8aConfig.setHost("*************");
        gbase8aConfig.setPort(5258);
        gbase8aConfig.setDatabase("jarad");
        gbase8aConfig.setUser("root");
        gbase8aConfig.setPassword("root");
        gbase8aConfig.setDbType("gbase");
        gbase8aConfig.setJdbcDriver("com.gbase.jdbc.Driver");
        try {
            Gbase8aJdbcContext gbase8aJdbcContext = new Gbase8aJdbcContext(gbase8aConfig);
            Connection connection = gbase8aJdbcContext.getConnection();
            Statement statement = connection.createStatement();
//    statement.execute("REPLACE INTO \"jarad\".\"fuck1\" (\"id\", \"birth\") VALUES('123','2022-05-10 00:00:00.000000')");
//    statement.execute("REPLACE INTO \"jarad\".\"fuck1\" (\"id\", \"birth\") VALUES('124','2022-05-10 00:00:00.000000')");
            PreparedStatement preparedStatement = connection.prepareStatement("REPLACE INTO \"jarad\".\"fuck1\" (\"id\", \"birth\") VALUES(?,?)");
            preparedStatement.setObject(1, "123");
            preparedStatement.setObject(2, "2022-05-10 00:00:00.000000");
            preparedStatement.addBatch();
//    preparedStatement.clearParameters();
//    preparedStatement.setObject(1, "124");
//    preparedStatement.setObject(2, "2022-05-10 00:00:00.000000");
//    preparedStatement.addBatch();
            preparedStatement.executeBatch();
            connection.commit();
            connection.close();
            gbase8aJdbcContext.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
