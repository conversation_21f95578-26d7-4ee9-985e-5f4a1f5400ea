package io.tapdata.connector.gbase8a;

import io.tapdata.common.JdbcContext;
import io.tapdata.connector.gbase8a.config.Gbase8aConfig;
import io.tapdata.entity.logger.TapLogger;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.StringKit;

import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public class Gbase8aJdbcContext extends JdbcContext {

    private final static String TAG = Gbase8aJdbcContext.class.getSimpleName();

    public Gbase8aJdbcContext(Gbase8aConfig config) {
        super(config);
    }

    @Override
    public List<DataMap> queryAllTables(List<String> tableNames) {
        TapLogger.debug(TAG, "Query some tables, schema: " + getConfig().getSchema());
        List<DataMap> tableList = TapSimplify.list();
        String tableSql = EmptyKit.isNotEmpty(tableNames) ? "AND table_name IN (" + StringKit.joinString(tableNames, "'", ",") + ")" : "";
        try {
            query(String.format(GBASE_8A_ALL_TABLE, getConfig().getDatabase(), tableSql),
                    resultSet -> tableList.addAll(DbKit.getDataFromResultSet(resultSet)));
        } catch (Throwable e) {
            TapLogger.error(TAG, "Execute queryAllTables failed, error: " + e.getMessage(), e);
        }
        return tableList;
    }

    @Override
    public List<DataMap> queryAllColumns(List<String> tableNames) {
        TapLogger.debug(TAG, "Query columns of some tables, schema: " + getConfig().getSchema());
        List<DataMap> columnList = TapSimplify.list();
        String tableSql = EmptyKit.isNotEmpty(tableNames) ? "AND table_name IN (" + StringKit.joinString(tableNames, "'", ",") + ")" : "";
        try {
            query(String.format(GBASE_8A_ALL_COLUMN, getConfig().getDatabase(), tableSql),
                    resultSet -> columnList.addAll(DbKit.getDataFromResultSet(resultSet)));
        } catch (Throwable e) {
            TapLogger.error(TAG, "Execute queryAllColumns failed, error: " + e.getMessage(), e);
        }
        return columnList;
    }

    @Override
    public List<DataMap> queryAllIndexes(List<String> tableNames) {
        TapLogger.debug(TAG, "Query indexes of some tables, schema: " + getConfig().getSchema());
        List<DataMap> indexList = TapSimplify.list();
        String tableSql = EmptyKit.isNotEmpty(tableNames) ? "AND table_name IN (" + StringKit.joinString(tableNames, "'", ",") + ")" : "";
        try {
            query(String.format(GBASE_8A_ALL_PRIMARY, getConfig().getDatabase(), tableSql),
                    resultSet -> indexList.addAll(DbKit.getDataFromResultSet(resultSet)));
        } catch (Throwable e) {
            TapLogger.error(TAG, "Execute queryAllIndexes failed, error: " + e.getMessage(), e);
        }
        return indexList;
    }

    public boolean isTableNameCaseSensitive() throws SQLException {
        AtomicBoolean atomicBoolean = new AtomicBoolean();
        queryWithNext("show variables like 'lower_case_table_names'",
                result -> atomicBoolean.set(result.getInt("Value") == 0));
        return atomicBoolean.get();
    }

    private static final String GBASE_8A_ALL_TABLE = "SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '%s' AND TABLE_TYPE='BASE TABLE' %s ORDER BY TABLE_NAME";
    private static final String GBASE_8A_ALL_COLUMN = "SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '%s' %s ORDER BY TABLE_NAME, ORDINAL_POSITION";
    private static final String GBASE_8A_ALL_PRIMARY = "select * from INFORMATION_SCHEMA.STATISTICS where TABLE_SCHEMA = '%s' %s AND INDEX_TYPE='PRIMARY' ORDER BY TABLE_NAME, SEQ_IN_INDEX";
}
