package io.tapdata.connector.gbase8a;

import io.tapdata.common.dml.NormalWriteRecorder;
import io.tapdata.common.dml.WritePolicyEnum;
import io.tapdata.entity.schema.TapTable;

import java.sql.Connection;
import java.util.Map;
import java.util.stream.Collectors;

public class Gbase8aWriteRecorder extends NormalWriteRecorder {

    public Gbase8aWriteRecorder(Connection connection, TapTable tapTable, String schema) {
        super(connection, tapTable, schema);
    }

    protected void largeInsert(Map<String, Object> after) {
        largeSqlValues.add("(" + allColumn.stream().map(k -> object2String(after.get(k))).collect(Collectors.joining(", ")) + ")");
    }

    protected String getLargeInsertSql() {
        if (WritePolicyEnum.UPDATE_ON_EXISTS == insertPolicy) {
            return "INSERT INTO " + escapeChar + schema + escapeChar + "." + escapeChar + tapTable.getId() + escapeChar + " ("
                    + allColumn.stream().map(k -> escapeChar + k + escapeChar).collect(Collectors.joining(", ")) + ") VALUES "
                    + String.join(", ", largeSqlValues) + " ON DUPLICATE KEY UPDATE "
                    + allColumn.stream().map(k -> escapeChar + k + escapeChar + "=values(" + escapeChar + k + escapeChar + ")").collect(Collectors.joining(", "));
        } else if (WritePolicyEnum.IGNORE_ON_EXISTS == insertPolicy) {
            return "INSERT IGNORE INTO " + escapeChar + schema + escapeChar + "." + escapeChar + tapTable.getId() + escapeChar + " ("
                    + allColumn.stream().map(k -> escapeChar + k + escapeChar).collect(Collectors.joining(", ")) + ") VALUES "
                    + String.join(", ", largeSqlValues);
        } else {
            return "INSERT INTO " + escapeChar + schema + escapeChar + "." + escapeChar + tapTable.getId() + escapeChar + " ("
                    + allColumn.stream().map(k -> escapeChar + k + escapeChar).collect(Collectors.joining(", ")) + ") VALUES "
                    + String.join(", ", largeSqlValues);
        }
    }

}
