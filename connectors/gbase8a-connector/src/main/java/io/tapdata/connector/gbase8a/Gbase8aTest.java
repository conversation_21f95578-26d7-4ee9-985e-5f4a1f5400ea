package io.tapdata.connector.gbase8a;

import io.tapdata.common.CommonDbTest;
import io.tapdata.connector.gbase8a.config.Gbase8aConfig;
import io.tapdata.pdk.apis.entity.TestItem;

import java.util.Arrays;
import java.util.function.Consumer;

import static io.tapdata.base.ConnectorBase.testItem;

public class Gbase8aTest extends CommonDbTest {

    public Gbase8aTest(Gbase8aConfig gbase8aConfig, Consumer<TestItem> consumer) {
        super(gbase8aConfig, consumer);
        jdbcContext = new Gbase8aJdbcContext(gbase8aConfig);
    }

    protected Boolean testWritePrivilege() {
        try {
            if (jdbcContext.queryAllTables(Arrays.asList(TEST_WRITE_TABLE, TEST_WRITE_TABLE.toUpperCase())).size() > 0) {
                jdbcContext.execute(String.format(TEST_DROP_TABLE, TEST_WRITE_TABLE));
            }
            //create
            jdbcContext.execute(String.format(TEST_CREATE_TABLE, TEST_WRITE_TABLE));
            //insert
            jdbcContext.execute(String.format(TEST_WRITE_RECORD, TEST_WRITE_TABLE));
            //update
            jdbcContext.execute(String.format(TEST_UPDATE_RECORD, TEST_WRITE_TABLE));
            //delete
            jdbcContext.execute(String.format(TEST_DELETE_RECORD, TEST_WRITE_TABLE));
            //drop
            jdbcContext.execute(String.format(TEST_DROP_TABLE, TEST_WRITE_TABLE));
            consumer.accept(testItem(TestItem.ITEM_WRITE, TestItem.RESULT_SUCCESSFULLY, TEST_WRITE_SUCCESS));
        } catch (Exception e) {
            consumer.accept(testItem(TestItem.ITEM_WRITE, TestItem.RESULT_FAILED, e.getMessage()));
        }
        return true;
    }

}
