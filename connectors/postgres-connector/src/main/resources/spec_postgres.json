{"properties": {"name": "PostgreSQL", "icon": "icons/postgres.png", "doc": "${doc}", "id": "postgres", "tags": ["Database", "ssl", "doubleActive", "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "NullsLast"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists", "just_insert"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists", "log_on_nonexists"]}, {"id": "dml_delete_policy", "alternatives": ["ignore_on_nonexists", "log_on_nonexists"]}, {"id": "dml_check_policy", "alternatives": ["ignore_all_check", "default_check"]}, {"id": "api_server_supported"}, {"id": "source_support_exactly_once"}, {"id": "batch_read_hash_split"}, {"id": "source_support_partition"}], "connection": {"type": "object", "properties": {"host": {"required": true, "type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-decorator-props": {"tooltip": "${hostTip}"}, "x-index": 10}, "port": {"required": true, "type": "string", "title": "${port}", "default": 5432, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-decorator-props": {"tooltip": "${portTip}"}, "x-index": 20}, "database": {"required": true, "type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-decorator-props": {"tooltip": "${databaseTip}"}, "x-index": 30}, "schema": {"required": true, "type": "string", "title": "${schema}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_owner", "x-decorator-props": {"tooltip": "${schemaTip}"}, "x-index": 40}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 60}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 70}, "logPluginName": {"required": true, "type": "string", "title": "${logPluginName}", "default": "pgoutput", "x-decorator": "FormItem", "x-component": "Select", "apiServerKey": "logPlugin", "x-decorator-props": {"tooltip": "${logPluginNameTip}"}, "x-index": 80, "enum": [{"label": "DECODERBUFS", "value": "decoderbufs"}, {"label": "WAL2JSON", "value": "wal2j<PERSON>"}, {"label": "WAL2JSONRDS", "value": "wal2json_rds"}, {"label": "WAL2JSONSTREMING", "value": "wal2json_streaming"}, {"label": "WAL2JSONRDSSTREAMING", "value": "wal2json_rds_streaming"}, {"label": "PGOUTPUT", "value": "pgoutput"}, {"label": "WALMINER", "value": "walminer"}]}, "OPTIONAL_FIELDS": {"type": "void", "properties": {"extParams": {"type": "string", "title": "${extParams}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "additionalString", "x-decorator-props": {"tooltip": "${extParamsTip}"}, "x-index": 50}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-decorator-props": {"tooltip": "${timezoneTip}"}, "x-index": 90, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}}}, "node": {"type": "object", "properties": {"closeNotNull": {"type": "boolean", "title": "${closeNotNull}", "default": false, "x-index": 1, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${closeNotNullTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "tableOwner": {"type": "string", "title": "${tableOwner}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 3, "x-decorator-props": {"tooltip": "${tableOwnerTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "createAutoInc": {"type": "boolean", "title": "${createAutoInc}", "default": false, "x-index": 5, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${createAutoIncTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "autoIncJumpValue": {"required": true, "type": "string", "title": "${autoIncJumpValue}", "default": 1000000, "x-index": 6, "x-decorator": "FormItem", "x-component": "InputNumber", "x-reactions": [{"dependencies": ["$inputs", ".createAutoInc"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}, "autoIncCacheValue": {"required": true, "type": "string", "title": "${autoIncCacheValue}", "default": 100, "x-index": 7, "x-decorator": "FormItem", "x-component": "InputNumber", "x-reactions": [{"dependencies": ["$inputs", ".createAutoInc"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}, "applyDefault": {"type": "boolean", "title": "${applyDefault}", "default": false, "x-index": 8, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${applyDefaultTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "hashSplit": {"type": "boolean", "title": "${hashSplit}", "default": false, "x-index": 10, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${hashSplitTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "maxSplit": {"required": true, "type": "string", "title": "${maxSplit}", "default": 20, "x-index": 12, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 2, "max": 10000}, "x-reactions": [{"dependencies": ["$inputs", ".hashSplit"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}, "batchReadThreadSize": {"required": true, "type": "string", "title": "${batchReadThreadSize}", "default": 4, "x-index": 13, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 1, "max": 16}, "x-reactions": [{"dependencies": ["$inputs", ".hashSplit"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}, "partitionRoot": {"type": "boolean", "title": "${partitionRoot}", "default": false, "x-index": 15, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${partitionRootTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "maximumQueueSize": {"type": "string", "title": "${maximumQueueSize}", "default": 8000, "x-index": 16, "x-decorator": "FormItem", "x-component": "InputNumber", "x-decorator-props": {"tooltip": "${maximumQueueSizeTip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "enableFileInput": {"type": "boolean", "title": "${enableFileInput}", "default": false, "x-index": 17, "x-decorator": "FormItem", "x-component": "Switch", "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}}}}, "messages": {"default": "en_US", "en_US": {"doc": "docs/postgres_en_US.md", "host": "Host", "hostTip": "The address of the database, it can be an IP address or a domain name, for example: *************", "port": "Port", "portTip": "The port number of the database, the default port of Postgres is 5432", "database": "database", "databaseTip": "database name, you can query all databases through pg_database", "schema": "schema", "schemaTip": "schema name, you can query all schemas through pg_namespace", "extParams": "extParams", "extParamsTip": "Additional connection parameters in URI, you can write according to personalized scenarios \nfor example: ?binaryTransfer=false&forceBinary=false&reWriteBatchedInserts=true", "user": "user", "password": "password", "logPluginName": "logPluginName", "logPluginNameTip": "The higher version PG comes with the PGOUTPUT plugin, but the before data in the logs is not complete. \nThe author recommends using WAL2JSON, and the installation method can be found in the MD documentation", "timezone": "timezone", "timezoneTip": "Specify the time zone, otherwise no time zone processing will be done", "closeNotNull": "Ignore <PERSON>", "closeNotNullTooltip": "When the switch is turned on, non empty restrictions are discarded", "tableOwner": "Specify table owner", "tableOwnerTooltip": "You can specify the table owner, if not specified, it defaults to the current user. If the permission is insufficient, use a user with sufficient permissions to execute: grant <specified owner> to <data synchronization user>", "createAutoInc": "Synchronize auto-increment columns", "createAutoIncTooltip": "Only PG 10 and above are supported. When the switch is turned on, the auto-increment attribute is synchronized when entering the incremental", "autoIncJumpValue": "Auto-increment key jump value", "autoIncCacheValue": "Auto-increment key cache value", "applyDefault": "Apply default value", "applyDefaultTooltip": "When the switch is turned on, the default value is applied to the target. If there are unadapted functions or expressions, it may cause an error", "hashSplit": "Hash split", "hashSplitTooltip": "When the switch is turned on, it can be sharded according to the hash value, suitable for large table full-stage sharded synchronization", "maxSplit": "Maximum number of splits", "batchReadThreadSize": "Batch read thread size", "partitionRoot": "Partition table CDC root table", "partitionRootTooltip": "Only postgres-v13 and above and log plugin pgoutput are supported. When the switch is turned on, the partition table CDC event only perceives the root table, and when the switch is turned off, only perceives each sub-table", "maximumQueueSize": "Maximum queue size", "maximumQueueSizeTip": "The queue size for reading incremental data in PostgreSQL. If the downstream synchronization is slow or individual records in the table are too large, please lower this setting", "enableFileInput": "Enable file input"}, "zh_CN": {"doc": "docs/postgres_zh_CN.md", "host": "地址", "hostTip": "数据库的地址，它可以是IP地址或者域名，例如：*************", "port": "端口", "portTip": "数据库的端口号，Postgres默认端口5432", "database": "数据库", "databaseTip": "数据库名称，可以通过查询pg_database查询所有数据库", "schema": "模型", "schemaTip": "每个数据库下有多个模型架构，可以通过查询pg_namespace查询所有模型", "extParams": "额外参数", "extParamsTip": "URI额外的连接参数，可以根据个性化场景书写，如：?binaryTransfer=false&forceBinary=false&reWriteBatchedInserts=true", "user": "账号", "password": "密码", "logPluginName": "日志插件", "logPluginNameTip": "高版本PG自带PGOUTPUT插件，但是日志的before数据并不完整，笔者推荐使用WAL2JSON，安装方式见MD文档", "timezone": "时区", "timezoneTip": "指定时区，否则不做时区处理", "closeNotNull": "忽略NotNull", "closeNotNullTooltip": "开关打开时会将非空限制丢弃", "tableOwner": "指定表所有者", "tableOwnerTooltip": "可以指定表所有者，不指定则默认为当前用户，如果权限不足请使用足够权限的用户执行：grant <被指定owner> to <数据同步用户> ", "createAutoInc": "同步自增列", "createAutoIncTooltip": "仅支持PG10版本以上，开关打开时会在进入增量时同步自增属性", "autoIncJumpValue": "自增键跳跃值", "autoIncCacheValue": "自增键缓存值", "applyDefault": "应用默认值", "applyDefaultTooltip": "开关打开时会将默认值应用到目标，如果有未适配的函数或表达式，可能会导致报错", "hashSplit": "哈希分片", "hashSplitTooltip": "开关打开时，可以根据哈希值进行分片，适用于大表全量阶段分片同步", "maxSplit": "最大分片数", "batchReadThreadSize": "批量读取线程数", "partitionRoot": "分区表CDC根表", "partitionRootTooltip": "仅postgres-v13以上且日志插件pgoutput时才支持，开关打开时，分区表CDC事件仅感知根表，关闭时，仅感知各子表", "maximumQueueSize": "最大队列大小", "maximumQueueSizeTip": "PostgreSQL读取增量数据队列大小，如果下游同步较慢或表的单条数据过大，请调低此配置", "enableFileInput": "启用文件输入"}, "zh_TW": {"doc": "docs/postgres_zh_TW.md", "host": "地址", "hostTip": "數據庫的地址，它可以是IP地址或者域名，例如：*************", "port": "端口", "portTip": "數據庫的端口號，Postgres默認端口5432", "database": "數據庫", "databaseTip": "數據庫名稱，可以通過查詢pg_database查詢所有數據庫", "schema": "模型", "schemaTip": "每個數據庫下有多個模型架構，可以通過查詢pg_namespace查詢所有模型", "extParams": "額外參數", "extParamsTip": "URI額外的連接參數，可以根據個性化場景書寫，如：?binaryTransfer=false&forceBinary=false&reWriteBatchedInserts=true", "user": "賬號", "password": "密碼", "logPluginName": "日誌插件", "logPluginNameTip": "高版本PG自帶PGOUTPUT插件，但是日誌的before數據並不完整，筆者推薦使用WAL2JSON，安裝方式見MD文檔", "timezone": "時區", "timezoneTip": "指定時區，否則不做時區處理", "closeNotNull": "忽略NotNull", "closeNotNullTooltip": "開關打開時會將非空限制丟棄", "tableOwner": "指定表所有者", "tableOwnerTooltip": "可以指定表所有者，不指定則默認為當前用戶，如果權限不足請使用足夠權限的用戶執行：grant <被指定owner> to <數據同步用戶> ", "createAutoInc": "同步自增列", "createAutoIncTooltip": "僅支持PG10版本以上，開關打開時會在進入增量時同步自增屬性", "autoIncJumpValue": "自增鍵跳躍值", "autoIncCacheValue": "自增鍵緩存值", "applyDefault": "應用默認值", "applyDefaultTooltip": "開關打開時會將默認值應用到目標，如果有未適配的函數或表達式，可能會導致報錯", "hashSplit": "哈希分片", "hashSplitTooltip": "開關打開時，可以根據哈希值進行分片，適用於大表全量階段分片同步", "maxSplit": "最大分片數", "batchReadThreadSize": "批量讀取線程數", "partitionRoot": "分區表CDC根表", "partitionRootTooltip": "僅postgres-v13以上且日誌插件pgoutput時才支持，開關打開時，分區表CDC事件僅感知根表，關閉時，僅感知各子表", "maximumQueueSize": "最大隊列大小", "maximumQueueSizeTip": "PostgreSQL讀取增量數據隊列大小。如果下游同步較慢或表的單條數據過大，請調低此配置", "enableFileInput": "啟用文件輸入"}}, "dataTypes": {"smallint": {"bit": 16, "priority": 3, "value": [-32768, 32767], "to": "TapNumber"}, "integer": {"bit": 32, "priority": 1, "value": [-2147483648, 2147483647], "to": "TapNumber"}, "bigint": {"bit": 64, "priority": 3, "value": [-9223372036854775808, 9223372036854775807], "to": "TapNumber"}, "numeric[($precision,$scale)]": {"precision": [1, 1000], "scale": [0, 1000], "fixed": true, "preferPrecision": 20, "preferScale": 8, "priority": 1, "to": "TapNumber"}, "real": {"to": "TapNumber", "bit": 4, "defaultPrecision": 24, "defaultScale": 8, "fixed": false}, "double precision": {"priority": 2, "precision": [1, 17], "preferPrecision": 11, "preferScale": 4, "scale": [0, 17], "fixed": false, "to": "TapNumber"}, "character[($byte)]": {"byte": 10485760, "priority": 2, "preferByte": 255, "fixed": true, "to": "TapString"}, "character varying[($byte)]": {"byte": 10485760, "priority": 1, "defaultByte": 10485760, "preferByte": 2000, "to": "TapString"}, "text": {"byte": "4g", "priority": 2, "to": "TapString"}, "bytea": {"byte": "4g", "priority": 1, "to": "TapBinary"}, "bit[($byte)]": {"byte": 64, "defaultByte": 1, "priority": 3, "fixed": true, "queryOnly": true, "to": "TapBinary"}, "bit varying[($byte)]": {"byte": 64, "defaultByte": 64, "priority": 1, "queryOnly": true, "to": "TapBinary"}, "boolean": {"bit": 1, "priority": 1, "to": "TapBoolean"}, "date": {"range": ["0001-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd", "priority": 1, "to": "TapDate"}, "interval": {"preferByte": 100, "queryOnly": true, "to": "TapString"}, "timestamp[($fraction)] without time zone": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "withTimeZone": false, "defaultFraction": 6, "priority": 1, "to": "TapDateTime"}, "timestamp[($fraction)] with time zone": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "withTimeZone": true, "defaultFraction": 6, "priority": 2, "to": "TapDateTime"}, "time[($fraction)] without time zone": {"range": ["00:00:00", "23:59:59"], "pattern": "HH:mm:ss", "fraction": [0, 6], "withTimeZone": false, "defaultFraction": 6, "priority": 1, "to": "TapTime"}, "time[($fraction)] with time zone": {"range": ["00:00:00", "23:59:59"], "pattern": "HH:mm:ss", "fraction": [0, 6], "withTimeZone": true, "defaultFraction": 6, "priority": 2, "to": "TapTime"}, "$nothing array": {"queryOnly": true, "to": "TapArray"}, "point": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "line": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "lseg": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "box": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "path": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "polygon": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "circle": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "cidr": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "inet": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "macaddr": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "uuid": {"to": "TapString", "preferByte": 256, "queryOnly": true}, "xml": {"to": "TapXml"}, "json": {"to": "<PERSON><PERSON><PERSON><PERSON>"}, "tsvector": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "tsquery": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "oid": {"to": "TapNumber", "bit": 64, "queryOnly": true}, "regproc": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "regprocedure": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "regoper": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "regoperator": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "regclass": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "regtype": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "regconfig": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "regdictionary": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "jsonb": {"to": "TapString", "queryOnly": true}, "money": {"to": "<PERSON><PERSON><PERSON><PERSON>", "defaultPrecision": 19, "defaultScale": 4, "fixed": true}}}