<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.tapdata</groupId>
        <artifactId>connectors</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>tencent-db-mysql-connector</artifactId>
    <name>TencentDB MySQL Connector</name>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <developers>
        <developer>
            <name>jackin</name>
            <email><EMAIL></email>
        </developer>
    </developers>
    <modules>

    </modules>

    <properties>
        <java.version>8</java.version>
        <tapdata.pdk.api.version>2.0.0-SNAPSHOT</tapdata.pdk.api.version>
        <pdk-error-code.version>1.0-SNAPSHOT</pdk-error-code.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>tapdata-pdk-api</artifactId>
            <version>${tapdata.pdk.api.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>tapdata-api</artifactId>
            <version>${tapdata.pdk.api.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>binary-log-client-td-sql</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>mysql-core-td-sql</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>mysql-binlog-connector-java</artifactId>
                    <groupId>com.zendesk</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>mysql-connector</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>io.tapdata</groupId>
                    <artifactId>mysql-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>pdk-error-code</artifactId>
            <version>${pdk-error-code.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <finalName>${connector.file.name}</finalName>
                    <transformers>
                        <transformer
                                implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                            <manifestEntries>
                                <Implementation-Title>${project.artifactId}</Implementation-Title>
                                <Implementation-Version>${project.version}</Implementation-Version>
                                <Implementation-Vendor>${project.groupId}</Implementation-Vendor>

                                <Build-OS>${os.name} ${os.version}</Build-OS>
                                <Build-Java>Java ${java.version}</Build-Java>
                                <PDK-Runner-Version>${tapdata.pdk.runner.version}</PDK-Runner-Version>
                                <PDK-API-Version>${tapdata.pdk.api.version}</PDK-API-Version>
                                <Tapdata-API-Version>${tapdata.pdk.api.version}</Tapdata-API-Version>
                                <Version>${project.version}</Version>
                                <Authentication>Beta</Authentication>
                                <PayMode>${pay.mode}</PayMode>
                                <Git-Build-Time>${git.build.time}</Git-Build-Time>
                                <Git-Branch>${git.branch}</Git-Branch>
                                <Git-Commit-Id>${git.commit.id}</Git-Commit-Id>
                                <Git-Build-User-Name>${git.build.user.name}</Git-Build-User-Name>
                                <Git-Build-User-Email>${git.build.user.email}</Git-Build-User-Email>
                            </manifestEntries>
                        </transformer>
                    </transformers>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <filters>
                                <filter>
                                    <artifact>**:**</artifact>
                                    <excludes>
                                        <exclude>**/org/apache/log4j/**</exclude>
                                        <exclude>**/org/apache/logging/log4j/**</exclude>
                                        <exclude>**/org/slf4j/**</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>

            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.3</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>validate</phase>
                    </execution>
                </executions>
                <configuration>
                    <verbose>true</verbose>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <failOnNoGitDirectory>true</failOnNoGitDirectory>
                    <injectAllReactorProjects>true</injectAllReactorProjects>
                    <dotGitDirectory>${project.basedir}/../../.git</dotGitDirectory>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-resource-one</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>

                        <configuration>
                            <outputDirectory>../dist</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${basedir}/target/</directory>
                                    <includes>
                                        <include>${connector.file.name}.jar</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>4.2.1</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-source-plugin</artifactId>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <id>attach-sources</id>-->
            <!--                        <goals>-->
            <!--                            <goal>jar</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/overwrite/</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
