package io.tapdata.connector.tencent.db.core;

import io.tapdata.common.ddl.DDLFactory;
import io.tapdata.connector.mysql.MysqlReader;
import io.tapdata.connector.mysql.TDMysqlJdbcContextV2;
import io.tapdata.connector.mysql.entity.MysqlBinlogPosition;
import io.tapdata.connector.mysql.entity.MysqlStreamEvent;
import io.tapdata.connector.mysql.entity.TDMysqlStreamOffset;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.ddl.TapDDLEvent;
import io.tapdata.entity.event.ddl.TapDDLUnknownEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.JsonParser;
import io.tapdata.entity.utils.cache.KVMap;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.connect.data.Schema;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.source.SourceRecord;

import java.util.*;
import java.util.function.Supplier;

import static io.tapdata.base.ConnectorBase.entry;
import static io.tapdata.base.ConnectorBase.map;

/**
 * <AUTHOR>
 * @description TDSqlReaderTD create by Gavin
 * @create 2023/4/18 14:35
 **/
public class TDSqlReaderTD extends MysqlReader {
    private final String partitionSetId;
    public static final String SERVER_NAME_KEY = "PARTITION_KEY_NAME_CONFIG";
    TapConnectorContext tapConnectorContext;

    public TDSqlReaderTD(TDMysqlJdbcContextV2 mysqlJdbcContext, Log tapLogger, Supplier<Boolean> isAlive) {
        super(mysqlJdbcContext, tapLogger, isAlive);
        this.partitionSetId = mysqlJdbcContext.getPartitionSetId();
    }

    @Override
    protected synchronized void initDebeziumServerName(TapConnectorContext tapConnectorContext) {
        this.tapConnectorContext = tapConnectorContext;
        super.initDebeziumServerName(tapConnectorContext);
        if (null == this.partitionSetId) {
            super.initDebeziumServerName(tapConnectorContext);
            return;
        }
        serverName = UUID.randomUUID().toString().toLowerCase();
        KVMap<Object> stateMap = tapConnectorContext.getStateMap();
        Object serverNameFromStateMap = stateMap.get(SERVER_NAME_KEY);
        if (serverNameFromStateMap instanceof Map) {
            Map<String, Object> serverNameFromServer = (Map<String, Object>) serverNameFromStateMap;
            Object configMap = serverNameFromServer.get(partitionSetId);
            if (null == configMap) {
                serverNameFromServer.put(partitionSetId, map(
                        entry(MysqlReader.SERVER_NAME_KEY, serverName),
                        entry(MysqlReader.FIRST_TIME_KEY, true)));
            } else {
                Map<String, Object> objectMap = (Map<String, Object>) configMap;
                this.serverName = (String) objectMap.get(MysqlReader.SERVER_NAME_KEY);
                objectMap.put(MysqlReader.FIRST_TIME_KEY, false);
            }
            stateMap.put(SERVER_NAME_KEY, serverNameFromServer);
        } else {
            stateMap.put(SERVER_NAME_KEY, map(
                    entry(partitionSetId, map(
                            entry(MysqlReader.SERVER_NAME_KEY, serverName),
                            entry(MysqlReader.FIRST_TIME_KEY, true))
                    )
            ));
        }
    }

    @Override
    protected TDMysqlStreamOffset binlogPosition2MysqlStreamOffset(MysqlBinlogPosition offset, JsonParser jsonParser) throws Throwable {
        String serverId = mysqlJdbcContext.getServerId();
        Map<String, Object> partitionMap = new HashMap<>();
        partitionMap.put("server", serverName);
        Map<String, Object> offsetMap = new HashMap<>();
        offsetMap.put("file", offset.getFilename());
        offsetMap.put("pos", offset.getPosition());
        offsetMap.put("server_id", serverId);
        TDMysqlStreamOffset TDMysqlStreamOffset = new TDMysqlStreamOffset();
        TDMysqlStreamOffset.setOffsetMap(serverName, new HashMap<String, String>() {{
            put(jsonParser.toJson(partitionMap), jsonParser.toJson(offsetMap));
        }});
        TDMysqlStreamOffset.setName(serverName);
        TDMysqlStreamOffset.setOffset(new HashMap<String, String>() {{
            put(jsonParser.toJson(partitionMap), jsonParser.toJson(offsetMap));
        }});
        return TDMysqlStreamOffset;
    }

    @Override
    protected void sourceRecordConsumer(SourceRecord record) {
        if (null != throwableAtomicReference.get()) {
            throw new RuntimeException(throwableAtomicReference.get());
        }
        if (null == record || null == record.value()) return;
        Schema valueSchema = record.valueSchema();
        List<MysqlStreamEvent> mysqlStreamEvents = new ArrayList<>();
        if (null != valueSchema.field("op")) {
            MysqlStreamEvent mysqlStreamEvent = wrapDML(record);
            Optional.ofNullable(mysqlStreamEvent).ifPresent(mysqlStreamEvents::add);
        } else if (null != valueSchema.field("ddl")) {
            mysqlStreamEvents = wrapDDL(record);
        } else if ("io.debezium.connector.common.Heartbeat".equals(valueSchema.name())) {
            Optional.ofNullable((Struct) record.value())
                    .map(value -> value.getInt64("ts_ms"))
                    .map(TapSimplify::heartbeatEvent)
                    .map(heartbeatEvent -> new MysqlStreamEvent(heartbeatEvent, getMysqlBinlogPosition(record), partitionSetId))
                    .ifPresent(mysqlStreamEvents::add);
        }
        if (CollectionUtils.isNotEmpty(mysqlStreamEvents)) {
            KVReadOnlyMap<TapTable> tableMap = tapConnectorContext.getTableMap();
            for (MysqlStreamEvent mysqlStreamEvent : mysqlStreamEvents) {
                TapEvent tapEvent = mysqlStreamEvent.getTapEvent();
                if (tapEvent instanceof TapInsertRecordEvent) {
                    Map<String, Object> after = ((TapInsertRecordEvent) tapEvent).getAfter();
                    TapTable tapTable = tableMap.get(((TapInsertRecordEvent) tapEvent).getTableId());
                    LinkedHashMap<String, TapField> nameFieldMap = tapTable.getNameFieldMap();
                    nameFieldMap.entrySet().stream().filter(ent -> {
                        TapField value = ent.getValue();
                        return null != value.getDataType() && "YEAR".equals(value.getDataType().toUpperCase(Locale.ROOT));
                    }).forEach(entry -> {
                        Object o = after.get(entry.getKey());
                        if (o instanceof Integer) {
                            after.put(entry.getKey(), "" + o);
                        }
                    });
                } else if (tapEvent instanceof TapUpdateRecordEvent) {
                    Map<String, Object> after = ((TapUpdateRecordEvent) tapEvent).getAfter();
                    TapTable tapTable = tableMap.get(((TapUpdateRecordEvent) tapEvent).getTableId());
                    LinkedHashMap<String, TapField> nameFieldMap = tapTable.getNameFieldMap();
                    nameFieldMap.entrySet().stream().filter(ent -> {
                        TapField value = ent.getValue();
                        return null != value.getDataType() && "YEAR".equals(value.getDataType().toUpperCase(Locale.ROOT));
                    }).forEach(entry -> {
                        Object o = after.get(entry.getKey());
                        if (o instanceof Integer) {
                            after.put(entry.getKey(), "" + o);
                        }
                    });
                }
                enqueue(new MysqlStreamEvent(tapEvent, mysqlStreamEvent.getMysqlBinlogPosition(), partitionSetId));
            }
        }
    }

    protected List<MysqlStreamEvent> wrapDDL(SourceRecord record) {
        List<MysqlStreamEvent> mysqlStreamEvents = new ArrayList<>();
        Object value = record.value();
        if (!(value instanceof Struct)) {
            return null;
        }
        Struct structValue = (Struct) value;
        Struct source = structValue.getStruct("source");
        Long eventTime = source.getInt64("ts_ms");
        String ddlStr = structValue.getString(SOURCE_RECORD_DDL_KEY);
        MysqlBinlogPosition mysqlBinlogPosition = getMysqlBinlogPosition(record);
        if (StringUtils.isNotBlank(ddlStr)) {
            try {
                DDLFactory.ddlToTapDDLEvent(
                        ddlParserType,
                        ddlStr,
                        DDL_WRAPPER_CONFIG,
                        tapTableMap,
                        tapDDLEvent -> {
                            MysqlStreamEvent mysqlStreamEvent = new MysqlStreamEvent(tapDDLEvent, mysqlBinlogPosition, partitionSetId);
                            tapDDLEvent.setTime(System.currentTimeMillis());
                            tapDDLEvent.setReferenceTime(eventTime);
                            tapDDLEvent.setOriginDDL(ddlStr);
                            tapDDLEvent.setExactlyOnceId(getExactlyOnceId(record));
                            mysqlStreamEvents.add(mysqlStreamEvent);
                            tapLogger.info("Read DDL: " + ddlStr + ", about to be packaged as some event(s)");
                        }
                );
            } catch (Throwable e) {
                TapDDLEvent tapDDLEvent = new TapDDLUnknownEvent();
                MysqlStreamEvent mysqlStreamEvent = new MysqlStreamEvent(tapDDLEvent, mysqlBinlogPosition, partitionSetId);
                tapDDLEvent.setTime(System.currentTimeMillis());
                tapDDLEvent.setReferenceTime(eventTime);
                tapDDLEvent.setOriginDDL(ddlStr);
                tapDDLEvent.setExactlyOnceId(getExactlyOnceId(record));
                mysqlStreamEvents.add(mysqlStreamEvent);
//                throw new RuntimeException("Handle ddl failed: " + ddlStr + ", error: " + e.getMessage(), e);
            }
        }
        return mysqlStreamEvents;
    }

    protected MysqlStreamEvent wrapOffsetEvent(TapEvent tapEvent, SourceRecord sourceRecord) {
        MysqlBinlogPosition mysqlBinlogPosition = getMysqlBinlogPosition(sourceRecord);
        return new MysqlStreamEvent(tapEvent, mysqlBinlogPosition, partitionSetId);
    }

    protected MysqlBinlogPosition getMysqlBinlogPosition(SourceRecord record) {
        MysqlBinlogPosition mysqlBinlogPosition = new MysqlBinlogPosition();
        mysqlBinlogPosition.setFilename((String) record.sourceOffset().get("file"));
        mysqlBinlogPosition.setPosition((long) record.sourceOffset().get("pos"));
        mysqlBinlogPosition.setGtidSet((String) record.sourceOffset().get("gtids"));
        return mysqlBinlogPosition;
    }
}
