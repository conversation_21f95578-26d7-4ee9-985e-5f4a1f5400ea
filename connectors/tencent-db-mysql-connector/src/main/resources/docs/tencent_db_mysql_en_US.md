## **Help**

### **1.  distributed database instance of TD-SQL MySQL version**

Please follow the instructions below to ensure successful addition and use of the distributed database TD SQL MySQL version database in Tapdata.

### **2. Support**
Database instance of TD-SQL MySQL version 5.0、5.1、5.5、5.6、5.7、8.x

### **3. As First（As Source）**
#### **3.1 Open Binlog**
- The binlog of the distributed database TD-SQL MySQL version must be enabled in order for Tap<PERSON> to complete synchronization work properly.
- Cascade deletion (CASCADE DELETE), which is a type of deletion generated by the database, is not supported as it is not recorded in the binlog.
Modify `$MYSQL_HOME/mysql.cnf `. such as:
```
server_id         = 223344
log_bin           = mysql-bin
expire_logs_days  = 1
binlog_format     = row
binlog_row_image  = full
```
Configuration explanation :<br>
server-id: Each server and replication client in the distributed database TD-SQL MySQL version must be unique<br>
binlog_format: Must be set to row or ROW<br>
binlog_row_image: Must be set to full<br>
expire_logs_days: The number of days to retain binary log files, which will be automatically deleted upon expiration<br>
log_bin: The basic name of the binlog sequence file<br>

#### **3.2 Restart the distributed database TD-SQL MySQL version**

```
/etc/inint.d/mysqld restart
```
erify that binlog is enabled, please execute the following command in the MySQL shell
```
show variables like 'binlog_format';
```
In the output result, format value should be 'ROW'

Verify binlog_ row_ Is the value of the image parameter full
```
show variables like 'binlog_row_image';
```
In the output result, binlog_ row_ The image value should be 'FULL'

#### **3.3 Create a distributed database TD-SQL MySQL version account**
After MySQL 8, the encryption method for passwords is different. Please pay attention to using the corresponding version and setting the password, otherwise it may result in the inability to perform incremental synchronization
##### **3.3.1 5.x Version**
```
create user 'username'@'localhost' identified by 'password';
```
##### **3.3.2 8.x Version**
```
// Create account
create user 'username'@'localhost' identified with mysql_native_password by 'password';
// Modify password
alter user 'username'@'localhost' identified with mysql_native_password by 'password';

```

#### **3.4 Authorize the Tapdata account**
Assign select permission to a database
```
GRANT SELECT, SHOW VIEW, CREATE ROUTINE, LOCK TABLES ON <DATABASE_NAME>.<TABLE_NAME> TO 'tapdata' IDENTIFIED BY 'password';
```
For global permissions
```
GRANT RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'tapdata' IDENTIFIED BY 'password';
```
#### **3.5  Constraint Description **
```
 When synchronizing from the distributed database TD-SQL MySQL version to other heterogeneous databases, if there is a table cascading setting in the source distributed database TD-SQL MySQL version, data updates and deletions caused by this cascading trigger will not be transmitted to the target. If it is necessary to build cascading processing capabilities on the target end, this type of data synchronization can be achieved through triggers and other means depending on the target situation. 