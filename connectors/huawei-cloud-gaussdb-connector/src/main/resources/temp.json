{"properties": {"name": "HuaWei'Cloud GaussDB", "icon": "icons/gauss.png", "doc": "${doc}", "id": "huawei-gauss-db", "authentication": "Beta", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}, {"id": "api_server_supported"}], "connection": {"type": "object", "properties": {"host": {"required": true, "type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 10}, "port": {"required": true, "type": "string", "title": "${port}", "default": 8000, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-index": 20}, "database": {"required": true, "type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 30}, "schema": {"required": true, "type": "string", "title": "${schema}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_owner", "x-index": 40}, "extParams": {"type": "string", "title": "${extParams}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "additionalString", "x-index": 50}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 60}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 70}, "haHost": {"required": true, "type": "string", "title": "${haHost}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 75}, "haPort": {"required": true, "type": "string", "default": 8001, "title": "${haPort}", "x-decorator": "FormItem", "x-component": "InputNumber", "x-index": 80}, "logPluginName": {"required": true, "type": "string", "title": "${logPluginName}", "default": "mppdb_decoding", "x-decorator": "FormItem", "x-component": "Select", "apiServerKey": "logPlugin", "x-index": 90, "enum": [{"label": "mppdb_decoding", "value": "mppdb_decoding"}]}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-index": 100, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}, "node": {"type": "object", "properties": {}}}, "messages": {"default": "en_US", "en_US": {"doc": "docs/gauss_db_en_US.md", "host": "Host", "port": "Port", "database": "Database", "schema": "<PERSON><PERSON><PERSON>", "extParams": "extParams", "user": "User", "password": "Password", "haHost": "haHost", "haPort": "haPort", "logPluginName": "logPluginName", "timezone": "timezone"}, "zh_CN": {"doc": "docs/gauss_db_zh_CN.md", "host": "Host", "port": "Port", "database": "Database", "schema": "<PERSON><PERSON><PERSON>", "extParams": "extParams", "user": "User", "password": "Password", "haHost": "haHost", "haPort": "haPort", "logPluginName": "logPluginName", "timezone": "timezone"}, "zh_TW": {"doc": "docs/gauss_db_zh_TW.md", "host": "Host", "port": "Port", "database": "Database", "schema": "<PERSON><PERSON><PERSON>", "extParams": "extParams", "user": "User", "password": "Password", "haHost": "haHost", "haPort": "haPort", "logPluginName": "logPluginName", "timezone": "timezone"}}, "dataTypes": {"char[($byte)]": {"to": "TapString", "byte": "10m", "defaultByte": 1, "byteRatio": 1, "fixed": true}, "character[($byte)]": {"to": "TapString", "byte": "10m", "defaultByte": 1, "byteRatio": 1, "fixed": true, "queryOnly": true}, "nchar[($byte)]": {"to": "TapString", "byte": "10m", "defaultByte": 1, "byteRatio": 1, "fixed": false, "queryOnly": true}, "varchar($byte)": {"to": "TapString", "byte": "10m", "defaultByte": 1, "byteRatio": 3, "fixed": false}, "character varying($byte)": {"to": "TapString", "byte": "10m", "defaultByte": 1, "byteRatio": 3, "fixed": false, "queryOnly": true}, "varchar2($byte)": {"to": "TapString", "byte": "10m", "defaultByte": 1, "byteRatio": 1, "fixed": false, "queryOnly": true}, "nvarchar2($byte)": {"to": "TapString", "byte": "10m", "defaultByte": 1, "byteRatio": 3, "fixed": false, "queryOnly": true}, "text": {"to": "TapString", "byte": 1073741823, "fixed": false}, "clob": {"to": "TapString", "byte": 35184372088831, "fixed": false}, "name": {"to": "TapString", "byte": 64, "fixed": false, "queryOnly": true}, "raw": {"to": "TapBinary", "byte": 1073733621}, "bytea": {"to": "TapBinary", "byte": 1073733621, "queryOnly": true}, "byteawithoutorderwithequalcol": {"to": "TapBinary", "byte": 1073741771, "queryOnly": true}, "byteawithoutordercol": {"to": "TapBinary", "byte": 1073741771, "queryOnly": true}, "_byteawithoutorderwithequalcol": {"to": "TapBinary", "byte": 1073741771, "queryOnly": true}, "_byteawithoutordercol": {"to": "TapBinary", "byte": 1073741771, "queryOnly": true}, "smallserial": {"to": "TapNumber", "bit": 8, "precision": 2, "value": [-32768, 32767]}, "serial": {"to": "TapNumber", "bit": 14, "precision": 4, "value": [-2147483648, 2147483647]}, "bigserial": {"to": "TapNumber", "bit": 32, "precision": 8, "value": [-9223372036854775808, 9223372036854775807]}, "tinyint": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [0, 255], "unsigned": "unsigned"}, "smallint": {"to": "TapNumber", "bit": 16, "value": [-32768, 32767], "precision": 5}, "integer": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [-2147483648, 2147483647]}, "binary_integer": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [-2147483648, 2147483647], "queryOnly": true}, "bigint": {"to": "TapNumber", "bit": 64, "precision": 19, "value": [-9223372036854775808, 9223372036854775807]}, "int16": {"to": "TapNumber", "bit": 128, "precision": 39, "value": [-170141183460469231731687303715884105728, 170141183460469231731687303715884105727]}, "numeric[($precision[,$scale])]": {"to": "TapNumber", "precision": [1, 1000], "scale": [0, 1000], "defaultPrecision": 147445, "defaultScale": 16383}, "decimal[($precision[,$scale])]": {"to": "TapNumber", "precision": [1, 1000], "scale": [0, 1000], "defaultPrecision": 147445, "defaultScale": 16383, "fixed": true, "queryOnly": true}, "number[($precision[,$scale])]": {"to": "TapNumber", "precision": [1, 1000], "scale": [0, 1000], "defaultPrecision": 147445, "defaultScale": 16383, "fixed": true, "queryOnly": true}, "largeserial": {"to": "TapNumber", "bit": 32, "precision": 147445, "scale": 16383}, "real": {"to": "TapNumber", "name": "REAL", "precision": [1, 30], "scale": [0, 30], "value": ["-3.402E+38", "3.402E+38"], "fixed": false}, "float4": {"to": "TapNumber", "name": "FLOAT4", "precision": [1, 30], "scale": [0, 30], "value": ["-3.402E+38", "3.402E+38"], "fixed": false}, "double": {"to": "TapNumber", "name": "DOUBLE", "precision": [1, 30], "scale": [0, 30], "value": ["-1.79E+308", "1.79E+308"], "fixed": false}, "precision": {"to": "TapNumber", "name": "PRECISION", "precision": [1, 30], "scale": [0, 30], "value": ["-1.79E+308", "1.79E+308"], "fixed": false}, "float8": {"to": "TapNumber", "name": "FLOAT8", "precision": [1, 30], "scale": [0, 30], "value": ["-1.79E+308", "1.79E+308"], "fixed": false}, "binary_double": {"to": "TapNumber", "name": "BINARY_DOUBLE", "precision": [1, 30], "scale": [0, 30], "value": ["-1.79E+308", "1.79E+308"], "fixed": false}, "float[($precision)]": {"to": "TapNumber", "precision": [1, 53], "fixed": false}, "dec[($precision[,$scale])]": {"to": "TapNumber", "precision": [1, 1000], "$scale": [0, 1000], "defaultPrecision": 147445, "defaultScale": 16383, "fixed": false}, "money": {"to": "TapNumber", "bit": 32, "name": "money", "precision": 17, "scale": 2, "value": [-9.223372036854776e+16, 9.223372036854776e+16]}, "boolean": {"to": "TapBoolean", "bit": 4, "name": "BOOLEAN", "value": [true, false, null]}, "date": {"to": "TapDate", "range": ["1000-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd"}, "TIME [($fraction)] [WITHOUT TIME ZONE]": {"to": "TapTime", "byte": 8, "fraction": [0, 6], "defaultFraction": 0, "range": ["-838:59:59", "838:59:59"], "pattern": "HH:mm:ss"}, "TIME [($fraction)] [WITH TIME ZONE]": {"to": "TapTime", "byte": 16, "fraction": [0, 6], "defaultFraction": 0, "range": ["-838:59:59", "838:59:59"], "pattern": "HH:mm:ss"}, "INTERVAL DAY ($l) TO SECOND ($fraction)": {"to": "TapDateTime", "byte": 16, "range": ["0:00:00:00", "6:23:59:59"], "pattern": "dd:HH:mm:ss", "l": [0, 6], "fraction": [0, 6], "defaultFraction": 0, "queryOnly": true}, "NTERVAL [$FIELDS] [($fraction)]": {"to": "TapNumber", "byte": 16, "$FIELDS": ["YEAR", "MONTH", "DAY", "HOUR", "MINUTE", "SECOND", "DAY TO HOUR", "DAY TO MINUTE", "DAY TO SECOND", "HOUR TO MINUTE", "HOUR TO SECOND", "MINUTE TO SECOND"], "fraction": [0, 6], "defaultFraction": 0, "queryOnly": true}, "SMALLDATETIME": {"to": "TapDateTime", "byte": 8, "range": ["1000-01-01 00:00", "9999-12-31 23:59"], "pattern": "yyyy-MM-dd HH:mm", "defaultFraction": 0, "withTimeZone": false, "queryOnly": true}, "TIMESTAMP[($fraction)]": {"to": "TapDateTime", "byte": 8, "range": ["1970-01-01 00:00:01", "2038-01-19 03:14:07"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 6, "withTimeZone": false, "queryOnly": true, "fixed": false}, "TIMESTAMP[($fraction)] WITHOUT TIME ZONE": {"to": "TapDateTime", "byte": 8, "range": ["1970-01-01 00:00:01", "2038-01-19 03:14:07"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 6, "withTimeZone": false, "fixed": false}, "TIMESTAMP[($fraction)] WITH TIME ZONE": {"to": "TapDateTime", "byte": 8, "range": ["1970-01-01 00:00:01", "2038-01-19 03:14:07"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 6, "withTimeZone": true, "fixed": false}, "enum($enums)": {"name": "enum", "to": "TapString", "queryOnly": true, "byte": 16383}, "set($sets)": {"name": "set", "to": "TapString", "queryOnly": true, "byte": 16383}, "point": {"to": "TapBinary", "byte": 16, "queryOnly": true}, "lseg": {"to": "TapBinary", "byte": 32, "queryOnly": true}, "box": {"to": "TapBinary", "byte": 32, "queryOnly": true}, "path": {"to": "TapBinary", "queryOnly": true}, "polygon": {"to": "TapBinary", "queryOnly": true}, "circle": {"to": "TapBinary", "byte": 24, "queryOnly": true}, "tsvector": {"to": "TapBinary", "queryOnly": true}, "tsquery": {"to": "TapBinary", "queryOnly": true}, "cidr": {"to": "TapString", "byte": 7, "queryOnly": true}, "inet": {"to": "TapString", "byte": 7, "queryOnly": true}, "macaddr": {"to": "TapString", "byte": 6, "queryOnly": true}, "bit($fraction)": {"to": "TapBinary", "fraction": [0, 9223372036854775807], "queryOnly": true}, "bit varying($fraction)": {"to": "TapBinary", "fraction": [0, 9223372036854775807], "queryOnly": true}, "uuid": {"to": "TapString", "preferByte": 256, "queryOnly": true}, "json": {"to": "TapMap", "byte": "4g", "pkEnablement": false}, "jsonb": {"to": "TapMap", "byte": "4g", "pkEnablement": false, "queryOnly": true}, "hll": {"to": "TapString", "byte": "16k", "queryOnly": true}}}