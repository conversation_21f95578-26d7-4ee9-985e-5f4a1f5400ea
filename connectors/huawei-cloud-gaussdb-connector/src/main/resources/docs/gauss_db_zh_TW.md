## **連接配寘幫助**
### 資料庫版本
 華為雲GaussDB主備2.8（企業版）
 
    - 支持自建GaussDB版本：505.1
    
    - postgres內核版本9.2
    
### **1. 必要的檢查**
1. 開啟邏輯複製

- 邏輯日誌現時從DN中抽取，如果進行邏輯複製，應使用SSL連接，囙此需要保證相應DN上的GUC參數ssl設定為on

2. 設定GUC參數

- 設定GUC參數wal_ level為logical

- 設定GUC參數max_ replication_ slots >= 每個節點所需的（物理流複製槽數+備份槽數+邏輯複製槽數）
    資料庫預設值為20，如需設定，建議參攷值設定為使用此連接作為源的的任務任務數+1
    
3. 使用CDC前需要在各DN節點的 pg_hba.conf 中配寘你的用戶機器（當前部署Agent的機器）：
```text
    # 前提條件：添加JDBC用戶機器IP到資料庫白名單裏，在pg_ hba.conf添加以下內容，然後重啓資料庫即可：
    # 假設JDBC用戶IP為***********
    host all all ***********/32 sha256
    host replication all ***********/32 sha256
```
配寘完後需重啓資料庫
    
### 資料來源參數
1. 資料庫IP
2. 埠
3. 資料庫名稱
4. Schema名稱
5. 資料庫登入用戶名
6. 資料庫登入密碼
7. 邏輯複製IP，主DN的IP
8. 邏輯複製埠，通常是主DN的埠+1，即默認為8001
9. 日誌挿件，默認使用mppdb_decoding
10. 時區
    
    
### 關於CDC邏輯複製
1. 不支持DDL語句解碼，在執行特定的DDL語句（如普通錶truncate或分區表exchange）時，可能造成解碼資料丟失。
2. 不支持列存、數據頁複製的解碼。
3. 單條元組大小不超過1GB，考慮解碼結果可能大於插入數據，囙此建議單條元組大小不超過500MB
4. GaussDB支持解碼的資料類型為：
```text
    INTEGER、BIGINT、SMALLINT、TINYINT、SERIAL、SMALLSERIAL、BIGSERIAL、
    FLOAT、DOUBLE PRECISION、
    DATE、TIME[WITHOUT TIME ZONE]、TIMESTAMP[WITHOUT TIME ZONE]、
    CHAR(n)、VARCHAR(n)、TEXT。
```
5. 不支持interval partition錶複製。
6. 不支持全域臨時表。
7. 在事務中執行DDL語句後，該DDL語句與之後的語句不會被解碼。
8. 為解析某個astore錶的UPDATE和DELETE語句，需為此錶配寘REPLICA IDENITY内容，在此錶無主鍵時需要配寘為FULL


### 異常處理
1. 增量啟動失敗
    - 複製槽連接失敗，相關錯誤如下：
      ```text
      com.huawei.opengauss.jdbc.util.PSQLException: 
        [192.168.*.*:5***5/1.9*.1*2.1*2:8001] 
        FATAL: no pg_hba.conf entry for replication connection from host "1*.1**.2*5.1*0",
        user "r**t", SSL off
      ```
      
      錯誤原因：訪問機器不在DN節點的白名單清單
      
      解決方法：
        在各DN節點的pg_ hba.conf中配寘你的用戶機器（當前部署Agent的機器）：
        ```text
        # 添加JDBC用戶機器IP到資料庫白名單裏，在pg_ hba.conf添加以下內容
        # 假設JDBC用戶IP為***********
          host all all ***********/32 sha256
          host replication all ***********/32 sha256
        # 然後重啓資料庫即可
        ```
