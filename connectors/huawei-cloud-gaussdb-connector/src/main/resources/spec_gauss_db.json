{"properties": {"name": "HuaWei'Cloud GaussDB", "icon": "icons/gauss.png", "doc": "${doc}", "id": "huawei-gauss-db", "tags": ["Database", "ssl"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}, {"id": "api_server_supported"}], "connection": {"type": "object", "properties": {"host": {"required": true, "type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 10}, "port": {"required": true, "type": "string", "title": "${port}", "default": 8000, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-index": 20}, "database": {"required": true, "type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 30}, "schema": {"required": true, "type": "string", "title": "${schema}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_owner", "x-index": 40}, "extParams": {"type": "string", "title": "${extParams}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "additionalString", "x-index": 50}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 60}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 70}, "haHost": {"default": "", "type": "string", "title": "${haHost}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 75}, "haPort": {"type": "string", "default": 8001, "title": "${haPort}", "x-decorator": "FormItem", "x-component": "InputNumber", "x-index": 80}, "logPluginName": {"required": true, "type": "string", "title": "${logPluginName}", "default": "mppdb_decoding", "x-decorator": "FormItem", "x-component": "Select", "apiServerKey": "logPlugin", "x-index": 90, "enum": [{"label": "mppdb_decoding", "value": "mppdb_decoding"}]}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-index": 100, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}, "node": {"type": "object", "properties": {"flushLsn": {"required": true, "type": "string", "default": 10, "title": "${flushLsn}", "x-decorator": "FormItem", "x-component": "InputNumber", "x-index": 10}}}}, "messages": {"default": "en_US", "en_US": {"doc": "docs/gauss_db_en_US.md", "host": "Host", "port": "Port", "database": "Database", "schema": "<PERSON><PERSON><PERSON>", "extParams": "JDBC Connection params", "user": "User", "password": "Password", "haHost": "Logic replicate IP", "haPort": "Logic replicate Port", "logPluginName": "Log plugin", "timezone": "Timezone", "flushLsn": "Log advancement interval"}, "zh_CN": {"doc": "docs/gauss_db_zh_CN.md", "host": "数据库IP", "port": "端口", "database": "数据库名称", "schema": "Schema名称", "extParams": "JDBC 连接参数", "user": "数据库登录用户名", "password": "数据库登录密码", "haHost": "逻辑复制IP", "haPort": "逻辑复制端口", "logPluginName": "日志插件", "timezone": "时区", "flushLsn": "日志推进间隔"}, "zh_TW": {"doc": "docs/gauss_db_zh_TW.md", "host": "資料庫IP", "port": "埠", "database": "資料庫名稱", "schema": "Schema名稱", "extParams": "JDBC連接參數", "user": "資料庫登入用戶名", "password": "資料庫登入密碼", "haHost": "邏輯複製IP", "haPort": "邏輯複製埠", "logPluginName": "日誌挿件", "timezone": "時區", "flushLsn": "日誌推進間隔"}}, "dataTypes": {"character[($byte)]": {"to": "TapString", "byte": "10m", "defaultByte": 1, "byteRatio": 1, "fixed": true}, "character varying[($byte)]": {"to": "TapString", "byte": "10m", "defaultByte": 1, "byteRatio": 3, "fixed": false}, "nvarchar2($byte)": {"to": "TapString", "byte": "10m", "defaultByte": 1, "byteRatio": 3, "fixed": false, "queryOnly": true}, "text": {"to": "TapString", "byte": 1073741823, "fixed": false}, "clob": {"to": "TapString", "byte": 35184372088831, "fixed": false}, "blob": {"to": "TapBinary", "byte": 35184372088831, "fixed": false}, "name": {"to": "TapString", "byte": 64, "fixed": false, "queryOnly": true}, "raw": {"to": "TapString", "byte": 1073733621}, "bytea": {"to": "TapBinary", "byte": 1073733621, "queryOnly": true}, "byteawithoutorderwithequalcol": {"to": "TapBinary", "byte": 1073741771, "queryOnly": true}, "byteawithoutordercol": {"to": "TapBinary", "byte": 1073741771, "queryOnly": true}, "_byteawithoutorderwithequalcol": {"to": "TapBinary", "byte": 1073741771, "queryOnly": true}, "_byteawithoutordercol": {"to": "TapBinary", "byte": 1073741771, "queryOnly": true}, "tinyint": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [0, 255], "unsigned": "unsigned"}, "smallint": {"to": "TapNumber", "bit": 16, "value": [-32768, 32767], "precision": 5}, "integer": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [-2147483648, 2147483647]}, "bigint": {"to": "TapNumber", "bit": 64, "precision": 19, "value": [-9223372036854775808, 9223372036854775807]}, "numeric[($precision,$scale)]": {"to": "TapNumber", "fixed": true, "precision": [1, 1000], "scale": [0, 1000], "preferPrecision": 20, "preferScale": 8}, "real": {"to": "TapNumber", "bit": 4, "defaultPrecision": 24, "defaultScale": 8, "fixed": false}, "double precision": {"priority": 2, "precision": [1, 17], "preferPrecision": 11, "preferScale": 4, "scale": [0, 17], "fixed": false, "to": "TapNumber"}, "money": {"to": "TapNumber", "bit": 32, "name": "money", "precision": 17, "scale": 2, "value": [-9.223372036854776e+16, 9.223372036854776e+16]}, "boolean": {"to": "TapBoolean", "bit": 4, "name": "BOOLEAN", "value": [true, false, null]}, "time[($fraction)] without time zone": {"to": "TapTime", "byte": 8, "fraction": [0, 6], "defaultFraction": 6, "range": ["-838:59:59", "838:59:59"], "pattern": "HH:mm:ss", "withTimeZone": false}, "time[($fraction)] with time zone": {"to": "TapTime", "byte": 16, "fraction": [0, 6], "defaultFraction": 6, "range": ["-838:59:59", "838:59:59"], "pattern": "HH:mm:ss", "withTimeZone": true}, "INTERVAL DAY ($l) TO SECOND ($fraction)": {"to": "TapDateTime", "byte": 16, "range": ["0:00:00:00", "6:23:59:59"], "pattern": "dd:HH:mm:ss", "l": [0, 6], "fraction": [0, 6], "defaultFraction": 0, "queryOnly": true}, "NTERVAL [$FIELDS] [($fraction)]": {"to": "TapNumber", "byte": 16, "$FIELDS": ["YEAR", "MONTH", "DAY", "HOUR", "MINUTE", "SECOND", "DAY TO HOUR", "DAY TO MINUTE", "DAY TO SECOND", "HOUR TO MINUTE", "HOUR TO SECOND", "MINUTE TO SECOND"], "fraction": [0, 6], "defaultFraction": 0, "queryOnly": true}, "smalldatetime": {"to": "TapDateTime", "byte": 8, "range": ["1000-01-01 00:00", "9999-12-31 23:59"], "pattern": "yyyy-MM-dd HH:mm", "defaultFraction": 0, "withTimeZone": false, "queryOnly": true}, "timestamp[($fraction)] without time zone": {"to": "TapDateTime", "byte": 8, "range": ["1970-01-01 00:00:01", "2038-01-19 03:14:07"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 6, "withTimeZone": false, "fixed": false}, "timestamp[($fraction)] with time zone": {"to": "TapDateTime", "byte": 8, "range": ["1970-01-01 00:00:01", "2038-01-19 03:14:07"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 6, "withTimeZone": true, "fixed": false}, "point": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "line": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "lseg": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "box": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "path": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "polygon": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "circle": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "tsvector": {"to": "TapBinary", "queryOnly": true}, "tsquery": {"to": "TapBinary", "queryOnly": true}, "cidr": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "inet": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "macaddr": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "bit(1)": {"to": "TapBoolean", "fixed": true, "bit": 1}, "bit[($byte)]": {"byte": 64, "defaultByte": 1, "priority": 3, "fixed": true, "queryOnly": true, "to": "TapBinary"}, "bit varying[($byte)]": {"byte": 64, "defaultByte": 64, "priority": 1, "queryOnly": true, "to": "TapBinary"}, "uuid": {"to": "TapString", "preferByte": 256, "queryOnly": true}, "json": {"to": "TapMap", "byte": "4g", "pkEnablement": false}, "jsonb": {"to": "TapMap", "byte": "4g", "pkEnablement": false, "queryOnly": true}, "hll": {"to": "TapString", "byte": "16k", "queryOnly": true}, "xml": {"to": "TapString", "byte": "1g", "queryOnly": true}, "xmltype": {"to": "TapString", "byte": "1g", "queryOnly": true}, "hash16": {"to": "TapString", "byte": 16, "queryOnly": true}, "hash32": {"to": "TapString", "byte": 32, "queryOnly": true}, "aclitem": {"to": "TapString", "queryOnly": true}}}