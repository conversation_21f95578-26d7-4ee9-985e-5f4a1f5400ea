package io.tapdata.connector.informix.cdc.logminer;

import com.informix.jdbc.IfxObject;
import com.informix.jdbcx.IfxDataSource;
import com.informix.stream.api.IfmxStreamRecord;
import com.informix.stream.cdc.IfxCDCEngine;
import com.informix.stream.cdc.records.IfxCDCBeginTransactionRecord;
import com.informix.stream.cdc.records.IfxCDCCommitTransactionRecord;
import com.informix.stream.cdc.records.IfxCDCOperationRecord;
import io.tapdata.common.cdc.ILogMiner;
import io.tapdata.common.cdc.NormalLogMiner;
import io.tapdata.common.cdc.NormalRedo;
import io.tapdata.common.cdc.NormalTransaction;
import io.tapdata.connector.informix.InformixJdbcContext;
import io.tapdata.connector.informix.cdc.InformixOffset;
import io.tapdata.connector.informix.config.InformixConfig;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.control.HeartbeatEvent;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.StringKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import org.springframework.util.Assert;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static com.informix.stream.api.IfmxStreamRecordType.AFTER_UPDATE;

public class InformixNewLogMiner extends NormalLogMiner implements ILogMiner {

    protected final InformixJdbcContext informixJdbcContext;
    protected final InformixConfig informixConfig;
    protected InformixOffset informixOffset;
    private IfxCDCEngine engine;
    private final Map<Integer, String> labelTableMap = new HashMap<>();
    private final Map<Integer, String> labelSchemaMap = new HashMap<>();
    private Long lastEventTimestamp = 0L;

    public InformixNewLogMiner(InformixJdbcContext informixJdbcContext, String connectorId, Log tapLogger) throws SQLException {
        this.informixJdbcContext = informixJdbcContext;
        this.informixConfig = (InformixConfig) informixJdbcContext.getConfig();
        this.connectorId = connectorId;
        this.tapLogger = tapLogger;
        this.setLargeTransactionUpperLimit(10000);
//        ddlParserType = DDLParserType.ORACLE_CCJ_SQL_PARSER;
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        informixOffset = (InformixOffset) offsetState;
    }

    @Override
    public void startMiner() throws Throwable {
        isRunning.set(true);
        initRedoLogQueueAndThread();
        InformixConfig cdcConfig = (InformixConfig) informixConfig.copy();
        cdcConfig.setDatabase("syscdcv1");
        IfxDataSource ds = new IfxDataSource(cdcConfig.getDatabaseUrl() + "user=" + cdcConfig.getUser() + ";password=" + cdcConfig.getPassword() + ";");
        IfxCDCEngine.Builder builder = new IfxCDCEngine.Builder(ds);
        String tableFormat = "\"%s\":\"%s\".\"%s\"";
        tableList.forEach(table -> builder.watchTable(String.format(tableFormat, informixConfig.getDatabase(), informixConfig.getSchema(), table),
                tableMap.get(table).getNameFieldMap().keySet().stream().map(t -> "\"" + t + "\"").toArray(String[]::new)));
        builder.timeout(60);
        builder.buffer(102400000);
        builder.sequenceId(informixOffset.getPendingScn());
        engine = builder.build();
        engine.setTapLogger(tapLogger);
        engine.setFetchSize(1);
        builder.getWatchedTables().forEach(table -> {
            labelTableMap.put(table.getLabel(), StringKit.removeHeadTail(table.getTableName(), "\"", null));
            labelSchemaMap.put(table.getLabel(), StringKit.removeHeadTail(table.getNamespace(), "\"", null));
        });
        engine.init();
        List<IfmxStreamRecord> records;
        NormalRedo normalRedo = null;
        while (isRunning.get() && EmptyKit.isNotEmpty(records = engine.getRecords())) {
            if (EmptyKit.isNotNull(threadException.get())) {
                throw new RuntimeException(threadException.get());
            }
            for (IfmxStreamRecord record : records) {
                if (record.getType() != AFTER_UPDATE) {
                    normalRedo = new NormalRedo();
                }
                Assert.notNull(normalRedo, "redoLogContent can not be null");
                normalRedo.setOperation(String.valueOf(record.getType()));
                switch (record.getType()) {
                    case INSERT:
                    case DELETE: {
                        Map<String, Object> data = new HashMap<>(((IfxCDCOperationRecord) record).getData());
                        normalRedo.setRedoRecord(data);
                        break;
                    }
                    case AFTER_UPDATE: {
                        Map<String, Object> data = new HashMap<>(((IfxCDCOperationRecord) record).getData());
                        normalRedo.setRedoRecord(data);
                        normalRedo.setOperation("UPDATE");
                        break;
                    }
                    case BEFORE_UPDATE: {
                        Map<String, Object> data = new HashMap<>(((IfxCDCOperationRecord) record).getData());
                        normalRedo.setUndoRecord(data);
                        continue;
                    }
                    case BEGIN:
                    case COMMIT:
                    case ROLLBACK:
                    case TIMEOUT:
                        break;
                    case DISCARD:
                        normalRedo.setOperation("ROLLBACK");
                        break;
                    case METADATA:
                        // TODO: 2023/6/4 ddl need to be supported
//                    redoLogContent.setOperation("DDL");
//                    redoLogContent.setRedoRecord(TapSimplify.map(TapSimplify.entry("ddl", ((IfxCDCMetaDataRecord) record).getColumns())));
//                    break;
                    case ERROR:
                    case TRUNCATE:
                    case TRANSACTION_GROUP:
                        tapLogger.warn("find recordType {} Check the sequenceId: {}", record.getType(), record.getSequenceId());
                        continue;
                    default:
                        throw new IllegalStateException("Unexpected value: " + record.getType());
                }
                normalRedo.setCdcSequenceId(record.getSequenceId());
                normalRedo.setTimestamp(0L);
                if (record instanceof IfxCDCCommitTransactionRecord) {
                    lastEventTimestamp = ((IfxCDCCommitTransactionRecord) record).getTime() * 1000;
                    normalRedo.setTimestamp(lastEventTimestamp);
                }
                if (record instanceof IfxCDCBeginTransactionRecord) {
                    lastEventTimestamp = ((IfxCDCBeginTransactionRecord) record).getTime() * 1000;
                    normalRedo.setTimestamp(lastEventTimestamp);
                }
                if (EmptyKit.isNotNull(record.getLabel())) {
                    normalRedo.setTableName(labelTableMap.get(Integer.valueOf(record.getLabel())));
                    normalRedo.setNameSpace(labelSchemaMap.get(Integer.valueOf(record.getLabel())));
                }
                normalRedo.setTransactionId(String.valueOf(record.getTransactionId()));
                // TODO: 2023/6/4 big transaction need to be supported
                enqueueRedoLogContent(normalRedo);
            }

        }
    }

    protected void initRedoLogQueueAndThread() {
        if (redoLogConsumerThreadPool == null) {
            redoLogConsumerThreadPool = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
            redoLogConsumerThreadPool.submit(() -> {
                NormalRedo normalRedo;
                while (isRunning.get()) {
                    while (ddlStop.get()) {
                        TapSimplify.sleep(1000);
                    }
                    try {
                        normalRedo = logQueue.poll(1, TimeUnit.SECONDS);
                        if (normalRedo == null) {
                            continue;
                        }
                    } catch (Exception e) {
                        break;
                    }
                    try {
                        if ("TIMEOUT".equals(normalRedo.getOperation())) {
                            consumer.accept(Collections.singletonList(new HeartbeatEvent().init().referenceTime(System.currentTimeMillis())), informixOffset);
                        }
                        convertDataToJavaType(normalRedo);
                        processOrBuffRedo(normalRedo, this::sendTransaction);

                    } catch (Throwable e) {
                        threadException.set(e);
                        consumer.streamReadEnded();
                    }
                }
            });
            redoLogConsumerThreadPool.submit(() -> {
                try {
                    while (isRunning.get()) {
                        Iterator<String> iterator = transactionBucket.keySet().iterator();
                        while (iterator.hasNext()) {
                            String transactionId = iterator.next();
                            NormalTransaction transaction = transactionBucket.get(transactionId);
                            if (lastEventTimestamp - transaction.getFirstTimestamp() < 720 * 60 * 1000L) {
                                break;
                            } else {
                                tapLogger.warn("Uncommitted transaction {} with {} events will be dropped", transactionId, transaction.getSize());
                                transaction.clearRedoes();
                                iterator.remove();
                            }
                        }
                        int sleep = 60;
                        try {
                            while (isRunning.get() && (sleep-- > 0)) {
                                TapSimplify.sleep(1000);
                            }
                        } catch (Exception ignore) {
                        }
                    }
                } catch (Exception e) {
                    threadException.set(e);
                }
            });
        }
    }

    @Override
    public void stopMiner() throws Throwable {
        super.stopMiner();
        if (EmptyKit.isNotNull(engine)) {
            engine.close();
        }
    }

    @Override
    protected void ddlFlush() throws Throwable {

    }

    private void convertDataToJavaType(NormalRedo normalRedo) throws SQLException {
        if (normalRedo.getRedoRecord() != null) {
            Map<String, Object> redoRecord = normalRedo.getRedoRecord();
            for (Map.Entry<String, Object> entry : redoRecord.entrySet()) {
                parseKeyValue(entry);
            }
        }
        if (normalRedo.getUndoRecord() != null) {
            Map<String, Object> undoRecord = normalRedo.getUndoRecord();
            for (Map.Entry<String, Object> entry : undoRecord.entrySet()) {
                parseKeyValue(entry);
            }
        }
    }

    private void parseKeyValue(Map.Entry<String, Object> entry) throws SQLException {
        if (EmptyKit.isNull(entry.getValue())) {
            return;
        }
        entry.setValue(((IfxObject) entry.getValue()).toObject());
//        switch (entry.getValue().getClass().getSimpleName()) {
//            case "IfxChar":
//            case "IfxVarChar":
//            case "IfxLvarchar":
//                entry.setValue(entry.getValue().toString());
//                break;
//            case "IfxBoolean":
//                entry.setValue(((IfxBoolean) entry.getValue()).toBoolean());
//                break;
//            case "IfxBigInt":
//            case "IfxInt8":
//                entry.setValue(Long.valueOf(entry.getValue().toString()));
//                break;
//            case "IfxInteger":
//            case "IfxShort":
//                entry.setValue(Integer.valueOf(entry.getValue().toString()));
//                break;
//            case "IfxBigDecimal":
//        }
    }

    @Override
    protected void createEvent(NormalRedo normalRedo, AtomicReference<List<TapEvent>> eventList, long timestamp) {
        if (EmptyKit.isNull(Objects.requireNonNull(normalRedo).getRedoRecord())) {
            return;
        }
        TapRecordEvent recordEvent;
        switch (Objects.requireNonNull(normalRedo).getOperation()) {
            case "INSERT": {
                recordEvent = new TapInsertRecordEvent().init()
                        .table(normalRedo.getTableName())
                        .after(normalRedo.getRedoRecord());
                break;
            }
            case "UPDATE": {
                recordEvent = new TapUpdateRecordEvent().init()
                        .table(normalRedo.getTableName())
                        .after(normalRedo.getRedoRecord())
                        .before(normalRedo.getUndoRecord());
                break;
            }
            case "DELETE": {
                recordEvent = new TapDeleteRecordEvent().init()
                        .table(normalRedo.getTableName())
                        .before(normalRedo.getRedoRecord());
                break;
            }
            default:
                return;
        }
        recordEvent.setReferenceTime(timestamp);
        eventList.get().add(recordEvent);
    }

    @Override
    protected void submitEvent(NormalRedo normalRedo, List<TapEvent> list) {
        assert normalRedo != null;
        informixOffset.setLastScn(normalRedo.getCdcSequenceId());
        Iterator<NormalTransaction> iterator = transactionBucket.values().iterator();
        if (iterator.hasNext()) {
            informixOffset.setPendingScn(iterator.next().getCdcSequenceId());
        } else {
            informixOffset.setPendingScn(normalRedo.getCdcSequenceId());
        }
        if (list.size() > 0) {
            consumer.accept(list, informixOffset);
        }
    }
}
