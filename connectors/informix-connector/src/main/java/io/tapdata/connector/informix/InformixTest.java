package io.tapdata.connector.informix;

import io.tapdata.common.CommonDbTest;
import io.tapdata.connector.informix.config.InformixConfig;
import io.tapdata.pdk.apis.entity.TestItem;

import java.util.function.Consumer;

import static io.tapdata.base.ConnectorBase.testItem;

public class InformixTest extends CommonDbTest {

    public InformixTest(InformixConfig informixConfig, Consumer<TestItem> consumer) {
        super(informixConfig, consumer);
        jdbcContext = new InformixJdbcContext(informixConfig);
    }

    public Boolean testReadPrivilege() {
        consumer.accept(testItem(TestItem.ITEM_READ, TestItem.RESULT_SUCCESSFULLY, "All tables can be selected"));
        return true;
    }

    @Override
    public Boolean testStreamRead() {
        consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY));
        return true;
    }

}
