package io.tapdata.connector.informix.cdc;

import io.tapdata.common.cdc.CdcRunner;
import io.tapdata.common.cdc.ILogMiner;
import io.tapdata.connector.informix.InformixJdbcContext;
import io.tapdata.connector.informix.cdc.logminer.InformixNewLogMiner;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;

import java.util.List;

public class InformixCdcRunner implements CdcRunner {

    private final ILogMiner logMiner;

    public InformixCdcRunner(InformixJdbcContext informixJdbcContext, String connectorId, Log tapLogger) throws Throwable {
        logMiner = new InformixNewLogMiner(informixJdbcContext, connectorId, tapLogger);
    }

    public InformixCdcRunner init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap,
                                  Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        logMiner.init(
                tableList,
                tableMap,
                offsetState,
                recordSize,
                consumer
        );
        return this;
    }

    @Override
    public void startCdcRunner() throws Throwable {
        logMiner.startMiner();
    }

    @Override
    public void closeCdcRunner() throws Throwable {
        logMiner.stopMiner();
    }

    @Override
    public boolean isRunning() {
        return false;
    }

    @Override
    public void run() {
        try {
            startCdcRunner();
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }
}
