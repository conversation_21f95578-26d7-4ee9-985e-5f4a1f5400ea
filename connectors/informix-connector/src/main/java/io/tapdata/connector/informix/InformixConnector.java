package io.tapdata.connector.informix;

import com.informix.jdbcx.IfxDataSource;
import com.informix.stream.api.IfmxStreamRecord;
import com.informix.stream.cdc.IfxCDCEngine;
import io.tapdata.common.CommonDbConnector;
import io.tapdata.common.CommonSqlMaker;
import io.tapdata.common.SqlExecuteCommandFunction;
import io.tapdata.connector.informix.bean.InformixColumn;
import io.tapdata.connector.informix.cdc.InformixCdcRunner;
import io.tapdata.connector.informix.cdc.InformixOffset;
import io.tapdata.connector.informix.config.InformixConfig;
import io.tapdata.connector.informix.dml.InformixRecordWriter;
import io.tapdata.entity.codec.TapCodecsRegistry;
import io.tapdata.entity.event.ddl.table.TapCreateTableEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.value.*;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.annotations.TapConnectorClass;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.context.TapConnectionContext;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TestItem;
import io.tapdata.pdk.apis.entity.WriteListResult;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.connector.target.CreateTableOptions;
import org.springframework.util.FileSystemUtils;

import java.io.File;
import java.sql.SQLException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

@TapConnectorClass("spec_informix.json")
public class InformixConnector extends CommonDbConnector {

    private InformixConfig informixConfig;
    private InformixJdbcContext informixJdbcContext;
    private InformixCdcRunner cdcRunner;

    private void initConnection(TapConnectionContext connectionContext) {
        informixConfig = (InformixConfig) new InformixConfig().load(connectionContext.getConnectionConfig());
        isConnectorStarted(connectionContext, connectorContext -> {
            firstConnectorId = (String) connectorContext.getStateMap().get("firstConnectorId");
            if (EmptyKit.isNull(firstConnectorId)) {
                firstConnectorId = connectionContext.getId();
                connectorContext.getStateMap().put("firstConnectorId", firstConnectorId);
            }
            informixConfig.load(connectorContext.getNodeConfig());
        });
        informixJdbcContext = new InformixJdbcContext(informixConfig);
        commonDbConfig = informixConfig;
        jdbcContext = informixJdbcContext;
        commonSqlMaker = new CommonSqlMaker();
        tapLogger = connectionContext.getLog();
    }

    @Override
    public void onStart(TapConnectionContext connectionContext) {
        initConnection(connectionContext);
        clearLogCache();
    }

    @Override
    public void onStop(TapConnectionContext connectionContext) throws Throwable {
        if (EmptyKit.isNotNull(informixJdbcContext)) {
            informixJdbcContext.close();
        }
        if (EmptyKit.isNotNull(cdcRunner)) {
            cdcRunner.closeCdcRunner();
            cdcRunner = null;
        }
        clearLogCache();
    }

    private void onDestroy(TapConnectorContext connectorContext) {
        clearLogCache();
    }

    private void clearLogCache() {
        File cacheDir = new File("cacheTransaction" + File.separator + firstConnectorId);
        if (cacheDir.exists()) {
            FileSystemUtils.deleteRecursively(cacheDir);
        }
    }

    @Override
    protected TapField makeTapField(DataMap dataMap) {
        return new InformixColumn(dataMap).getTapField();
    }

    @Override
    public void registerCapabilities(ConnectorFunctions connectorFunctions, TapCodecsRegistry codecRegistry) {
        connectorFunctions.supportReleaseExternalFunction(this::onDestroy);
        connectorFunctions.supportErrorHandleFunction(this::errorHandle);
        connectorFunctions.supportCreateTableV2(this::createTableV2);
        connectorFunctions.supportDropTable(this::dropTable);
        connectorFunctions.supportClearTable(this::clearTable);
        connectorFunctions.supportWriteRecord(this::writeRecord);
        connectorFunctions.supportBatchCount(this::batchCount);
        connectorFunctions.supportBatchRead(this::batchReadWithoutOffset);
        connectorFunctions.supportStreamRead(this::streamRead);
        connectorFunctions.supportTimestampToStreamOffset(this::timestampToStreamOffset);
        connectorFunctions.supportQueryByAdvanceFilter(this::queryByAdvanceFilterWithOffset);
        connectorFunctions.supportCountByPartitionFilterFunction(this::countByAdvanceFilter);
        connectorFunctions.supportQueryByFilter(this::queryByFilter);
        connectorFunctions.supportExecuteCommandFunction((a, b, c) -> SqlExecuteCommandFunction.executeCommand(a, b, () -> informixJdbcContext.getConnection(), this::isAlive, c));
        connectorFunctions.supportRunRawCommandFunction(this::runRawCommand);

        codecRegistry.registerFromTapValue(TapRawValue.class, "TEXT", tapRawValue -> {
            if (tapRawValue != null && tapRawValue.getValue() != null) return tapRawValue.getValue().toString();
            return "null";
        });
        codecRegistry.registerFromTapValue(TapMapValue.class, "TEXT", tapMapValue -> {
            if (tapMapValue != null && tapMapValue.getValue() != null) return toJson(tapMapValue.getValue());
            return "null";
        });
        codecRegistry.registerFromTapValue(TapArrayValue.class, "TEXT", tapValue -> {
            if (tapValue != null && tapValue.getValue() != null) return toJson(tapValue.getValue());
            return "null";
        });
        //TapTimeValue, TapDateTimeValue and TapDateValue's value is DateTime, need convert into Date object.
        codecRegistry.registerFromTapValue(TapTimeValue.class, tapTimeValue -> tapTimeValue.getValue().toTime());
        codecRegistry.registerFromTapValue(TapDateTimeValue.class, tapDateTimeValue -> tapDateTimeValue.getValue().toTimestamp());
        codecRegistry.registerFromTapValue(TapDateValue.class, tapDateValue -> tapDateValue.getValue().toSqlDate());
    }

    @Override
    public ConnectionOptions connectionTest(TapConnectionContext connectionContext, Consumer<TestItem> consumer) {
        informixConfig = (InformixConfig) new InformixConfig().load(connectionContext.getConnectionConfig());
        ConnectionOptions connectionOptions = ConnectionOptions.create();
        connectionOptions.connectionString(informixConfig.getConnectionString());
        try (
                InformixTest informixTest = new InformixTest(informixConfig, consumer)
        ) {
            informixTest.testOneByOne();
        }
        return connectionOptions;
    }

    protected CreateTableOptions createTableV2(TapConnectorContext tapConnectorContext, TapCreateTableEvent tapCreateTableEvent) throws SQLException {
        TapTable tapTable = tapCreateTableEvent.getTable();
        CreateTableOptions createTableOptions = new CreateTableOptions();
        if (informixJdbcContext.queryAllTables(Collections.singletonList(tapTable.getId())).size() > 0) {
            createTableOptions.setTableExists(true);
            return createTableOptions;
        }
        Collection<String> primaryKeys = tapTable.primaryKeys();
        String sql = "CREATE TABLE IF NOT EXISTS \"" + informixConfig.getSchema() + "\".\"" + tapTable.getId() + "\"(" + new CommonSqlMaker().buildColumnDefinition(tapTable, false);
        if (EmptyKit.isNotEmpty(tapTable.primaryKeys())) {
            sql += "," + " PRIMARY KEY (\"" + String.join("\",\"", primaryKeys) + "\" )";
        }
        sql += ") ";
        informixJdbcContext.execute(sql);
        createTableOptions.setTableExists(false);
        return createTableOptions;
    }

    private void writeRecord(TapConnectorContext tapConnectorContext, List<TapRecordEvent> tapRecordEvents, TapTable tapTable, Consumer<WriteListResult<TapRecordEvent>> consumer) throws Throwable {
        String insertDmlPolicy = tapConnectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_INSERT_POLICY);
        String updateDmlPolicy = tapConnectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_UPDATE_POLICY);
        new InformixRecordWriter(informixJdbcContext, tapTable)
                .setInsertPolicy(insertDmlPolicy)
                .setUpdatePolicy(updateDmlPolicy)
                .write(tapRecordEvents, consumer);
    }

    private void streamRead(TapConnectorContext nodeContext, List<String> tableList, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        if (EmptyKit.isNull(cdcRunner)) {
            cdcRunner = new InformixCdcRunner(informixJdbcContext, firstConnectorId, tapLogger).init(
                    tableList,
                    nodeContext.getTableMap(),
                    offsetState,
                    recordSize,
                    consumer
            );
        }
        cdcRunner.startCdcRunner();
    }

    private Object timestampToStreamOffset(TapConnectorContext connectorContext, Long offsetStartTime) throws Throwable {
        InformixConfig cdcConfig = (InformixConfig) new InformixConfig().load(connectorContext.getConnectionConfig());
        cdcConfig.setDatabase("syscdcv1");
        AtomicReference<Long> sequenceId = new AtomicReference<>();
        AtomicReference<IfxCDCEngine> engine = new AtomicReference<>();
        try (
                InformixJdbcContext cdcJdbcContext = new InformixJdbcContext(cdcConfig)
        ) {
            cdcJdbcContext.execute("create table if not exists informix.tapdata_cdc(a1 int)");
            AtomicReference<Exception> exception = new AtomicReference<>();
            AtomicReference<Boolean> isStarted = new AtomicReference<>(false);
            new Thread(() -> {
                try {
                    IfxDataSource ds = new IfxDataSource(cdcConfig.getDatabaseUrl() + "user=" + cdcConfig.getUser() + ";password=" + cdcConfig.getPassword() + ";");
                    IfxCDCEngine.Builder builder = new IfxCDCEngine.Builder(ds);
                    builder.watchTable("syscdcv1:informix.tapdata_cdc", "a1");
                    builder.timeout(5);
                    engine.set(builder.build());
                    engine.get().setTapLogger(tapLogger);
                    engine.get().setFetchSize(1);
                    engine.get().init();
                    isStarted.set(true);
                    IfmxStreamRecord record;
                    while (isAlive() && (record = engine.get().getRecord()) != null) {
                        if (record.hasOperationData()) {
                            sequenceId.set(record.getSequenceId());
                            break;
                        }
                    }
                } catch (Exception e) {
                    exception.set(e);
                }
            }).start();
            boolean inserted = false;
            while (isAlive()) {
                if (EmptyKit.isNotNull(exception.get())) {
                    throw exception.get();
                }
                if (!isStarted.get()) {
                    TapSimplify.sleep(1000);
                    continue;
                }
                TapSimplify.sleep(1000);
                if (!inserted) {
                    cdcJdbcContext.execute("insert into informix.tapdata_cdc values(1)");
                    inserted = true;
                }
                if (EmptyKit.isNotNull(sequenceId.get())) {
                    break;
                }
            }
        } finally {
            if (EmptyKit.isNotNull(engine.get())) {
                engine.get().close();
            }
        }
        InformixOffset informixOffset = new InformixOffset();
        informixOffset.setPendingScn(sequenceId.get());
        informixOffset.setLastScn(sequenceId.get());
        return informixOffset;
    }
}
