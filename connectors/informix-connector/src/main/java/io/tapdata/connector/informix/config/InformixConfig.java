package io.tapdata.connector.informix.config;

import io.tapdata.common.CommonDbConfig;
import io.tapdata.kit.EmptyKit;

import java.io.Serializable;

public class InformixConfig extends CommonDbConfig implements Serializable {

    private String server;

    public InformixConfig() {
        setDbType("informix-sqli");
        setEscapeChar('"');
        setJdbcDriver("com.informix.jdbc.IfxDriver");
    }

    public String getDatabaseUrlPattern() {
        return "jdbc:informix-sqli://%s:%s/%s:INFORMIXSERVER=%s;delimident=y;%s";
    }

    public String getDatabaseUrl() {
        if (EmptyKit.isNull(this.getExtParams())) {
            this.setExtParams("");
        }
        if (EmptyKit.isNotEmpty(this.getExtParams()) && (this.getExtParams().startsWith("?") || this.getExtParams().startsWith(":"))) {
            this.setExtParams(this.getExtParams().substring(1));
        }
        return String.format(this.getDatabaseUrlPattern(), this.getHost(), this.getPort(), this.getDatabase(), this.getServer(), this.getExtParams());
    }

    public String getServer() {
        return server;
    }

    public void setServer(String server) {
        this.server = server;
    }
}
