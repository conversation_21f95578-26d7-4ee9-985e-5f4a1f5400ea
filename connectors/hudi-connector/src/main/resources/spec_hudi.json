{"properties": {"name": "hudi", "icon": "icons/hudi.png", "id": "hudi", "doc": "${doc}", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists"]}, {"id": "dml_update_policy", "alternatives": ["insert_on_nonexists"]}], "connection": {"type": "object", "properties": {"nameSrvAddr": {"required": true, "type": "string", "title": "${nameSrvAddr}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "mq_nameSrvAddr", "x-index": 10}, "database": {"type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 20, "required": true}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 30}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 40}, "krb5": {"type": "boolean", "title": "${krb5}", "default": false, "x-decorator": "FormItem", "x-component": "Switch", "apiServerKey": "krb5", "x-reactions": [{"target": "*(krb5<PERSON><PERSON><PERSON><PERSON>,krb5<PERSON><PERSON><PERSON>,principal,krb5ServiceName)", "fulfill": {"state": {"visible": "{{$self.value===true}}"}}}], "x-index": 50}, "krb5Keytab": {"type": "string", "title": "${krb5Keytab}", "x-decorator": "FormItem", "x-component": "TextFileReader", "x-component-props": {"base64": true}, "apiServerKey": "krb5Keytab", "fileNameField": "krb5KeytabFile", "required": true, "x-index": 60}, "krb5Conf": {"type": "string", "title": "${krb5Conf}", "x-decorator": "FormItem", "x-component": "TextFileReader", "x-component-props": {"base64": true}, "apiServerKey": "krb5Conf", "fileNameField": "krb5ConfFile", "required": true, "x-index": 70}, "principal": {"type": "string", "title": "${principal}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "principal", "required": true, "x-index": 80}, "hadoopCoreSiteXML": {"type": "string", "title": "${hadoopCoreSiteXML}", "x-decorator": "FormItem", "x-component": "TextFileReader", "x-component-props": {"base64": true}, "x-decorator-props": {"tooltip": "${hadoopCoreSiteXML_tip}"}, "apiServerKey": "hadoopCoreSiteXML", "fileNameField": "hadoopCoreSiteXMLFile", "required": true, "x-index": 90}, "hdfsSiteXML": {"type": "string", "title": "${hdfsSiteXML}", "x-decorator": "FormItem", "x-component": "TextFileReader", "x-component-props": {"base64": true}, "x-decorator-props": {"tooltip": "${hdfsSiteXML_tip}"}, "apiServerKey": "hdfsSiteXML", "fileNameField": "hdfsSiteXMLFile", "required": true, "x-index": 100}, "hiveSiteXML": {"type": "string", "title": "${hiveSiteXML}", "x-decorator": "FormItem", "x-component": "TextFileReader", "x-component-props": {"base64": true}, "x-decorator-props": {"tooltip": "${hiveSiteXML_tip}"}, "apiServerKey": "hiveSiteXML", "fileNameField": "hiveSiteXMLFile", "required": true, "x-index": 110}, "ssl": {"type": "boolean", "title": "${ssl}", "default": true, "x-decorator": "FormItem", "x-component": "Switch", "apiServerKey": "ssl", "x-index": 120}, "extParams": {"type": "string", "title": "${extParams}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "extParams", "x-index": 130}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-index": 140, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}, "node": {"properties": {"tableType": {"type": "string", "title": "${tableType}", "default": "COPY_ON_WRITE", "x-decorator": "FormItem", "x-component": "Select", "x-index": 10, "enum": [{"label": "COPY_ON_WRITE", "value": "COPY_ON_WRITE"}, {"label": "MERGE_ON_READ", "value": "MERGE_ON_READ"}], "x-display": "hidden"}, "noPkAutoInsert": {"type": "boolean", "title": "${noPkAutoInsert}", "default": true, "x-decorator": "FormItem", "x-component": "Switch", "apiServerKey": "noPkAutoInsert", "x-index": 20, "x-display": "hidden"}}}}, "messages": {"default": "en_US", "en_US": {"tableType": "Table creation type", "noPkAutoInsert": "No primary key table auto append write", "nameSrvAddr": "Cluster Address", "database": "database", "krb5": "kerberos authentication", "krb5Keytab": "Key representation file（*.keytab）", "krb5Conf": "Configuration file（*.conf）", "principal": "Hive Principal", "ssl": "ssl", "user": "user", "password": "password", "extParams": "Connection Parameter String", "timezone": "timezone", "doc": "docs/hudi_en_US.md", "hiveSiteXML": "Hive conf file", "hdfsSiteXML": "HDFS conf file", "hadoopCoreSiteXML": "<PERSON><PERSON> conf", "hiveSiteXML_tip": "Hive configuration file(hive-site.xml)", "hdfsSiteXML_tip": "HDFS configuration file(hdfs-site.xml)", "hadoopCoreSiteXML_tip": "Hadoop configuration file(core-site.xml)", "deleteStage": "删除策略", "deleteStage_1": "逻辑策略", "deleteStage_2": "物理策略"}, "zh_CN": {"tableType": "建表类型", "noPkAutoInsert": "无主键表自动追加写入", "nameSrvAddr": "集群地址", "database": "数据库", "krb5": "kerberos 认证", "krb5Keytab": "密钥表示文件（*.keytab）", "krb5Conf": "配置文件（*.conf）", "principal": "Hive主体配置", "ssl": "ssl", "user": "账号", "password": "密码", "extParams": "连接参数", "timezone": "时区", "doc": "docs/hudi_zh_CN.md", "hiveSiteXML": "Hive配置文件", "hdfsSiteXML": "HDFS配置文件", "hadoopCoreSiteXML": "<PERSON><PERSON>配置文件", "hiveSiteXML_tip": "Hive配置文件(hive-site.xml)", "hdfsSiteXML_tip": "HDFS配置文件(hdfs-site.xml)", "hadoopCoreSiteXML_tip": "Hadoop配置文件(core-site.xml)", "deleteStage": "删除策略", "deleteStage_1": "逻辑策略", "deleteStage_2": "物理策略"}, "zh_TW": {"tableType": "建表類型", "noPkAutoInsert": "無主鍵錶自動追加寫入", "nameSrvAddr": "集群地址", "database": "數據庫", "krb5": "kerberos 認證", "krb5Keytab": "密鑰表示文件（*.keytab）", "krb5Conf": "配置文件（*.conf）", "principal": "Hive主体配置", "ssl": "ssl", "user": "賬號", "password": "密碼", "extParams": "連接參數", "timezone": "時區", "doc": "docs/hudi_zh_TW.md", "hiveSiteXML": "Hive配置文件", "hdfsSiteXML": "HDFS配置文件", "hadoopCoreSiteXML": "<PERSON><PERSON>配置文件", "hiveSiteXML_tip": "Hive配置文件(hive-site.xml)", "hdfsSiteXML_tip": "HDFS配置文件(hdfs-site.xml)", "hadoopCoreSiteXML_tip": "Hadoop配置文件(core-site.xml)", "deleteStage": "删除策略", "deleteStage_1": "逻辑策略", "deleteStage_2": "物理策略"}}, "dataTypes": {"string": {"to": "TapString", "byte": "64k", "name": "string", "pkEnablement": false}, "tinyint": {"to": "TapNumber", "bit": 8, "name": "tinyint", "precision": 3, "value": [-128, 127]}, "smallint": {"to": "TapNumber", "bit": 16, "name": "smallint", "precision": 5, "value": [-32768, 32767]}, "int": {"to": "TapNumber", "bit": 32, "name": "int", "precision": 10, "value": [-2147483648, 2147483647]}, "bigint": {"to": "TapNumber", "name": "bigint", "bit": 64, "precision": 19, "value": [0, 9223372036854775807], "unsigned": "unsigned"}, "float": {"to": "TapNumber", "name": "float", "precision": [1, 30], "scale": [0, 30], "value": ["-3.402823466E+38", "3.402823466E+38"], "fixed": false}, "double": {"to": "TapNumber", "name": "double", "precision": [1, 255], "preferPrecision": 11, "preferScale": 4, "scale": [0, 30], "value": ["-1.7976931348623157E+308", "1.7976931348623157E+308"], "fixed": false}, "decimal[($precision,$scale)]": {"to": "TapNumber", "precision": [1, 65], "scale": [0, 30], "defaultPrecision": 10, "defaultScale": 0, "fixed": true}, "date": {"to": "TapDate", "range": ["1000-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd"}, "timestamp": {"to": "TapDateTime", "range": ["1970-01-01 00:00:00.000", "2038-01-11 23:59:59.999"], "pattern": "yyyy-MM-dd HH:mm:ss.SSS", "fraction": [0, 6], "defaultFraction": 0, "withTimeZone": true}, "boolean": {"bit": 1, "name": "boolean", "to": "TapBoolean"}}}