<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="RegisterMain quickapi in local" type="Application" factoryName="Application" folderName="Register opensource PDK(3000)">
    <option name="MAIN_CLASS_NAME" value="io.tapdata.pdk.cli.RegisterMain" />
    <module name="tapdata-cli (1)" />
    <option name="VM_PARAMETERS" value="-Dtags=quickapi" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="io.tapdata.pdk.cli.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>