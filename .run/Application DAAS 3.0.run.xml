<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Application DAAS 3.0" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" folderName="3.0">
    <envs>
      <env name="app_type" value="DAAS" />
      <env name="process_id" value="sam_iengine" />
      <env name="TAPDATA_MONGO_URI" value="mongodb://localhost/tapdata_v3_0" />
      <env name="DEBUG" value="false" />
    </envs>
    <module name="iengine-app" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="io.tapdata.Application" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="io.tapdata.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>