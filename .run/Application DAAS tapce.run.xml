<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Application DAAS tapce" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" folderName="Tapce">
    <option name="ALTERNATIVE_JRE_PATH" value="11" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <envs>
      <env name="app_type" value="DAAS" />
      <env name="DEBUG" value="false" />
      <env name="JET_EDGE_QUEUE_SIZE" value="2000" />
      <env name="LOAD_CACHE_LIMIT" value="1000" />
      <env name="process_id" value="sam_iengine" />
      <env name="SOURCE_TO_TAP_VALUE_CONCURRENT" value="true" />
      <env name="SOURCE_TO_TAP_VALUE_CONCURRENT_NUM" value="8" />
      <env name="TAPDATA_MONGO_URI" value="mongodb://localhost/tapdata_tapce" />
    </envs>
    <module name="iengine-app" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="io.tapdata.Application" />
    <option name="VM_PARAMETERS" value="-Duser.timezone=GMT" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="io.tapdata.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>