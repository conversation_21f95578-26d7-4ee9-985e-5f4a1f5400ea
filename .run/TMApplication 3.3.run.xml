<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="TMApplication 3.3" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" folderName="3.3">
    <envs>
      <env name="spring.data.mongodb.default.uri" value="mongodb://localhost/tapdata_v3_3" />
      <env name="spring.data.mongodb.log.uri" value="mongodb://localhost/tapdata_v3_3" />
      <env name="spring.data.mongodb.obs.uri" value="mongodb://localhost/tapdata_v3_3" />
      <env name="tapdata_memory_token" value="sam" />
    </envs>
    <module name="tm" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.tapdata.tm.TMApplication" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>