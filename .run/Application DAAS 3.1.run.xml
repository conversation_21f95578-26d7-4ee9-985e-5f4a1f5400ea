<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Application DAAS 3.1" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" folderName="3.1">
    <envs>
      <env name="app_type" value="DAAS" />
      <env name="DEBUG" value="false" />
      <env name="process_id" value="sam_iengine" />
      <env name="TAPDATA_MONGO_URI" value="mongodb://localhost/tapdata_v3_1" />
    </envs>
    <module name="iengine-app" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="io.tapdata.Application" />
    <option name="VM_PARAMETERS" value="-XX:+UnlockCommercialFeatures" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="io.tapdata.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>