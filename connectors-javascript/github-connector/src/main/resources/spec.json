{"properties": {"name": "GitHub", "icon": "icon/github.png", "doc": "${doc}", "id": "GitHub", "tags": ["SaaS"]}, "configOptions": {"pdkExpansion": [], "node": {"type": "object", "properties": {"owner_repo": {"title": "Owner and repo", "type": "string", "required": true, "x-decorator": "FormItem", "x-component": "AsyncSelect", "x-component-props": {"filterable": true, "method": "{{loadCommandList}}", "params": "{{ {$values: $values, command: \"getUserRepos\"} }}", "pageSize": 100}, "x-index": 30, "x-reactions": []}}}, "connection": {"type": "object", "properties": {"code": {"type": "String", "title": "${code}", "x-decorator": "FormItem", "x-component": "Input", "x-decorator-props": {"style": {"display": "none"}}, "apiServerKey": "code", "x-index": 30, "required": true}, "line": {"type": "void", "x-component": "Space", "x-decorator": "FormItem", "title": " ", "x-decorator-props": {"colon": false}, "properties": {"authButton": {"type": "void", "x-decorator": "FormItem", "x-component": "<PERSON><PERSON>", "x-content": "${OAuthButtonTitle}", "title": "", "x-index": 20, "x-component-props": {"type": "primary", "onClick": "{{useAsyncDataSourceByConfig({service: goToAuthorized, withoutField: true}, { target: 'https://redirect.tapdata.io/oauth/complete', pdkId: 'github', oauthUrl: 'https://github.com/login/oauth/authorize?scope=user repo public_repo read:project read:user read:org&client_id=3befd8286de3bc48e082&redirect_uri=https%3A%2F%2Fredirect.tapdata.io%2Foauth%2Fcomplete%2Fgithub&state=1' })}}"}, "x-decorator-props": {"style": {"width": "120px"}}}, "OAuthStatus": {"type": "void", "title": "", "x-decorator": "FormItem", "x-component": "Text", "apiServerKey": "AAA", "x-index": 30, "required": true, "x-component-props": {"content": "{{$values.code ? '${OAuthButtonOK}' : '${OAuthButton}'}}", "style": {"color": "{{ $values.code ? 'green' : 'rgb(83, 95, 114)' }}"}}}}}}}}, "messages": {"default": "en_US", "en_US": {"doc": "doc/demo_en_US.md", "owner": "GitHub owner", "repo": "GitHub repo", "code": "Authorize", "OAuthButtonTitle": "Authorize", "OAuthButton": "Please authorize API access to continue", "OAuthButtonOK": "Authorization successful"}, "zh_CN": {"doc": "doc/demo_zh_CN.md", "owner": "GitHub owner", "repo": "GitHub repo", "code": "授权", "OAuthButtonTitle": "授权", "OAuthButton": "请先授权成功再点击连接测试或保存", "OAuthButtonOK": "成功授权"}, "zh_TW": {"doc": "doc/demo_zh_TW.md", "owner": "GitHub owner", "repo": "GitHub repo", "code": "授權", "OAuthButtonTitle": "授權", "OAuthButton": "請先授權成功再點擊鏈接測試或保存", "OAuthButtonOK": "成功授權"}}}