## Create Lark data source (target)

You need to go to https://open.feishu.cn/app Find the corresponding application and find it in the ***voucher and basic information*** of the application:

![](https://tapdata-bucket-01.oss-cn-beijing.aliyuncs.com/lark/step_0.PNG)

![](https://tapdata-bucket-01.oss-cn-beijing.aliyuncs.com/lark/step_1.PNG)

1. Get ***App ID*** and fill in here.

2. Get***App Secret***and fill in here.

At this time, the FeiShu data source is created successfully!

###Precautions

####Fly book creation quest

***Note:***：

Approval type Code: 49636CC4-9156-48C0-9609-32CAACC85E00

The approval type Code needs to be obtained in the background of the flybook administrator, as shown in the picture:

![](https://tapdata-bucket-01.oss-cn-beijing.aliyuncs.com/lark/approval/approval_2.jpg)

![](https://tapdata-bucket-01.oss-cn-beijing.aliyuncs.com/lark/approval_1/approval_1.jpg)
