<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>connectors-javascript</artifactId>
        <groupId>io.tapdata</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zoho-crm-connector</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>zoho-crm-connector</name>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>js-connector-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>js-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <finalName>${connector.file.name}</finalName>
                    <transformers>
                        <transformer
                                implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                            <manifestEntries>
                                <Implementation-Title>${project.artifactId}</Implementation-Title>
                                <Implementation-Version>${project.version}</Implementation-Version>
                                <Implementation-Vendor>${project.groupId}</Implementation-Vendor>
                                <Build-OS>${os.name} ${os.version}</Build-OS>
                                <Build-Java>Java ${java.version}</Build-Java>
                                <PDK-Runner-Version>${tapdata.pdk.runner.version}</PDK-Runner-Version>
                                <PDK-API-Version>${tapdata.pdk.api.version}</PDK-API-Version>
                                <Tapdata-API-Version>${tapdata.pdk.api.version}</Tapdata-API-Version>
                                <Version>${project.version}</Version>
                                <Authentication>Alpha</Authentication>
                                <PayMode>${pay.mode}</PayMode>
                                <Git-Build-Time>${git.build.time}</Git-Build-Time>
                                <Git-Branch>${git.branch}</Git-Branch>
                                <Git-Commit-Id>${git.commit.id}</Git-Commit-Id>
                                <Git-Build-User-Name>${git.build.user.name}</Git-Build-User-Name>
                                <Git-Build-User-Email>${git.build.user.email}</Git-Build-User-Email>
                            </manifestEntries>
                        </transformer>
                    </transformers>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <filters>
                                <filter>
                                    <artifact>**:**</artifact>
                                    <excludes>
                                        <exclude>**/org/apache/log4j/**</exclude>
                                        <exclude>**/org/apache/logging/log4j/**</exclude>
                                        <exclude>**/org/slf4j/**</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.3</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>validate</phase>
                    </execution>
                </executions>
                <configuration>
                    <verbose>true</verbose>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <failOnNoGitDirectory>true</failOnNoGitDirectory>
                    <injectAllReactorProjects>true</injectAllReactorProjects>
                    <dotGitDirectory>${project.basedir}/../../.git</dotGitDirectory>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-resource-one</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>

                        <configuration>
                            <outputDirectory>../../connectors/dist</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${basedir}/target/</directory>
                                    <includes>
                                        <include>${connector.file.name}.jar</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>4.2.1</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>

        </plugins>
        <resources>
            <resource>
                <directory>src/main/javascript</directory>
                <includes>
                    <include>**/*.js</include>
                </includes>
                <targetPath>./${project.parent.artifactId}/${project.artifactId}/src/main/javascript/</targetPath>
            </resource>
            <resource>
                <directory>${project.basedir}/src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <targetPath>./</targetPath>
            </resource>
        </resources>
    </build>
</project>