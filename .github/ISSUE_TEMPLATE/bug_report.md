---		
 name: Bug		
 about: Create a report to help us improve		
 labels: 'Type: Defect'		
---

<!--
Thanks for reporting your issue. Please share with us the following information, to help us resolve your issue quickly and efficiently.
-->
  
**Describe the bug**
A clear and concise description of what the bug is.

**Expected behavior**
A clear and concise description of what you expected to happen.

**To Reproduce**

Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. ...

**Additional context**

<!--
Add any other context about the problem here.

Common details that we're often interested in:
- Detailed description of the steps to reproduce your issue
- Logs and stack traces, if available
- Hazelcast version that you use (e.g. 3.4, also specify whether it is a minor release or the latest snapshot)
- If available, integration module versions (e.g. Tomcat, Jetty, Spring, Hibernate). Also, include their detailed configuration information such as web.xml, Hibernate configuration and `context.xml` for Spring
- Cluster size, i.e. the number of Hazelcast cluster members
- Number of the clients
- Version of Java. It is also helpful to mention the JVM parameters
- Operating system. If it is Linux, kernel version is helpful
- Unit test with the `hazelcast.xml` file. If you could include a unit test which reproduces your issue, we would be grateful
-->
