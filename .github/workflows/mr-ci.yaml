name: Merge Request CI

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}

on:
  push:
    branches: [ "main", "develop", "release-v*.*" ]
  pull_request:
    branches: [ "main", "develop", "release-v*.*" ]

jobs:

  Get-Branch:
    runs-on: ubuntu-latest
    outputs:
      FRONTEND_BRANCH: ${{ steps.set-outputs.outputs.FRONTEND_BRANCH }}
    steps:
      - name: Get Enterprise Branch Name -- Common
        run: |
          echo "FRONTEND_BRANCH=${{ github.ref_name }}" >> $GITHUB_ENV
      - name: Get Enterprise Branch Name -- Pull Request
        if: ${{ github.event_name == 'pull_request' }}
        run: |
          echo "FRONTEND_BRANCH=${{ github.event.pull_request.head.ref }}" >> $GITHUB_ENV
      - name: Get Enterprise Branch Name -- Schedule
        if: ${{ github.event_name == 'schedule' }}
        run: |
          echo "FRONTEND_BRANCH=develop" >> $GITHUB_ENV
      - name: Set Output
        id: set-outputs
        run: |
          echo "::set-output name=FRONTEND_BRANCH::${FRONTEND_BRANCH}"

  Get-Stable-Branch:
    needs:
      - Get-Branch
    uses: tapdata/tapdata-application/.github/workflows/get-stable-branch-and-set-tag.yaml@main
    with:
      tapdata-frontend-branch: ${{ needs.Get-Branch.outputs.FRONTEND_BRANCH }}
    secrets:
      TAPDATA_ENT_CICD_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}

  Sync-Code-to-Office:
    uses: tapdata/tapdata-application/.github/workflows/sync-code-to-office.yaml@main
    needs:
      - Get-Stable-Branch
    with:
      tapdata: main
      tapdata-enterprise: main
      tapdata-license: main
      tapdata-connectors: main
      tapdata-connectors-enterprise: main
      tapdata-enterprise-web: ${{ needs.Get-Stable-Branch.outputs.FRONTEND_BRANCH }}
    secrets:
      TAPDATA_ENT_CICD_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}

  Scan-Frontend:
    runs-on: ubuntu-latest
    needs:
      - Sync-Code-to-Office
      - Get-Stable-Branch
    steps:
      - name: Scan -- Tapdata-Enterprise
        uses: convictional/trigger-workflow-and-wait@v1.6.1
        with:
          owner: tapdata
          repo: tapdata-application
          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          workflow_file_name: sonarqube-scan.yaml
          ref: main
          wait_interval: 10
          client_payload: '{"tapdata-enterprise-web": "${{ needs.Get-Stable-Branch.outputs.FRONTEND_BRANCH }}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: true

#  Build-And-Deploy:
#    if: ${{ github.event_name == 'schedule' || inputs.mrci_run }}
#    runs-on: ubuntu-latest
#    needs:
#      - Sync-Code-to-Office
#      - Get-Stable-Branch
#    outputs:
#      IP: ${{ steps.get_ip_port.outputs.IP }}
#      PORT: ${{ steps.get_ip_port.outputs.PORT }}
#    steps:
#      - name: Trigger - Build Tapdata
#        uses: convictional/trigger-workflow-and-wait@v1.6.1
#        with:
#          owner: tapdata
#          repo: tapdata-application
#          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          workflow_file_name: build-tapdata-op.yaml
#          ref: main
#          wait_interval: 10
#          client_payload: '{"FRONTEND_BRANCH": "${{ needs.Get-Stable-Branch.outputs.FRONTEND_BRANCH }}", "TAG_NAME": "${{ needs.Get-Stable-Branch.outputs.TAG_NAME }}"}'
#          propagate_failure: true
#          trigger_workflow: true
#          wait_workflow: true
#      - name: Trigger - Deploy Tapdata
#        uses: convictional/trigger-workflow-and-wait@v1.6.1
#        with:
#          owner: tapdata
#          repo: tapdata-application
#          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          workflow_file_name: deploy-tapdata-op.yaml
#          ref: main
#          wait_interval: 10
#          client_payload: '{"TAG_NAME": "${{ needs.Get-Stable-Branch.outputs.TAG_NAME }}", "AUTO_TEST": true}'
#          propagate_failure: true
#          trigger_workflow: true
#          wait_workflow: true
#      - name: Checkout Tapdata Application
#        uses: actions/checkout@v2
#        with:
#          repository: 'tapdata/tapdata-application'
#          ref: "main"
#          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          path: tapdata-application
#      - name: Get IP and Port
#        id: get_ip_port
#        run: |
#          bash tapdata-application/build/upgrade.sh --get-ip-port=true --version=${{ needs.Get-Stable-Branch.outputs.TAG_NAME }} --deploy-way=docker-compose
#          IP=$(cat .service_ip_port | awk -F':' '{print $1}')
#          PORT=$(cat .service_ip_port | awk -F':' '{print $2}')
#          echo "::set-output name=IP::$IP"
#          echo "::set-output name=PORT::$PORT"
#
#  TestSigma-Test:
#    if: ${{ github.event_name == 'schedule' || inputs.mrci_run }}
#    runs-on: ubuntu-latest
#    needs:
#      - Build-And-Deploy
#    steps:
#      - name: Set Action
#        if: ${{ github.event_name == 'workflow_dispatch' }}
#        run: |
#          echo "Action=workflow_dispatch" >> $GITHUB_ENV
#      - name: Set Action
#        if: ${{ github.event_name == 'schedule' }}
#        run: |
#          echo "Action=schedule" >> $GITHUB_ENV
#      - name: Trigger - Deploy Tapdata
#        uses: convictional/trigger-workflow-and-wait@v1.6.1
#        with:
#          owner: tapdata
#          repo: tapdata-application
#          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          workflow_file_name: testsigma.yaml
#          ref: main
#          wait_interval: 10
#          client_payload: '{"IP": "${{ needs.Build-And-Deploy.outputs.IP }}", "PORT": "${{ needs.Build-And-Deploy.outputs.PORT }}", "Action": "${{ env.Action }}"}'
#          propagate_failure: true
#          trigger_workflow: true
#          wait_workflow: true
#
#  CLOSE_ENV:
#    if: ${{ github.event_name == 'schedule' || failure() || inputs.mrci_run }}
#    runs-on: ubuntu-latest
#    needs:
#      - TestSigma-Test
#      - Get-Stable-Branch
#    steps:
#      - name: Checkout Tapdata Application
#        uses: actions/checkout@v2
#        with:
#          repository: 'tapdata/tapdata-application'
#          ref: "main"
#          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          path: tapdata-application
#      - name: Close Test Env
#        id: get_ip_port
#        run: |
#          bash tapdata-application/build/upgrade.sh --delete-env=${{ needs.Get-Stable-Branch.outputs.TAG_NAME }} --delete-env-tag=true
#
