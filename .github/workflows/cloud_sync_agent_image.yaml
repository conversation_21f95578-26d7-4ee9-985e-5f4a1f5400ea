name: 云版-Agent镜像分发

on:
  push:
    branches: [ "Xmaster", "Xrelease-v*.*", "Xdevelop-v*.*" ]
  pull_request:
    branches: [ "Xmaster", "Xrelease-v*.*", "Xdevelop-v*.*" ]
  workflow_dispatch:
    inputs:
      AGENT_VERSION:
        description: 'Agent版本'
        required: true
        type: string
      DFS_BRANCH:
        description: 'Tapdata-Cloud分支'
        required: true
        type: string
      JOB_ID:
        description: 'Job ID'
        required: false
        type: string

jobs:

  Sync-Agent-Image:
    runs-on: gcp-docker
    steps:
      - name: 如果飞书触发，获取环境变量
        if: ${{ github.event_name == 'workflow_dispatch' && github.event.action == 'feishu trigger' }}
        run: |
          echo "AGENT_VERSION=${{ github.event.client_payload.agent_version }}" >> $GITHUB_ENV
          echo "DFS_BRANCH=${{ github.event.client_payload.dfs_branch }}" >> $GITHUB_ENV
          echo "JOB_ID=${{ github.event.client_payload.job_id }}" >> $GITHUB_ENV
      - name: 如果Github页面手动触发，获取环境变量
        if: ${{ github.event_name == 'workflow_dispatch' }}
        run: |
          echo "AGENT_VERSION=${{ github.event.inputs.AGENT_VERSION }}" >> $GITHUB_ENV
          echo "DFS_BRANCH=${{ github.event.inputs.DFS_BRANCH }}" >> $GITHUB_ENV
          echo "JOB_ID=${{ github.event.inputs.JOB_ID }}" >> $GITHUB_ENV
      - name: ${{ env.JOB_ID }}
        if: ${{ github.event_name == 'repository_dispatch' && github.event.action == 'feishu trigger' }}
        run: echo run identifier ${{ github.event.client_payload.id }}
      - name: 拉取Tapdata-Cloud代码
        uses: actions/checkout@v3
        with:
          repository: tapdata/tapdata-cloud
          ref: ${{ env.DFS_BRANCH }}
          fetch-depth: 0
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapdata-cloud
      - name: 同步镜像
        run: |
          cd tapdata-cloud && ./drs/build/build.sh -P dfs -v ${{ env.AGENT_VERSION }} -s true