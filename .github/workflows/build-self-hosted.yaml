name: OP 版本完整编译与基础集成-自托管运行器

on:
  workflow_dispatch:
    inputs:
      FRONTEND_BRANCH:
        description: "Input Frontend Branch: "
        required: true
        type: string
      OPENSOURCE_BRANCH:
        description: "Input Tapdata Branch: "
        required: true
        type: string
      LISENSE_BRANCH:
        description: "Input Tapdata-License Branch: "
        required: false
        type: string
        default: "main"
      IS_TAR:
        description: "Need Tar File: "
        required: false
        type: boolean
        default: false
      FRONTEND_MODE:
        description: 'Frontend Build Mode: '
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - msa
          - ikas
          - chowsangsang
      DEPLOY:
        description: 'Deploy to Env: '
        required: false
        default: "无"
        type: choice
        options:
          - "无"
          - "dev(3030)"
          - "new(3031)"
      TAG_NAME:
        description: 'Input Tag: '
        required: false
        type: string
        default: ""
      JOB_ID:
        description: 'JOB_ID: '
        required: false
        type: string
        default: ""
      FOR_AUTO_TEST:
        description: "For auto test, set to true: "
        required: true
        type: boolean
        default: false
      TAPDATA_PLATFORM_VERSION:
        description: "Tapdata Platform Version: "
        required: true
        type: choice
        default: 'Linux'
        options:
          - 'Linux'
          - "Windows"

env:
  REGISTRY: ghcr.io
  RELEASE_REPO: asia-docker.pkg.dev/crypto-reality-377106/tapdata
  QINGCLOUD_REPO: dockerhub.qingcloud.com/tapdata
  PRIVATE_REPO: *************:5000/tapdata
  PACKAGE_REPO: https://tapdata-generic.pkg.coding.net/tapdata/enterprise-tar/chunks
  PACKAGE_REPO_NO_CHUNK: https://tapdata-generic.pkg.coding.net/tapdata/enterprise-tar
  TEST_DATABASE: ${{ secrets.TEST_DATABASE }}
  MVN_SETTINGS: ${{ secrets.MVN_SETTINGS }}
  FEISHU_PERSON_IN_CHARGE: ${{ secrets.FEISHU_PERSON_IN_CHARGE }}
  FEISHU_CHAT_ID: ${{ secrets.FEISHU_CHAT_ID }}
  FEISHU_APP_ID: ${{ secrets.FEISHU_APP_ID}}
  FEISHU_APP_SECRET: ${{ secrets.FEISHU_APP_SECRET }}
  CODING_DOCKER_USERNAME: ${{ secrets.CODING_DOCKER_USERNAME }}
  CODING_DOCKER_PASSWORD: ${{ secrets.CODING_DOCKER_PASSWORD }}
  CODING_GENERIC_USERNAME: ${{ secrets.CODING_GENERIC_USERNAME }}
  CODING_GENERIC_PASSWORD: ${{ secrets.CODING_GENERIC_PASSWORD}}
  CODE_FOLDER: folder-a,folder-b,folder-c
  TAPDATA_APPLICATION: main

jobs:

  Set-Env:
    runs-on: ubuntu-latest
    if: ${{ github.event_name != 'repository_dispatch' || (github.event_name == 'repository_dispatch' && (github.event.client_payload.enterprise_or_cloud == 'both' || github.event.client_payload.enterprise_or_cloud == 'enterprise')) }}
    outputs:
      OPENSOURCE_BRANCH: ${{ steps.set-outputs.outputs.OPENSOURCE_BRANCH }}
      CONNECTOR_BRANCH: ${{ steps.set-outputs.outputs.CONNECTOR_BRANCH }}
      LISENSE_BRANCH: ${{ steps.set-outputs.outputs.LISENSE_BRANCH }}
      CURRENT_BRANCH: ${{ steps.set-outputs.outputs.CURRENT_BRANCH }}
      FRONTEND_BRANCH: ${{ steps.set-outputs.outputs.FRONTEND_BRANCH }}
      FRONTEND_MODE: ${{ steps.set-outputs.outputs.FRONTEND_MODE }}
      DEPLOY: ${{ steps.set-outputs.outputs.DEPLOY }}
      JOB_ID: ${{ steps.set-outputs.outputs.JOB_ID }}
      IS_TAR: ${{ steps.set-outputs.outputs.IS_TAR }}
      TAG_NAME: ${{ steps.set-outputs.outputs.TAG_NAME }}
      FOR_AUTO_TEST: ${{ steps.set-outputs.outputs.FOR_AUTO_TEST }}
    timeout-minutes: 5
    steps:
      - name: Set Env if Workflow_dispatch
        if: ${{ github.event_name == 'workflow_dispatch' }}
        run: |
          job_id=$(tr -dc 'a-zA-Z0-9' < /dev/urandom | head -c 10)
          echo "OPENSOURCE_BRANCH=${{ inputs.OPENSOURCE_BRANCH }}" >> $GITHUB_ENV
          echo "LISENSE_BRANCH=${{ inputs.LISENSE_BRANCH }}" >> $GITHUB_ENV
          echo "CURRENT_BRANCH=${{ github.ref_name }}" >> $GITHUB_ENV
          echo "FRONTEND_BRANCH=${{ inputs.FRONTEND_BRANCH }}" >> $GITHUB_ENV
          echo "FRONTEND_MODE=${{ inputs.FRONTEND_MODE }}" >> $GITHUB_ENV
          echo "DEPLOY=${{ inputs.DEPLOY }}" >> $GITHUB_ENV
          echo "IS_TAR=${{ inputs.IS_TAR }}" >> $GITHUB_ENV
          echo "JOB_ID=${{ inputs.JOB_ID }}" >> $GITHUB_ENV
          echo "FOR_AUTO_TEST=${{ inputs.FOR_AUTO_TEST }}" >> $GITHUB_ENV
      - name: Set env if repository_dispatch
        if: ${{ github.event_name == 'repository_dispatch' }}
        run: |
          echo "OPENSOURCE_BRANCH=${{ github.event.client_payload.opensource_branch }}" >> $GITHUB_ENV
          echo "LISENSE_BRANCH=${{ github.event.client_payload.lisense_branch }}" >> $GITHUB_ENV
          echo "CURRENT_BRANCH=${{ github.event.client_payload.current_branch }}" >> $GITHUB_ENV
          echo "FRONTEND_BRANCH=${{ github.event.client_payload.frontend_branch }}" >> $GITHUB_ENV
          echo "FRONTEND_MODE=${{ github.event.client_payload.frontend_mode }}" >> $GITHUB_ENV
          echo "DEPLOY=${{ github.event.client_payload.deploy }}" >> $GITHUB_ENV
          echo "IS_TAR=${{ github.event.client_payload.is_package_tar }}" >> $GITHUB_ENV
          echo "JOB_ID=${{ github.event.client_payload.id }}" >> $GITHUB_ENV
          echo "FOR_AUTO_TEST=${{ github.event.client_payload.for_auto_test }}" >> $GITHUB_ENV
      - name: Checkout Tapdata Code
        uses: actions/checkout@v3
        with:
          repository: 'tapdata/tapdata'
          ref: ${{ env.OPENSOURCE_BRANCH }}
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapdata
          fetch-depth: 0
      - name: Set Tag
        run: |
          if [[ "${{ inputs.TAG_NAME }}" != "" ]]; then
            echo "TAG_NAME=${{ inputs.TAG_NAME }}" >> $GITHUB_ENV
          else
            cd tapdata
            main_tag=$(git branch --show-current | cut -d '-' -f 2)
            current_timestamp=$(date +%s)
            hex_timestamp=$(printf "%X" "$current_timestamp" | tr 'A-F' 'a-f')
            tag_name="$main_tag-$hex_timestamp"
            echo "TAG_NAME=$tag_name" >> $GITHUB_ENV
          fi
      - name: Get Connector Branch
        run: |
          cd tapdata
          opensource_branch=$(git rev-parse --abbrev-ref HEAD)
          echo "tapdata分支为：$opensource_branch"
          version=$(echo $opensource_branch | grep -o -E '[0-9]+\.[0-9]+\.[0-9]+' || echo $opensource_branch)
          version_as_number=$(echo $version | tr -d '.')
          if [[ ${{ env.FOR_AUTO_TEST }} == "true" ]]; then
            connector_branch="develop"
          elif (( version_as_number >= 352 && version_as_number <= 355 )); then
            connector_branch="release-v1.2.6"
          elif (( version_as_number >= 356 && version_as_number <= 3511 )); then
            connector_branch="release-v1.3.2"
          else
            connector_branch="main"
          fi
          echo "connectors分支为：$connector_branch"
          echo "CONNECTOR_BRANCH=$connector_branch" >> $GITHUB_ENV
      - name: Set Output
        id: set-outputs
        run: |
          echo "::set-output name=OPENSOURCE_BRANCH::${{ env.OPENSOURCE_BRANCH }}"
          echo "::set-output name=CONNECTOR_BRANCH::${{ env.CONNECTOR_BRANCH }}"
          echo "::set-output name=LISENSE_BRANCH::${{ env.LISENSE_BRANCH }}"
          echo "::set-output name=OPENSOURCE_CONNECTORS::${{ env.OPENSOURCE_CONNECTORS }}"
          echo "::set-output name=ENTERPRISE_CONNECTORS::${{ env.ENTERPRISE_CONNECTORS }}"
          echo "::set-output name=CURRENT_BRANCH::${{ env.CURRENT_BRANCH }}"
          echo "::set-output name=FRONTEND_BRANCH::${{ env.FRONTEND_BRANCH }}"
          echo "::set-output name=FRONTEND_MODE::${{ env.FRONTEND_MODE }}"
          echo "::set-output name=DEPLOY::${{ env.DEPLOY }}"
          echo "::set-output name=IS_TAR::${{ env.IS_TAR }}"
          echo "::set-output name=TAG_NAME::${{ env.TAG_NAME }}"
          echo "::set-output name=JOB_ID::${{ env.JOB_ID }}"
          echo "::set-output name=FOR_AUTO_TEST::${{ env.FOR_AUTO_TEST }}"
      - name: ${{ env.JOB_ID }}
        if: ${{ github.event_name == 'repository_dispatch' || env.JOB_ID != '' }}
        run: echo run identifier ${{ env.JOB_ID }}
      - name: Tag=${{ env.TAG_NAME }};CURRENT_BRANCH=${{ env.CURRENT_BRANCH }};OPENSOURCE_BRANCH=${{ env.OPENSOURCE_BRANCH }};FRONTEND_BRANCH=${{ env.FRONTEND_BRANCH }}
        run: |
          echo "success"

  Push-Code-To-GOGS:
    uses: tapdata/tapdata-application/.github/workflows/sync-code-to-office.yaml@main
    needs: Set-Env
    with:
      tapdata: ${{ needs.Set-Env.outputs.OPENSOURCE_BRANCH }}
      tapdata-license: ${{ needs.Set-Env.outputs.LISENSE_BRANCH }}
      tapdata-enterprise: ${{ needs.Set-Env.outputs.CURRENT_BRANCH }}
      tapdata-connectors: ${{ needs.Set-Env.outputs.CONNECTOR_BRANCH }}
      tapdata-connectors-enterprise: ${{ needs.Set-Env.outputs.CONNECTOR_BRANCH }}
      tapdata-enterprise-web: ${{ needs.Set-Env.outputs.FRONTEND_BRANCH }}
    secrets:
      TAPDATA_ENT_CICD_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}

  Build-Tapdata:
    runs-on: office-build
    needs:
      - Set-Env
      - Push-Code-To-GOGS
    timeout-minutes: 40
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Checkout Tapdata Enterprise Code
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ needs.Set-Env.outputs.CURRENT_BRANCH }} --single-branch ssh://git@**************:10022/tapdata/tapdata-enterprise.git
          cd tapdata-enterprise && git fetch --tags
      - name: Checkout Tapdata Code
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ needs.Set-Env.outputs.OPENSOURCE_BRANCH }} --single-branch ssh://git@**************:10022/tapdata/tapdata.git
          cd tapdata && git fetch --tags
      - name: Get Tapdata Version
        run: |
          cd tapdata && tapdata_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata: ${{ needs.Set-Env.outputs.OPENSOURCE_BRANCH }} $tapdata_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Checkout Tapdata License
        run: |
            export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
            git clone -b ${{ needs.Set-Env.outputs.LISENSE_BRANCH }} --single-branch ssh://git@**************:10022/tapdata/tapdata-license.git
      - name: Get Tapdata License Version
        run: |
          cd tapdata-license && tapdata_license_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata-license: ${{ needs.Set-Env.outputs.LISENSE_BRANCH }} $tapdata_license_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-license.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Checkout Tapdata Application
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ env.TAPDATA_APPLICATION }} --single-branch ssh://git@**************:10022/tapdata/tapdata-application.git
      - name: Patch Maven Dependens
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Build Tapdata - And Analyze
        env:
          GITHUB_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          cd tapdata-application && bash build/build.sh -c tapdata -P true -u false -l "-Dmaven.compile.fork=true -P idaas -P enterprise"
      - name: Build Tapdata Lisence
        run: |
          cd tapdata-application && bash build/build.sh -c tapdata-license -d true -P true -u false
      - name: Upload Tapdata Component
        run: |
          cd tapdata-application && bash build/build.sh -k tapdata
          temp_dir=temp/${{ needs.Set-Env.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          cp -r output/* $temp_dir/
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/
          bash build/build.sh -d true
          

  Build-Connectors:
    runs-on: office-build
    needs:
      - Push-Code-To-GOGS
      - Set-Env
    timeout-minutes: 50
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Checkout Tapdata Application
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ env.TAPDATA_APPLICATION }} --single-branch ssh://git@**************:10022/tapdata/tapdata-application.git
      - name: Checkout Tapdata Code
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ needs.Set-Env.outputs.OPENSOURCE_BRANCH }} --single-branch ssh://git@**************:10022/tapdata/tapdata.git
          cd tapdata && git fetch --tags
      - name: Checkout Connectors Code
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ needs.Set-Env.outputs.CONNECTOR_BRANCH }} --single-branch ssh://git@**************:10022/tapdata/tapdata-connectors.git
          cd tapdata-connectors && git fetch --tags
      - name: Get Connectors Version
        run: |
          cd tapdata-connectors && tapdata_connectors_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata-connectors: main $tapdata_connectors_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-connectors.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Checkout Enterprise Connectors Code
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ needs.Set-Env.outputs.CONNECTOR_BRANCH }} --single-branch ssh://git@**************:10022/tapdata/tapdata-connectors-enterprise.git
          cd tapdata-connectors-enterprise && git fetch --tags
      - name: Get Enterprise Connectors Version
        run: |
          cd tapdata-connectors-enterprise && tapdata_connectors_enterprise_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata-connectors-enterprise: main $tapdata_connectors_enterprise_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-connectors-enterprise.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Patch Maven Dependens
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Build Connectors
        run: |
          cd tapdata-application && bash build/build.sh -c opensource-connectors -u false -P true -l "-Dmaven.compile.fork=true"
      - name: Build Enterprise Connectors
        run: |
          cd tapdata-application && bash build/build.sh -c enterprise-connectors -u false -d true -P true -l "-Dmaven.compile.fork=true"
      - name: Upload Tapdata Component
        run: |
          if [[ "${{ needs.Set-Env.outputs.FOR_AUTO_TEST }}" == "true" ]]; then
            cd tapdata-application && bash build/build.sh -k connectors -i true
          else
            cd tapdata-application && bash build/build.sh -k connectors
          fi
          temp_dir=temp/${{ needs.Set-Env.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          cp -r output/* $temp_dir/
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/
          bash build/build.sh -d true

  Build-Tapdata-Enterprise:
    runs-on: office-build
    needs:
      - Push-Code-To-GOGS
      - Set-Env
    timeout-minutes: 30
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Checkout Tapdata Enterprise Code
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b main --single-branch ssh://git@**************:10022/tapdata/tapdata-enterprise.git
          cd tapdata-enterprise && git fetch --tags
      - name: Get Tapdata-Enterprise Version
        run: |
          cd tapdata-enterprise && tapdata_enterprise_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata-enterprise: ${{ needs.Set-Env.outputs.CURRENT_BRANCH }} $tapdata_enterprise_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-enterprise.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Checkout Tapdata Application
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ env.TAPDATA_APPLICATION }} --single-branch ssh://git@**************:10022/tapdata/tapdata-application.git
      - name: Patch Dependens
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --links --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/apiserver/ tapdata-enterprise/apiserver/node_modules/
          rsync --links --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata-agent/ tapdata-enterprise/tapdata-agent/node_modules/
          mkdir -p ~/.pkg-cache/v3.4/
          rsync -r --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/build-temp/ ~/.pkg-cache/v3.4/
      - name: Build Tapdata Enterprise
        run: |
          cd tapdata-application && bash build/build.sh -c tapdata-enterprise -u false -v ${{ inputs.TAPDATA_PLATFORM_VERSION }}
      - name: Upload Tapdata Component
        run: |
          cd tapdata-application && bash build/build.sh -k tapdata-enterprise -v ${{ inputs.TAPDATA_PLATFORM_VERSION }}
          temp_dir=temp/${{ needs.Set-Env.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          cp -r output/* $temp_dir/
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --links --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/
          bash build/build.sh -d true
          

#  Build-Common-Lib:
#    runs-on: gcp-docker
#    needs: Set-Env
#    steps:
#      - name: Clean Directory
#        run: |
#          rm -rf ./*
#      - name: Checkout Common Lib Code
#        uses: actions/checkout@v2
#        with:
#          fetch-depth: 0
#          repository: 'tapdata/tapdata-common-lib'
#          ref: "main"
#          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          path: tapdata-common-lib
#      - name: Get Common Lib Version
#        run: |
#          cd tapdata-common-lib && tapdata_common_lib_version=$(git rev-parse --short HEAD) && cd ..
#          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
#          echo "- tapdata-common-lib: main $tapdata_common_lib_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-common-lib.version
#          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
#          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
#      - name: Checkout Tapdata Application
#        uses: actions/checkout@v2
#        with:
#          repository: 'tapdata/tapdata-application'
#          ref: "main"
#          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          path: tapdata-application
#      - name: Patch Maven Dependens
#        run: |
#          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
#          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
#      - name: Build Common Lib
#        run: |
#          cd tapdata-application && bash build/build.sh -c common-lib -u false -d true
#      - name: Upload Tapdata Component
#        run: |
#          cd tapdata-application && bash build/build.sh -k common_lib
#          temp_dir=temp/${{ needs.Set-Env.outputs.TAG_NAME }}
#          mkdir -p $temp_dir
#          cp -r output/* $temp_dir/
#          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
#          rsync -r --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/
#          bash build/build.sh -d true
  
  Build-Frontend:
    runs-on: office-build
    needs:
      - Push-Code-To-GOGS
      - Set-Env
    timeout-minutes: 60
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Checkout Frontend Code
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ needs.Set-Env.outputs.FRONTEND_BRANCH }} --single-branch ssh://git@**************:10022/tapdata/tapdata-enterprise-web.git
          cd tapdata-enterprise-web && git fetch --tags
      - name: Get Frontend Version
        run: |
          cd tapdata-enterprise-web && tapdata_enterprise_web_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata-enterprise-web: ${{ needs.Set-Env.outputs.FRONTEND_BRANCH }} $tapdata_enterprise_web_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-enterprise-web.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Checkout Tapdata Application
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ env.TAPDATA_APPLICATION }} --single-branch ssh://git@**************:10022/tapdata/tapdata-application.git
      - name: Build Frontend
        env:
          DAAS_BUILD_NUMBER: ${{ needs.Set-Env.outputs.TAG_NAME }}
        run: |
          cd tapdata-application && bash build/build.sh -c tapdata-enterprise-web -u false -m ${{ needs.Set-Env.outputs.FRONTEND_MODE }} -t ${{ needs.Set-Env.outputs.TAG_NAME }}
      - name: Upload Tapdata Component
        run: |
          cd tapdata-application && bash build/build.sh -k web
          temp_dir=temp/${{ needs.Set-Env.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          cp -r output/* $temp_dir/
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/
          bash build/build.sh -d true

  Make-Docker-Image:
    runs-on: office-build
    timeout-minutes: 30
    needs:
      - Build-Tapdata
      - Build-Connectors
      - Build-Frontend
      - Build-Tapdata-Enterprise
      - Push-Code-To-GOGS
      - Set-Env
    permissions:
      contents: write
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Checkout Tapdata Application
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ env.TAPDATA_APPLICATION }} --single-branch ssh://git@**************:10022/tapdata/tapdata-application.git
      - name: Package and Push Image
        if: ${{ inputs.TAPDATA_PLATFORM_VERSION == 'Linux' }}
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          cd tapdata-application
          mkdir -p output
          rsync -r --links --progress --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/temp/${{ needs.Set-Env.outputs.TAG_NAME }}/ output/
          bash build/build.sh -o image -t ${{ needs.Set-Env.outputs.TAG_NAME }}
      - name: Print Image Info
        if: ${{ inputs.TAPDATA_PLATFORM_VERSION == 'Linux' }}
        run: |
          echo '**Google Docker Image:**' >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "harbor.internal.tapdata.io/tapdata/tapdata/tapdata-enterprise:${{ needs.Set-Env.outputs.TAG_NAME }}" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
      - name: Checkout Tapdata Application
        run: |
          rm -rf tapdata-application
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b version --single-branch ssh://git@**************:10022/tapdata/tapdata-application.git
      - name: Add Commit ID and Version Map
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --progress --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/version/${{ needs.Set-Env.outputs.TAG_NAME }}/ ./
          echo "## ${{ needs.Set-Env.outputs.TAG_NAME }}" >> tapdata-application/README.md
          cat tapdata.version >> tapdata-application/README.md
          cat tapdata-enterprise.version >> tapdata-application/README.md
          cat tapdata-enterprise-web.version >> tapdata-application/README.md
          cat tapdata-license.version >> tapdata-application/README.md
          cat tapdata-connectors.version >> tapdata-application/README.md
          cat tapdata-connectors-enterprise.version >> tapdata-application/README.md
          cat tapdata-common-lib.version >> tapdata-application/README.md || exit 0
      - uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: "Update README.md"
          branch: 'version'
          repository: 'tapdata-application'
          commit_user_name: 'cicd'
          commit_user_email: '<EMAIL>'

  Make-Tar-File:
    runs-on: office-build
    timeout-minutes: 30
    needs:
      - Build-Tapdata
      - Build-Connectors
      - Build-Frontend
      - Build-Tapdata-Enterprise
      - Push-Code-To-GOGS
      - Set-Env
    permissions:
      contents: write
    if: ${{ needs.Set-Env.outputs.IS_TAR == 'true' }}
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Checkout Tapdata Application
        run: |
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ env.TAPDATA_APPLICATION }} --single-branch ssh://git@**************:10022/tapdata/tapdata-application.git
      - name: Package and Push Tar File
        run: |
          cd tapdata-application
          mkdir -p output
          rsync -r --links --progress --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/temp/${{ needs.Set-Env.outputs.TAG_NAME }}/ output/
          bash build/build.sh -o tar -t ${{ needs.Set-Env.outputs.TAG_NAME }} -m ${{ needs.Set-Env.outputs.FRONTEND_MODE }}
      - name: Print Tar Info
        run: |
          file_name=${{ needs.Set-Env.outputs.FRONTEND_MODE }}-tapdata-enterprise-${{ needs.Set-Env.outputs.TAG_NAME }}.tar.gz
          echo '**Download Tar File:**' >> $GITHUB_STEP_SUMMARY
          echo "[Download Link](http://*************:5244/gz/$file_name)" >> $GITHUB_STEP_SUMMARY
          echo "username/password： **tapdata/Gotapd8!**" >> $GITHUB_STEP_SUMMARY

  Deploy:
    runs-on: office-build
    needs:
      - Make-Docker-Image
      - Set-Env
    timeout-minutes: 30
    if: ${{ (needs.Set-Env.outputs.DEPLOY != '' && needs.Set-Env.outputs.DEPLOY != '无') || needs.Set-Env.outputs.FOR_AUTO_TEST == 'true' }}
    steps:
      - name: Checkout Tapdata Application
        run: |
          rm -rf tapdata-application
          export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"
          git clone -b ${{ env.TAPDATA_APPLICATION }} --single-branch ssh://git@**************:10022/tapdata/tapdata-application.git
      - name: Deploy Auto Test Env
        if: ${{ needs.Set-Env.outputs.FOR_AUTO_TEST == 'true' }}
        run: |
          cd tapdata-application
          bash build/upgrade.sh --deploy=true --version=${{ needs.Set-Env.outputs.TAG_NAME }} --deploy-way=docker-compose
      - name: Deploy
        if: ${{ needs.Set-Env.outputs.DEPLOY != '' && needs.Set-Env.outputs.DEPLOY != '无' }}
        run: |
          cd tapdata-application
          if [[ '${{ needs.Set-Env.outputs.DEPLOY }}' == 'dev(3030)' ]]; then
            upgrade_env='tapdata-dev'
          elif [[ '${{ needs.Set-Env.outputs.DEPLOY }}' == 'new(3031)' ]]; then
            upgrade_env='tapdata-new'
          fi
          bash build/upgrade.sh --upgrade=true --version=${{ needs.Set-Env.outputs.TAG_NAME }} --upgrade-env=$upgrade_env --deploy-way=docker-compose

  Clean-Build-Temp:
    runs-on: office-build
    timeout-minutes: 30
    needs:
      - Make-Docker-Image
      - Make-Tar-File
      - Set-Env
    if: ${{ always() }}
    steps:
      - name: Clean Build Temp
        run: |
          mkdir -p temp/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --delete --password-file=/tmp/rsync.passwd temp/${{ needs.Set-Env.outputs.TAG_NAME }}/ rsync://root@*************:873/data/temp/${{ needs.Set-Env.outputs.TAG_NAME }}/
    
