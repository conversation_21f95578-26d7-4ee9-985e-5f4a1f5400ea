project_id                  = "hazelcast-33"
region                      = "us-central1"
zone                        = "us-central1-c"
member_count                = "2"
gcp_instance_type           = "f1-micro"
gcp_label_key               = "integration-test"
gcp_label_value             = "terraform"
hazelcast_mancenter_version = "5.1.2" # last known working version, tests use latest-snapshot
gcp_ssh_user                = "ubuntu"
hazelcast_path              = "~/lib/hazelcast.jar"
gcp_key_file                = "gcp_key_file.json"