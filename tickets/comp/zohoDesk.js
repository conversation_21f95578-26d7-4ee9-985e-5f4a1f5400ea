const axios = require("axios");
const config = require('../config/config.json');
const {saveData,getMongodb} = require('./mongodb');
let token;
async function getToken(){
    let now = new Date().getTime()
    const db = await getMongodb();
    if(!token){
        let rs = await db.collection("config").findOne({key:"zohoToken"});
        if(rs){
            token = rs;
        }
    }
    if(token && token.refreshTime && now < token.refreshTime){
        return;
    }
    if(token && token.refresh_token){
        let url = 'https://accounts.zoho.com.cn/oauth/v2/token?refresh_token='+token.refresh_token+'&grant_type=refresh_token&client_id='+config.zoho_client_id+'&client_secret='+config.zoho_client_secret+'&redirect_uri=https://redirect.tapdata.io/oauth/complete/zoho-crm';
        let response = await axios.post(url, {})
        Object.assign(token,response.data);
    }else{
        let url = 'https://accounts.zoho.com.cn/oauth/v2/token?grant_type=authorization_code&client_id='+config.zoho_client_id+'&client_secret='+config.zoho_client_secret+'&redirect_uri=https://redirect.tapdata.io/oauth/complete/zoho-crm&code='+config.zoho_code;
        let response = await axios.post(url, {})
        token = response.data;
    }

    token.refreshTime = now + (token.expires_in * 1000) - 5000;
    token.key = 'zohoToken';
    let set = {
        $set:token
    }
    await db.collection("config").updateOne({key:"zohoToken"},set,{upsert:true});;
}
let fields = [
    'subject',
    'createdTime',
    'priority',
    'id',
    'dueDate',
    'modifiedTime',
    'status'
]
async function getTickers(from,limit,cb){
    await getToken();
    let url = "https://desk.zoho.com.cn/api/v1/tickets?include=contacts&from="+from+"&limit="+limit + '&fields=' + fields.join(',');
    let response = await axios.get(url, {
        headers: {
            "Authorization": "Zoho-oauthtoken " + token.access_token,
            "Content-Type": "application/json",
            "Accept-Encoding": "*",
            "orgId":"41064196"
        }})
    if(response.data && response.data.data && response.data.data.length > 0){
        await cb(response.data.data)
        return true
    }
    return false;
}

async function createTickers(body){
    let data = {
        "subCategory": null,
        "productId": null,
        "contactId": "7223000003522582",
        "subject": body.subject,
        "customFields": {
            "缺陷标签": null,
            "Coding链接": null,
            "缺陷分类": "任务编排",
            "服务跟进人": null,
            "缺陷描述": null,
            "版本号": "v3",
            "备注": null,
            "转T3时间": null,
            "处理层级": "T1",
            "售后工时记录": null,
            "产品名称": "Tapdata云版",
            "项目简称": "国内云版用户",
            "T2支持评价": null,
            "支持分类": null,
            "T2协助者工时记录": null,
            "数据源ID": body.connectionId,
            "数据源名称": body.connectionName,
            "任务ID": body.jobId,
            "任务名称": body.jobName,
            "用户ID": body.userId,
            "用户名": body.userName
        },
        "cf": {
            "cf_wen_ti_fen_lei": null,
            "cf_shu_ju_yuan_ming_cheng": body.connectionName,
            "cf_ban_ben_hao": "v3",
            "cf_que_xian_biao_qian": null,
            "cf_chan_pin_ming_cheng": "Tapdata云版",
            "cf_t1xie_zhu_zhe_gong_shi_ji_lu": null,
            "cf_ren_wu_ming_cheng": body.jobName,
            "cf_coding_lian_jie": null,
            "cf_ke_hu_jian_cheng": "国内云版用户",
            "cf_chu_li_ceng_ji": "T1",
            "cf_zhuan_t2shi_jian": null,
            "cf_bei_zhu_1": null,
            "cf_ren_wu_id": body.jobId,
            "cf_t2zhi_chi_ping_jia": null,
            "cf_yong_hu_ming": body.userName,
            "cf_shu_ju_yuan_id": body.connectionId,
            "cf_que_xian_miao_shu": null,
            "cf_fu_wu_gen_jin_ren": null,
            "cf_gong_shi_ji_lu": null,
            "cf_que_xian_fen_lei": "任务编排",
            "cf_yong_hu_id": body.userId
        },
        "dueDate": null,
        "departmentId": "7223000000007057",
        "channel": "Web",
        "description": body.description,
        "priority": null,
        "classification": null,
        "assigneeId": null,
        "phone": body.phone,
        "category": "用户支持",
        "email": body.email,
        "status": "Open"
    };
    if(body.country !== 'China'){
        data.customFields.项目简称 = '海外云版用户';
        data.cf.cf_ke_hu_jian_cheng = '海外云版用户';
    }
    await getToken();
    let url = "https://desk.zoho.com.cn/api/v1/tickets";
    let response = await axios.post(url, data,{
        headers: {
            "Authorization": "Zoho-oauthtoken " + token.access_token,
            "Content-Type": "application/json",
            "Accept-Encoding": "*",
            "orgId":"41064196"
        }})
    if(response.data && response.data){
        response.data.userId = body.userId;
        response.data._id = response.data.id
        await saveData([response.data],'UserTickets');
        await createComment(response.data.id,body.country);
        return true
    }
    return false;
}

async function createComment(id,country){
    let data = {
        "isPublic": "true",
        "attachmentIds": [],
        "content": "感谢您的工单提交！我们已收到您的请求并正在处理中。我们的团队会尽快跟进并提供帮助。如果您有任何紧急问题，可以通过页面的【联系我们】添加我们的企业微信，直接联系我们的工程师来解决。"
    };
    if(country !== 'China'){
        data.content = "Thank you for submitting your service request! We have received your inquiry and it is currently being processed. Our team will follow up shortly and provide assistance. If you have any urgent matters, you can reach out to our engineers directly for resolution via the 'Join Slack' button on our website.";
    }
    await getToken();
    let url = "https://desk.zoho.com.cn/api/v1/tickets/"+id+"/comments";
    let response = await axios.post(url, data,{
        headers: {
            "Authorization": "Zoho-oauthtoken " + token.access_token,
            "Content-Type": "application/json",
            "Accept-Encoding": "*",
            "orgId":"41064196"
        }})
    if(response.data && response.data){
        return true
    }
    return false;
}

async function getTickersList(query){
    await getToken();
    if(query.userId && query.page && query.limit){
        const db = await getMongodb();
        let skip = (query.page -1) * query.limit;
        let where = {userId:query.userId};
        if(query.subject && query.subject !== ''){
            where.subject = new RegExp(query.subject);
        }
        let rs = await db.collection("UserTickets").find(where,{limit:query.limit * 1,skip:skip}).toArray();
        let count = await db.collection("UserTickets").count(where);
        return {code:'ok',data:{items:rs,total:count}};
    }
    return [];
}

async function getTicketAndComments(params){
    await getToken();
    let rs = {};
    if(params.id){
        const db = await getMongodb();
        rs = await db.collection("UserTickets").findOne({id:params.id});
        if(rs){
            rs.comments = await getComments(params.id);
            rs.connectionId = rs.customFields['数据源ID'];
            rs.connectionName = rs.customFields['数据源名称'];
            rs.jobId = rs.customFields['任务ID'];
            rs.jobName = rs.customFields['任务名称'];
        }else{
            rs = {};
        }
    }
    return {code:'ok',data:rs};
}

async function getComments(id){
    await getToken();
    let url = "https://desk.zoho.com.cn/api/v1/tickets/"+id+"/comments"
    let response = await axios.get(url, {
        headers: {
            "Authorization": "Zoho-oauthtoken " + token.access_token,
            "Content-Type": "application/json",
            "Accept-Encoding": "*",
            "orgId":"41064196"
        }})
    if(response.data && response.data.data){
        return response.data.data;
    }
    return false;
}

async function getTicketInfo(id){
    await getToken();
    let url = "https://desk.zoho.com.cn/api/v1/tickets/"+id
    let response = await axios.get(url, {
        headers: {
            "Authorization": "Zoho-oauthtoken " + token.access_token,
            "Content-Type": "application/json",
            "Accept-Encoding": "*",
            "orgId":"41064196"
        }})
    if(response.data){
        return response.data;
    }
    return false;
}

async function closeTicket(params){
    if(params && params.id){
        await getToken();
        let url = "https://desk.zoho.com.cn/api/v1/tickets/"+params.id
        let response = await axios.patch(url, {status:"Closed"},{
            headers: {
                "Authorization": "Zoho-oauthtoken " + token.access_token,
                "Content-Type": "application/json",
                "Accept-Encoding": "*",
                "orgId":"41064196"
            }})
        if(response.data){
            await saveData([{_id:params.id,status:"Closed"}],'UserTickets');
            return response.data;
        }
    }
    return false;
}

async function getAllTickers(cb){
    let from = 1;
    let limit = 10;
    let page = 1;
    let haveNext = true
    while (haveNext){
        console.info(new Date(),'page:',page)
        haveNext = await getTickers(from,limit,cb);
        from = from+limit;
        page++;
    }
}

function handelData(data) {
    if (!data)
        return data;
    for (let x in data) {
        if (typeof data[x] === 'object') {
            handelData(data[x]);
        }
        if(x.substring(0,1) === '$'){
            let newX = '_' + x.substring(1);
            data[newX] = data[x];
            delete data[x];
        }
    }
    return data;
}

exports.getAllTickers = getAllTickers;
exports.getComments = getComments;
exports.getTicketInfo = getTicketInfo;
exports.createTickers = createTickers;
exports.getTickersList = getTickersList;
exports.getTicketAndComments = getTicketAndComments;
exports.closeTicket = closeTicket;