const mongodb = require("mongodb");
const config = require('../config/config.json');
let db;
async function getMongoConn(){
    if(db){
        return db
    }
    const Conn = await mongodb.MongoClient.connect(config.mongodb_URI, {useUnifiedTopology: true})
    db = Conn.db();
    return db;
}

async function saveData(data,table){
    await getMongoConn()
    if(!db){
        return false;
    }
    for(let x in data){
        let set = {
            $set:data[x]
        }
        await db.collection(table).updateOne({_id:data[x]._id},set,{upsert:true});
    }
}

exports.saveData = saveData
exports.getMongodb = getMongoConn