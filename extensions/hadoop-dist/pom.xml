<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2021 Hazelcast Inc.
  ~
  ~ Licensed under the Hazelcast Community License (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://hazelcast.com/hazelcast-community-license
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <name>hazelcast-jet-hadoop-dist</name>
    <description>Common parent for file connector modules built on top of Hadoop module</description>

    <artifactId>hazelcast-jet-hadoop-dist</artifactId>

    <parent>
        <groupId>com.hazelcast.jet</groupId>
        <artifactId>hazelcast-jet-extensions</artifactId>
        <version>5.5.0</version>
    </parent>

    <properties>
        <main.basedir>${project.basedir}/../../..</main.basedir>
    </properties>

    <modules>
        <module>files-azure</module>
        <module>files-gcs</module>
        <module>files-s3</module>
        <module>hadoop</module>
        <module>hadoop-all</module>
    </modules>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <configuration>
                        <!--
                        We actually don't shade these in here in Hadoop module, they are already part of core
                        But we need to relocate the usage in the Hadoop module
                        -->
                        <relocations>
                            <relocation>
                                <pattern>com.fasterxml.jackson.jr</pattern>
                                <shadedPattern>com.hazelcast.com.fasterxml.jackson.jr</shadedPattern>
                            </relocation>
                            <relocation>
                                <pattern>com.fasterxml.jackson.core</pattern>
                                <shadedPattern>com.hazelcast.com.fasterxml.jackson.core</shadedPattern>
                            </relocation>
                            <relocation>
                                <pattern>com.fasterxml.jackson.databind</pattern>
                                <shadedPattern>com.hazelcast.com.fasterxml.jackson.databind</shadedPattern>
                            </relocation>
                            <relocation>
                                <pattern>com.fasterxml.jackson.dataformat</pattern>
                                <shadedPattern>com.hazelcast.com.fasterxml.jackson.dataformat</shadedPattern>
                            </relocation>
                        </relocations>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <configuration>
                    <includedLicenses combine.children="append">
                        <includedLicense>The Go license</includedLicense>
                        <includedLicense>CDDL</includedLicense>
                        <includedLicense>CDDL 1.1</includedLicense>
                        <includedLicense>Eclipse Distribution License - v 1.0</includedLicense>
                    </includedLicenses>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.hazelcast.jet</groupId>
            <artifactId>hazelcast-jet-hadoop-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro</artifactId>
            <version>${avro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro-mapred</artifactId>
            <version>${avro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.parquet</groupId>
            <artifactId>parquet-avro</artifactId>
            <version>${parquet.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <scope>provided</scope>
        </dependency>

    </dependencies>
</project>
