{"name": "@tap/dag", "version": "1.0.0", "description": "", "keywords": [], "license": "ISC", "author": "", "main": "src/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@figmania/anim": "^2.3.12", "@figmania/webcomponent": "^2.3.12", "@formily/core": "^2.0.9", "@formily/element-plus": "^2.0.9", "@formily/path": "^2.0.12", "@formily/reactive": "^2.0.9", "@formily/reactive-vue": "*", "@tap/api": "workspace:^1.0.0", "@tap/assets": "workspace:^", "@tap/business": "workspace:^1.0.0", "@tap/component": "workspace:^1.0.0", "@tap/form": "workspace:^1.0.0", "@tap/i18n": "workspace:^1.0.0", "@tap/shared": "workspace:^1.0.0", "@vueuse/core": "^10.5.0", "axios": "^0.21.1", "core-js": "^3.8.3", "dagre": "^0.8.5", "dayjs": "^1.11.2", "jsplumb": "^2.15.6", "mousetrap": "^1.6.5", "resize-observer-polyfill": "^1.5.1", "vue": "^3.0.0", "vue-json-pretty": "^1.9.5", "vue-request": "^2.0.4", "vue-virtual-scroller": "2.0.0-beta.8", "vuex": "^4.0.2"}}