.merge-table-tree-space {
  border: 1px solid rgba(221, 221, 221, 0.4);
  max-height: calc(100vh - 120px);

  hr.v-divider {
    border-color: rgba(221, 221, 221, 0.4);
  }

  .formily-element-space-item {
    overflow-y: auto;
  }

  .el-tree {
    &-node {
      &__content {
        height: 30px;
        &:hover {
          background-color: rgba(47, 46, 63, 0.05);
        }

        .el-tree-node__expand-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          //width: 30px;
          //height: 30px;
          //margin-left: -4px;
        }

        .el-image {
          width: 20px;
          height: 20px;
          vertical-align: middle;
        }
      }

      &.is-current > .el-tree-node__content {
        background-color: #eef3ff;
      }

      &.is-drop-inner > .el-tree-node__content {
        background-color: #d0deff;
      }
      //rgba(47, 46, 63, 0.05)
    }

    &__drop-indicator {
      height: 2px;
      background-color: #2c65ff;
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        display: block;
        padding: 2px;
        border-radius: 100%;
        border: 2px solid #2c65ff;
        transform: translate(-100%, -50%);
      }
    }
  }

  .merge-table-tree-node {
    &-action {
      display: none;
    }

    &:hover .merge-table-tree-node-action {
      display: inline-flex;
    }
  }
}
