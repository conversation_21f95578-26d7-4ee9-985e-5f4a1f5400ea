.processor-field-mapping {
  flex: 1;
  height: 100%;
  overflow: hidden;
  .icon-error {
    color: red;
  }
  .icon-color {
    color: map.get($iconFillColor, normal);
  }
  .processor-ml-10 {
    padding-left: 10px;
  }
  .table__empty_img {
    width: 80px;
    height: 80px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .noData {
    font-size: 12px;
    color: map.get($bgColor, special);
  }
  .page__current {
    width: 22px;
    height: 22px;
    font-size: 14px;
    font-weight: 400;
    color: map.get($color, primary);
    line-height: 22px;
    background-color: map.get($bgColor, pageCount);
  }
  .task-form__text {
    display: inline-block;
    width: 130px;
    text-align: left;
  }
  .btn-rest {
    height: 28px;
    width: 28px;
  }
  .btn-refresh {
    padding: 0;
    height: 28px;
    width: 28px;
    min-width: 28px;
    font-size: 16px;
    &:hover,
    &.is-plain:focus:hover {
      border-color: map.get($color, primary);
      background-color: map.get($color, white);
    }
  }
  .task-form-body {
    display: flex;
    height: 80vh;
    border: 1px solid map.get($borderColor, light);
    border-radius: 4px;
    .task-form-left {
      padding-top: 8px;
      width: 200px;
      border-right: 1px solid map.get($borderColor, light);
      .table-name {
        font-size: 12px;
        color: map.get($fontColor, normal);
        font-weight: 500;
      }
      .line-height {
        height: 40px;
        line-height: 40px;
      }
    }
    .task-form-left__ul {
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      li {
        background: map.get($bgColor, white);
        border-left: 2px solid transparent;
        border-bottom: 1px solid map.get($borderColor, light);
        display: flex;
        padding: 10px;
        &:hover {
          background: map.get($bgColor, disactive);
          cursor: pointer;
          border-left-color: map.get($color, primary);
        }
        &.active {
          background: map.get($bgColor, disactive);
          border-left-color: map.get($color, primary);
          cursor: pointer;
        }
        .task-form-text-box {
          flex: 1;
          min-width: 0;
          .target {
            font-size: 12px;
            font-weight: 400;
            color: #ef9868;
            line-height: 20px;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .select {
            font-size: 12px;
            font-weight: 400;
            color: #86909c;
            line-height: 5px;
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
        }
      }
    }
    .main {
      display: flex;
      flex: 1;
      overflow: hidden;
      flex-direction: column;
      padding-top: 8px;
    }
    .col-new-field-name {
      max-width: 90px;
    }

    .rename-table-item-input {
      position: relative;
      width: 100%;
      height: 28px;
      line-height: 28px;
      outline: none;
      box-shadow: none;
      background: 0 0;
      color: inherit;
      border: 1px solid transparent;
      border-radius: 4px;
      transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

      &:hover,
      &:focus {
        border-color: map.get($color, primary);
      }
    }
    .row-deleted {
      color: map.get($color, disable);
    }
    .color-darkorange {
      color: darkorange;
    }
    .field-mapping__icon {
      color: map.get($color, primary);
    }
    .field-mapping-table__default_value {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      line-height: 15px;
    }
  }

  .field-mapping-table.el-table {
    .ck-cell-wrap .cell {
      padding: 0 !important;
      text-align: center;
    }
  }

  .table-checkbox-wrap {
    border-top: 1px solid #ebeef5;
  }

  .field-search-input-wrap {
    width: 180px;
  }

  .fields-toolbar {
    border-left: 1px solid #ebeef5;
  }
  /*.el-table__empty-block {
    height: 100% !important;
  }
  .el-table__header {
    .el-table__cell {
      border-right: 0;
      &.is-leaf {
        border-bottom: 0;
      }
      &:hover {
        border-right: 1px solid map.get($borderColor, light);
      }
    }
    th {
      color: map.get($fontColor, normal);
      font-weight: 500;
      white-space: nowrap;
      background-color: map.get($bgColor, normal);
    }
  }
  .el-table__body {
    td {
      color: map.get($fontColor, light);
    }
  }
  &:after {
    width: 0;
  }*/
}
