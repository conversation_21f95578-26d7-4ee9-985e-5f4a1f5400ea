import i18n from '@tap/i18n'
import { NodeType } from './extends/NodeType'

export class LogCollector extends NodeType {
  constructor() {
    super()
  }

  type = 'logCollector'

  minInputs = 0 // 最小输入个数
  maxInputs = 1 // 最小输入个数
  minOutputs = 0 // 最小输出个数
  maxOutputs = 1 // 最大输出个数

  // allowSource = false // 该节点不允许有源

  group = 'data'

  formSchema = {
    type: 'object',
    properties: {
      $inputs: {
        type: 'array',
        'x-display': 'hidden',
      },
      $outputs: {
        type: 'array',
        'x-display': 'hidden',
      },
      databaseType: {
        type: 'string',
        'x-display': 'hidden',
      },
      'connectionIds.0': {
        type: 'string',
        'x-display': 'hidden',
        'x-reactions': '{{useSyncConnection}}',
      },

      type: {
        type: 'string',
        'x-display': 'hidden',
      },

      tabs: {
        type: 'void',
        'x-component': 'FormTab',
        'x-component-props': {
          class: 'config-tabs',
          formTab: '{{formTab}}',
        },
        properties: {
          tab1: {
            type: 'void',
            'x-component': 'FormTab.TabPane',
            'x-component-props': {
              label: i18n.t('public_basic_settings'),
            },
            properties: {
              nameWrap: {
                type: 'void',
                title: i18n.t('public_node_name'),
                'x-decorator': 'FormItem',
                'x-decorator-props': {
                  asterisk: true,
                  feedbackLayout: 'none',
                },
                'x-component': 'FormFlex',
                'x-component-props': {
                  gap: 8,
                  align: 'start',
                },
                properties: {
                  name: {
                    type: 'string',
                    required: true,
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      style: {
                        flex: 1,
                      },
                    },
                    'x-component': 'Input',
                    'x-component-props': {
                      onChange: `{{() => { $values.attrs.hasNameEdited = true }}}`,
                    },
                  },

                  clipboardButton: {
                    type: 'void',
                    'x-component': 'ClipboardButton',
                    'x-component-props': {
                      tooltip: i18n.t('packages_dag_copy_node_id'),
                      finishTooltip: i18n.t('packages_dag_nodes_table_yifuzhi'),
                      content: '{{$values.id}}',
                    },
                  },
                },
              },

              sourceConfig: {
                type: 'void',
                'x-reactions': {
                  dependencies: ['$outputs'],
                  fulfill: {
                    state: {
                      display: '{{$deps[0].length > 0 ? "visible":"hidden"}}',
                    },
                  },
                },
                properties: {
                  SharedMiningTableInfo: {
                    type: 'void',
                    'x-decorator': 'FormItem',
                    'x-component': 'SharedMiningTableInfo',
                    'x-component-props': {
                      height: '280px',
                    },
                  },

                  nodeConfig: {
                    type: 'object',
                  },
                },
              },
            },
          },
        },
      },

      'attrs.connectionType': {
        type: 'string',
        'x-display': 'hidden',
      },
    },
  }
}
