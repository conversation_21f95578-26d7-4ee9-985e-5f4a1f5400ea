export default {
  packages_ldp_lineage: '血缘',
  packages_ldp_lineage_loading_tips: '双击节点可以下钻',
  packages_ldp_order_fully_managed_storage: '订购全托管存储',
  packages_ldp_connection_expired: '存储已失效，请重新设置',
  packages_ldp_view_lineage: '查看血缘',
  packages_ldp_table_comment: '表注释',
  packages_ldp_src_dashboard_anEsctui: '按Esc退出溯源场景',
  packages_ldp_src_tablepreview_querenshanchu: '确认删除？',
  packages_ldp_src_tablepreview_gaibiaojianghuicong: '该表将会从数据库里删除，操作后不可恢复',
  packages_ldp_src_tablepreview_jiancedaoyouren: '检测到有任务正在使用 {val1}，请删除所有相关任务后重试',
  packages_ldp_src_target_muqianzhichide: '目前支持的类型',
  packages_ldp_upgrade_storage: '升级存储',
  packages_ldp_data_hub_intro_title: '什么是实时数据平台?',
  packages_ldp_data_hub_intro_desc1:
    '数据中心允许组织连接不同的数据源，并将这些整合的数据从一个统一的、集中的位置提供给多个应用程序或用户。',
  packages_ldp_data_hub_intro_desc2:
    'Tapdata采用CDC技术从数据源同步数据，使用MongoDB / MongoDB Atlas作为中心存储，实现近乎实时的数据延迟体验。',
  packages_ldp_data_hub_intro_scene_title: '哪些应用场景可以使用实时数据平台?',
  packages_ldp_data_hub_intro_scene_single_view: '单一视图',
  packages_ldp_data_hub_intro_scene_single_view_sub: ' 产品 ｜ 客户 ｜ 订单',
  packages_ldp_data_hub_intro_scene_realtime: '实时',
  packages_ldp_data_hub_intro_scene_realtime_sub: '数据仪表盘  ｜  报表',
  packages_ldp_data_hub_intro_scene_api: '企业API服务',
  packages_ldp_data_hub_intro_scene_api_sub: '数据库到API',
  packages_ldp_data_hub_intro_how_do: '实时数据平台是如何工作的?',
  packages_ldp_data_hub_intro_how_do_step1: '配置数据中心存储',
  packages_ldp_data_hub_intro_how_do_step1_sub: '首先,配置一个数据存储, 我们建议使用MongoDB or TiDB 集群',
  packages_ldp_data_hub_intro_how_do_step2: '同步数据并合并',
  packages_ldp_data_hub_intro_how_do_step2_sub: '使用 Tapdata 的复制和转换功能，将数据从您的数据源同步到数据中心。',
  packages_ldp_data_hub_intro_how_do_step3: '发布API或发送到仪表板',
  packages_ldp_data_hub_intro_how_do_step3_sub: '发布API或发送到仪表板',
  packages_ldp_data_hub_subscribe: '订阅存储',
  page_title_data_hub: '实时数据平台',
  packages_ldp_source_empty_text: '1. 创建源数据库',
  packages_ldp_target_empty_text: '2. 创建目标数据库',
  packages_ldp_not_support_increments: '当前源数据不支持增量',
  packages_ldp_drag_source_table_to_start: '将源表拖入此处开始复制',
  packages_ldp_run_only_once: '仅运行一次',
  packages_ldp_run_every_10_minutes: '每10分钟运行一次',
  packages_ldp_run_every_hour: '每1小时运行一次',
  packages_ldp_run_every_day: '每天运行一次',
  packages_ldp_custom_cron_expression: '自定义cron表达式',
  packages_ldp_view_task_monitor: '查看任务监控',
  packages_ldp_book_demo: '预约演示',
  packages_ldp_mdm_create_method: '创建方式',
  packages_ldp_mdm_create_method_transformation: '使用数据转换任务',
  packages_ldp_mdm_create_method_materialized: '使用建模向导'
}
