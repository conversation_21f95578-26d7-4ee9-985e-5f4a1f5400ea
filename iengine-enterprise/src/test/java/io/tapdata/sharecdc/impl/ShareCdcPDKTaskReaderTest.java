package io.tapdata.sharecdc.impl;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.ringbuffer.Ringbuffer;
import com.tapdata.constant.ClientOperatorUtil;
import com.tapdata.constant.ConfigurationCenter;
import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.HazelcastUtil;
import com.tapdata.entity.Connections;
import com.tapdata.entity.OperationType;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.mongo.HttpClientMongoOperator;
import com.tapdata.tm.commons.dag.DAG;
import com.tapdata.tm.commons.dag.nodes.TableNode;
import com.tapdata.tm.commons.externalStorage.ExternalStorageDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.shareCdcTableMapping.ShareCdcTableMappingDto;
import io.tapdata.commom.StoreLoggerImpl;
import io.tapdata.common.SettingService;
import io.tapdata.common.sharecdc.EventType;
import io.tapdata.common.sharecdc.ShareCdcUtil;
import io.tapdata.construct.ConstructIterator;
import io.tapdata.construct.constructImpl.ConstructRingBuffer;
import io.tapdata.entity.codec.FromTapValueCodec;
import io.tapdata.entity.codec.TapCodecsRegistry;
import io.tapdata.entity.codec.filter.TapCodecsFilterManager;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.schema.value.*;
import io.tapdata.flow.engine.V2.sharecdc.ShareCdcContext;
import io.tapdata.flow.engine.V2.sharecdc.ShareCdcTaskContext;
import io.tapdata.flow.engine.V2.sharecdc.ShareCdcTaskPdkContext;
import io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException;
import io.tapdata.flow.engine.V2.util.ExternalStorageUtil;
import io.tapdata.flow.engine.V2.util.TapCodecUtil;
import io.tapdata.loglistener.TapLogger;
import io.tapdata.pdk.core.api.ConnectorNode;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.*;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.util.ReflectionTestUtils;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.data.mongodb.core.query.Criteria.where;

public class ShareCdcPDKTaskReaderTest {
	TaskDto taskDto;
	String tableName;

	TapLogger obsLogger;
	ShareCdcPDKTaskReader shareCdcPDKTaskReader;

	ShareCdcContext shareCdcContext;

	@BeforeEach
	public void init() {
		shareCdcPDKTaskReader = mock(ShareCdcPDKTaskReader.class);
		taskDto = new TaskDto();
		taskDto.setName("test");
		ReflectionTestUtils.setField(shareCdcPDKTaskReader, "logCollectorTaskDto", taskDto);
		when(shareCdcPDKTaskReader.logWrapper(anyInt(), any())).thenCallRealMethod();

		shareCdcContext = mock(ShareCdcTaskPdkContext.class);
		ReflectionTestUtils.setField(shareCdcPDKTaskReader, "shareCdcContext", shareCdcContext);
		when(((ShareCdcTaskPdkContext) shareCdcContext).getConnections()).thenReturn(new Connections());
		when(((ShareCdcTaskPdkContext) shareCdcContext).getTaskDto()).thenReturn(taskDto);

		ClientMongoOperator clientMongoOperator = mock(ClientMongoOperator.class);
		ReflectionTestUtils.setField(shareCdcPDKTaskReader, "clientMongoOperator", clientMongoOperator);

		tableName = "testConstruct";
		obsLogger = mock(TapLogger.class);
		ReflectionTestUtils.setField(shareCdcPDKTaskReader, "obsLogger", obsLogger);

		HazelcastInstance hazelcastInstance = mock(HazelcastInstance.class);
		ReflectionTestUtils.setField(shareCdcPDKTaskReader, "hazelcastInstance", hazelcastInstance);

		Query query = Query.query(where("sign").is("null_" + tableName));
		when(clientMongoOperator.findOne(query, ConnectorConstant.SHARE_CDC_TABLE_MAPPING_COLLECTION,
				ShareCdcTableMappingDto.class)).thenReturn(new ShareCdcTableMappingDto());

	}

	@Test
	public void getConstructForRocksdbTest() {
		ExternalStorageDto logCollectorExternalStorage = new ExternalStorageDto();
		logCollectorExternalStorage.setType("rocksdb");
		logCollectorExternalStorage.setUri("/data/test");
		ReflectionTestUtils.setField(shareCdcPDKTaskReader, "logCollectorExternalStorage", logCollectorExternalStorage);
		try (MockedStatic<ExternalStorageUtil> data = Mockito
				.mockStatic(ExternalStorageUtil.class)) {
			data.when(() -> ExternalStorageUtil.initHZRingBufferStorage(logCollectorExternalStorage,
					null, "null_" + tableName, null, new StoreLoggerImpl(obsLogger))).thenAnswer((Answer<Void>) invocation -> null);
			when(shareCdcPDKTaskReader.getConstruct(anyString())).thenCallRealMethod();
			ConstructRingBuffer<Document> constructRingBuffer = shareCdcPDKTaskReader.getConstruct(tableName);
			String actualData = (String) ReflectionTestUtils.getField(constructRingBuffer, "name");
			String exceptData = ShareCdcUtil.getConstructName(taskDto, tableName);
			assertEquals(exceptData, actualData);
		}
	}

	@Test
	public void getConstructForMongoDbTest() {

		ExternalStorageDto logCollectorExternalStorage = new ExternalStorageDto();
		logCollectorExternalStorage.setType("mongodb");
		logCollectorExternalStorage.setUri("/data/test");
		ReflectionTestUtils.setField(shareCdcPDKTaskReader, "logCollectorExternalStorage", logCollectorExternalStorage);
		try (MockedStatic<ExternalStorageUtil> data = Mockito
				.mockStatic(ExternalStorageUtil.class)) {
			data.when(() -> ExternalStorageUtil.initHZRingBufferStorage(logCollectorExternalStorage,
					null, "null_" + tableName, null, new StoreLoggerImpl(obsLogger))).thenAnswer((Answer<Void>) invocation -> null);
			when(shareCdcPDKTaskReader.getConstruct(anyString())).thenCallRealMethod();
			ConstructRingBuffer<Document> constructRingBuffer = shareCdcPDKTaskReader.getConstruct(tableName);
			String actualData = (String) ReflectionTestUtils.getField(constructRingBuffer, "name");
			String exceptData = ShareCdcUtil.getConstructName(taskDto, tableName) + "_" + ((ShareCdcTaskPdkContext) shareCdcContext).getTaskDto().getName();
			assertEquals(exceptData, actualData);
		}
	}

	@DisplayName("test shareCdcPDKTaskReader init")
	@Test
	void testInit() throws ShareCdcUnsupportedException {
		try (MockedStatic<ClientOperatorUtil> clientOperatorUtilMockedStatic = mockStatic(ClientOperatorUtil.class);
			 MockedStatic<ShareCdcUtil> shareCdcUtilMockedStatic = mockStatic(ShareCdcUtil.class);
			 MockedStatic<HazelcastUtil> hazelcastUtilMockedStatic = mockStatic(HazelcastUtil.class)) {
			clientOperatorUtilMockedStatic.when(() -> ClientOperatorUtil.buildHttpClientMongoOperator(any())).thenReturn(mock(HttpClientMongoOperator.class));
			shareCdcUtilMockedStatic.when(() -> ShareCdcUtil.shareCdcEnable(any())).thenReturn(true);
			hazelcastUtilMockedStatic.when(() -> HazelcastUtil.getInstance(any())).thenReturn(mock(HazelcastInstance.class));

			shareCdcPDKTaskReader = mock(ShareCdcPDKTaskReader.class);
			when(shareCdcPDKTaskReader.canShareCdc(0)).thenReturn(10);

			ConfigurationCenter configurationCenter = new ConfigurationCenter();
			TaskDto taskDto = new TaskDto();

			TableNode tableNode = new TableNode();
			DAG dag = mock(DAG.class);
			tableNode.setDag(dag);
			when(dag.getTaskId()).thenReturn(new ObjectId("65ae2d427f580c320ec3bc65"));
			tableNode.getTaskId();

			Connections connections = new Connections();
			ConnectorNode connectorNode = mock(ConnectorNode.class);
			ShareCdcTaskPdkContext shareCdcTaskContext = new ShareCdcTaskPdkContext(System.currentTimeMillis(), configurationCenter, taskDto, tableNode, connections, connectorNode);
			shareCdcTaskContext.setObsLogger(mock(TapLogger.class));
			doCallRealMethod().when(shareCdcPDKTaskReader).init(shareCdcTaskContext);
			shareCdcPDKTaskReader.init(shareCdcTaskContext);
		}
	}

	@Nested
	class handleDataTest {

		private TapCodecsFilterManager engineCodecsFilterManger;

		@BeforeEach
		void setUp() {
			when(shareCdcPDKTaskReader.handleData(any())).thenCallRealMethod();
			engineCodecsFilterManger = TapCodecUtil.createEngineCodecsFilterManger();
			ReflectionTestUtils.setField(shareCdcPDKTaskReader, "engineCodecsFilterManger", engineCodecsFilterManger);
		}

		@Test
		void testObjectId() {
			ObjectId objectId = new ObjectId();
			byte[] bytes = objectId.toString().getBytes();
			byte[] dest = new byte[bytes.length + 2];
			dest[0] = 99;
			dest[dest.length - 1] = 23;
			System.arraycopy(bytes, 0, dest, 1, bytes.length);
			Object result = shareCdcPDKTaskReader.handleData(dest);
			assertInstanceOf(TapStringValue.class, result);
			assertEquals(objectId.toHexString(), ((TapStringValue) result).getValue());
		}

		@Test
		void testDate() {
			LocalDateTime localDateTime = LocalDateTime.of(2024, 5, 16, 0, 0);
			DateTime dateTime = new DateTime(localDateTime);
			TapDateValue tapDateValue = new TapDateValue(dateTime);
			FromTapValueCodec<TapDateValue> customFromTapValueCodec = engineCodecsFilterManger.getCodecsRegistry().getCustomFromTapValueCodec(TapDateValue.class);
			Object result = customFromTapValueCodec.fromTapValue(tapDateValue);
			result = shareCdcPDKTaskReader.handleData(result);
			assertInstanceOf(TapDateValue.class, result);
			assertEquals(tapDateValue.getValue().getSeconds(), ((TapDateValue) result).getValue().getSeconds());
		}

		@Test
		void testDateTime() {
			LocalDateTime localDateTime = LocalDateTime.of(2024, 5, 16, 18, 43, 57);
			DateTime dateTime = new DateTime(localDateTime);
			TapDateTimeValue tapDateTimeValue = new TapDateTimeValue(dateTime);
			FromTapValueCodec<TapDateTimeValue> customFromTapValueCodec = engineCodecsFilterManger.getCodecsRegistry().getCustomFromTapValueCodec(TapDateTimeValue.class);
			Object result = customFromTapValueCodec.fromTapValue(tapDateTimeValue);
			result = shareCdcPDKTaskReader.handleData(result);
			assertInstanceOf(Instant.class, result);
			assertEquals(tapDateTimeValue.getValue().getSeconds(), ((Instant) result).getEpochSecond());
		}

		@Test
		void testToTapValueCodecIsNull() {
			TapCodecsRegistry tapCodecsRegistry = mock(TapCodecsRegistry.class);
			when(tapCodecsRegistry.getCustomToTapValueCodec(byte[].class)).thenReturn(null);
			engineCodecsFilterManger = spy(engineCodecsFilterManger);
			doReturn(tapCodecsRegistry).when(engineCodecsFilterManger).getCodecsRegistry();
			ReflectionTestUtils.setField(shareCdcPDKTaskReader, "engineCodecsFilterManger", engineCodecsFilterManger);
			byte[] bytes = "testtesttest".getBytes(StandardCharsets.UTF_8);
			Object result = shareCdcPDKTaskReader.handleData(bytes);
			assertSame(bytes, result);
		}

		@Test
		void testInstant() {
			byte[] bytes = new byte[]{98, 50, 48, 50, 53, 45, 48, 50, 45, 49, 57, 84, 49, 57, 58, 50, 54, 58, 53, 50, 46, 51, 50, 52, 51, 56, 56, 90, 22};
			Object result = shareCdcPDKTaskReader.handleData(bytes);
			assertInstanceOf(TapDateTimeValue.class, result);
			DateTime value = ((TapDateTimeValue) result).getValue();
			assertEquals("2025-02-19T19:26:52.324388Z", value.toInstant().toString());
		}
	}

	@Nested
	@DisplayName("Method tapEventWrapper test")
	class tapEventWrapperTest {
		@BeforeEach
		void setUp() {
			when(shareCdcPDKTaskReader.tapEventWrapper(any())).thenCallRealMethod();
		}

		@Test
		@DisplayName("test insert event")
		void testInsertEvent() {
			Document document = new Document("after", new Document("_str", "test"))
					.append("op", OperationType.INSERT.getOp())
					.append("timestamp", System.currentTimeMillis());

			try (
					MockedStatic<ShareCdcBaseReader> shareCdcBaseReaderMockedStatic = mockStatic(ShareCdcBaseReader.class);
					MockedStatic<ShareCdcUtil> shareCdcUtilMockedStatic = mockStatic(ShareCdcUtil.class)
			) {
				shareCdcBaseReaderMockedStatic.when(() -> ShareCdcBaseReader.logDocumentVerify(document)).thenAnswer(invocationOnMock -> null);
				ShareCdcBaseReader.ShareCDCReaderEvent shareCDCReaderEvent = shareCdcPDKTaskReader.tapEventWrapper(document);
				TapEvent tapEvent = shareCDCReaderEvent.getTapEvent();
				assertInstanceOf(TapInsertRecordEvent.class, tapEvent);
				shareCdcUtilMockedStatic.verify(() -> ShareCdcUtil.iterateAndHandleSpecialType(any(), any(), any(), any(), eq(EventType.AFTER)), times(1));
			}
		}

		@Test
		@DisplayName("test update event")
		void testUpdateEvent() {
			Document document = new Document("after", new Document("_str", "test1"))
					.append("before", new Document("_str", "test"))
					.append("op", OperationType.UPDATE.getOp())
					.append("timestamp", System.currentTimeMillis());

			try (
					MockedStatic<ShareCdcBaseReader> shareCdcBaseReaderMockedStatic = mockStatic(ShareCdcBaseReader.class);
					MockedStatic<ShareCdcUtil> shareCdcUtilMockedStatic = mockStatic(ShareCdcUtil.class)
			) {
				shareCdcBaseReaderMockedStatic.when(() -> ShareCdcBaseReader.logDocumentVerify(document)).thenAnswer(invocationOnMock -> null);
				ShareCdcBaseReader.ShareCDCReaderEvent shareCDCReaderEvent = shareCdcPDKTaskReader.tapEventWrapper(document);
				TapEvent tapEvent = shareCDCReaderEvent.getTapEvent();
				assertInstanceOf(TapUpdateRecordEvent.class, tapEvent);
				shareCdcUtilMockedStatic.verify(() -> ShareCdcUtil.iterateAndHandleSpecialType(any(), any(), any(), any(), eq(EventType.BEFORE)), times(1));
				shareCdcUtilMockedStatic.verify(() -> ShareCdcUtil.iterateAndHandleSpecialType(any(), any(), any(), any(), eq(EventType.AFTER)), times(1));
			}
		}

		@Test
		@DisplayName("test delete event")
		void testDeleteEvent() {
			Document document = new Document("before", new Document("_str", "test"))
					.append("op", OperationType.DELETE.getOp())
					.append("timestamp", System.currentTimeMillis());

			try (
					MockedStatic<ShareCdcBaseReader> shareCdcBaseReaderMockedStatic = mockStatic(ShareCdcBaseReader.class);
					MockedStatic<ShareCdcUtil> shareCdcUtilMockedStatic = mockStatic(ShareCdcUtil.class)
			) {
				shareCdcBaseReaderMockedStatic.when(() -> ShareCdcBaseReader.logDocumentVerify(document)).thenAnswer(invocationOnMock -> null);
				ShareCdcBaseReader.ShareCDCReaderEvent shareCDCReaderEvent = shareCdcPDKTaskReader.tapEventWrapper(document);
				TapEvent tapEvent = shareCDCReaderEvent.getTapEvent();
				assertInstanceOf(TapDeleteRecordEvent.class, tapEvent);
				shareCdcUtilMockedStatic.verify(() -> ShareCdcUtil.iterateAndHandleSpecialType(any(), any(), any(), any(), eq(EventType.BEFORE)), times(1));
			}
		}
	}

	@Nested
	@DisplayName("Method isIllegalDate test")
	class isIllegalDateTest {
		@BeforeEach
		void setUp() {
			when(shareCdcPDKTaskReader.isIllegalDate(any())).thenCallRealMethod();
		}

		@Test
		@DisplayName("test illegal date")
		void testMainProcess() {
			DateTime dateTime = new DateTime("0000-00-00", DateTime.DATE_TYPE);
			TapDateValue tapDateValue = new TapDateValue(dateTime);
			assertTrue(shareCdcPDKTaskReader.isIllegalDate(tapDateValue));

			dateTime = new DateTime("0000-00-00 00:00:00", DateTime.DATETIME_TYPE);
			TapDateTimeValue tapDateTimeValue = new TapDateTimeValue(dateTime);
			assertTrue(shareCdcPDKTaskReader.isIllegalDate(tapDateTimeValue));

			dateTime = new DateTime("99:99:99", DateTime.TIME_TYPE);
			TapTimeValue tapTimeValue = new TapTimeValue(dateTime);
			assertTrue(shareCdcPDKTaskReader.isIllegalDate(tapTimeValue));

			dateTime = new DateTime("0000", DateTime.YEAR_TYPE);
			TapYearValue tapYearValue = new TapYearValue(dateTime);
			assertTrue(shareCdcPDKTaskReader.isIllegalDate(tapYearValue));
		}

		@Test
		@DisplayName("test legal date")
		void testLegalDate() {
			DateTime dateTime = new DateTime(System.currentTimeMillis());
			TapDateTimeValue tapDateTimeValue = new TapDateTimeValue(dateTime);
			assertFalse(shareCdcPDKTaskReader.isIllegalDate(tapDateTimeValue));
		}

		@Test
		@DisplayName("test not date type")
		void testNotDateType() {
			TapStringValue tapStringValue = new TapStringValue("test");
			assertFalse(shareCdcPDKTaskReader.isIllegalDate(tapStringValue));
		}
	}

	@Nested
	@DisplayName("Method checkTableStartPointValid test")
	class checkTableStartPointValidTest {

		private ConstructRingBuffer constructRingBuffer;
		private Map<String, Long> sequenceMap;
		private Ringbuffer ringbuffer;

		@BeforeEach
		void setUp() {
			sequenceMap = new ConcurrentHashMap<>();
			ReflectionTestUtils.setField(shareCdcPDKTaskReader, "sequenceMap", sequenceMap);
			constructRingBuffer = mock(ConstructRingBuffer.class);
			when(shareCdcPDKTaskReader.getConstruct(anyString())).thenReturn(constructRingBuffer);
			when(shareCdcPDKTaskReader.isRunning()).thenReturn(true);
			ringbuffer = mock(Ringbuffer.class);
		}

		@Test
		void test1() {
			when(shareCdcContext.getCdcStartTs()).thenReturn(1000L);
			Document document = new Document("timestamp", 2000L);
			ConstructIterator<Document> iterator = mock(ConstructIterator.class);
			when(iterator.peek(anyLong(), any(TimeUnit.class))).thenReturn(document);
			assertDoesNotThrow(() -> when(constructRingBuffer.find()).thenReturn(iterator));
			when(obsLogger.isDebugEnabled()).thenReturn(false);
			taskDto.setType(TaskDto.TYPE_CDC);
			List<String> tableNames = new ArrayList<>();
			tableNames.add("test1");
			ReflectionTestUtils.setField(shareCdcPDKTaskReader, "tableNames", tableNames);
			when(ringbuffer.tailSequence()).thenReturn(0L);
			when(constructRingBuffer.getRingbuffer()).thenReturn(ringbuffer);
			assertDoesNotThrow(() -> when(shareCdcPDKTaskReader.checkTableStartPointValid(anyInt())).thenCallRealMethod());

			int step = assertDoesNotThrow(() -> shareCdcPDKTaskReader.checkTableStartPointValid(1));
			assertEquals(2, step);
			verify(obsLogger).info(anyString());
		}
	}
}
