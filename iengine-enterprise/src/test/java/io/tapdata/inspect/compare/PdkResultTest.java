package io.tapdata.inspect.compare;

import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.type.TapDateTime;
import io.tapdata.entity.schema.value.DateTime;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.inspect.util.InspectJobUtil;
import io.tapdata.pdk.apis.entity.TapAdvanceFilter;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.connector.source.BatchCountFunction;
import io.tapdata.pdk.apis.functions.connector.source.CountByPartitionFilterFunction;
import io.tapdata.pdk.apis.functions.connector.source.ExecuteCommandFunction;
import io.tapdata.pdk.core.api.ConnectorNode;
import io.tapdata.pdk.core.monitor.PDKInvocationMonitor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


class PdkResultTest {
    private List<List<Object>> diffKeyValues;
    List<Object> values;
    PdkResult pdkResult;
    ConnectorNode connectorNode;

    @BeforeEach
    void setUp() {
        diffKeyValues = new ArrayList<>();
        values = new ArrayList<>();
        values.add("1");
        values.add("2");
        diffKeyValues.add(values);
        pdkResult = mock(PdkResult.class);
        ReflectionTestUtils.setField(pdkResult,"diffKeyValues",diffKeyValues);
        connectorNode = mock(ConnectorNode.class);
        ReflectionTestUtils.setField(pdkResult,"connectorNode",connectorNode);
    }
    @DisplayName("test addFilterMatchIfHaveDiffKey method when diffKeyValues not null, key and value size equals")
    @Test
    void test1(){
        TapAdvanceFilter tapAdvanceFilter=new TapAdvanceFilter();
        doCallRealMethod().when(pdkResult).addFilterMatchIfHaveDiffKey(tapAdvanceFilter);
        List<String> dataKeys= new ArrayList<>();
        dataKeys.add("key1");
        dataKeys.add("key2");
        ReflectionTestUtils.setField(pdkResult,"dataKeys",dataKeys);
        ReflectionTestUtils.setField(pdkResult,"diffKeyIndex",0);
        pdkResult.addFilterMatchIfHaveDiffKey(tapAdvanceFilter);
        DataMap match = tapAdvanceFilter.getMatch();
        assertEquals(2,match.size());
    }
    @DisplayName("test addFilterMatchIfHaveDiffKey method when diffKeyValues not null, key and value size not equals")
    @Test
    void test2(){
        TapAdvanceFilter tapAdvanceFilter=new TapAdvanceFilter();
        doCallRealMethod().when(pdkResult).addFilterMatchIfHaveDiffKey(tapAdvanceFilter);
        List<String> dataKeys= new ArrayList<>();
        dataKeys.add("key1");
        ReflectionTestUtils.setField(pdkResult,"dataKeys",dataKeys);
        ReflectionTestUtils.setField(pdkResult,"diffKeyIndex",0);
        assertThrows(RuntimeException.class,()->pdkResult.addFilterMatchIfHaveDiffKey(tapAdvanceFilter));
    }

    @Nested
    class initTotalTest {
        @DisplayName("test initTotal method when countByPartitionFilterFunction is not null")
        @Test
        void test1() {
            ReflectionTestUtils.setField(pdkResult,"diffKeyValues",null);
            ConnectorFunctions connectorFunctions = mock(ConnectorFunctions.class);
            when(connectorNode.getConnectorFunctions()).thenReturn(connectorFunctions);
            when(connectorFunctions.getBatchCountFunction()).thenReturn(mock(BatchCountFunction.class));
            when(connectorFunctions.getCountByPartitionFilterFunction()).thenReturn(mock(CountByPartitionFilterFunction.class));
            when(connectorFunctions.getExecuteCommandFunction()).thenReturn(mock(ExecuteCommandFunction.class));
            doCallRealMethod().when(pdkResult).initTotal();
            pdkResult.initTotal();
        }
        @DisplayName("test initTotal method when countByPartitionFilterFunction is null")
        @Test
        void test2() {
            try (MockedStatic<InspectJobUtil> mb = Mockito
                    .mockStatic(InspectJobUtil.class)) {
                mb.when(()->InspectJobUtil.buildErrorConsumer("test")).thenReturn(mock(Consumer.class));
                ReflectionTestUtils.setField(pdkResult,"diffKeyValues",null);
                ReflectionTestUtils.setField(pdkResult,"tableName","test");
                ConnectorFunctions connectorFunctions = mock(ConnectorFunctions.class);
                when(connectorNode.getConnectorFunctions()).thenReturn(connectorFunctions);
                when(connectorFunctions.getBatchCountFunction()).thenReturn(mock(BatchCountFunction.class));
                when(connectorFunctions.getCountByPartitionFilterFunction()).thenReturn(null);
                when(connectorFunctions.getExecuteCommandFunction()).thenReturn(mock(ExecuteCommandFunction.class));
                doCallRealMethod().when(pdkResult).initTotal();
                pdkResult.initTotal();
                mb.verify(() -> InspectJobUtil.buildErrorConsumer("test"),new Times(1));
            }
        }
    }

    @Test
    void tapAdvanceFilterTest() {
        try (MockedStatic<InspectJobUtil> mb = Mockito
                .mockStatic(InspectJobUtil.class)) {
            mb.when(()->InspectJobUtil.buildErrorConsumer("test")).thenReturn(mock(Consumer.class));
            ReflectionTestUtils.setField(pdkResult,"tableName","test");
            TapAdvanceFilter tapAdvanceFilter = new TapAdvanceFilter();
            doCallRealMethod().when(pdkResult).tapAdvanceFilter(tapAdvanceFilter);
            pdkResult.tapAdvanceFilter(tapAdvanceFilter);
            mb.verify(() -> InspectJobUtil.buildErrorConsumer("test"),new Times(1));
        }
    }

    @Nested
    class handleDataKeysTest {
        Map<String, Object> keyMap;
        List<Object> keyValues;
        TapTable tapTable;
        @BeforeEach
        void setUp() {
            keyMap = new HashMap<>();
            keyValues = new ArrayList<>();
            tapTable = new TapTable();
            LinkedHashMap<String, TapField> nameFieldMap = new LinkedHashMap<>();
            TapField tapField = new TapField();
            tapField.setName("key1");
            tapField.setTapType(new TapDateTime());
            nameFieldMap.put("key1",tapField);
            tapTable.setNameFieldMap(nameFieldMap);
            ReflectionTestUtils.setField(pdkResult,"tapTable",tapTable);
            doCallRealMethod().when(pdkResult).handleDataKeys(keyMap, keyValues);
            List<String> dataKeys= new ArrayList<>();
            dataKeys.add("key1");
            ReflectionTestUtils.setField(pdkResult,"dataKeys",dataKeys);

        }
        @Test
        void testHandleDataKeysForDate() {
            keyValues.add("2025-04-15T10:00:00Z");
            pdkResult.handleDataKeys(keyMap, keyValues);
            assertTrue(keyMap.get("key1") instanceof DateTime);
        }

    }
}
