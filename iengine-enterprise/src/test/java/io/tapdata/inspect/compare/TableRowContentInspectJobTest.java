package io.tapdata.inspect.compare;

import com.tapdata.constant.ConnectorConstant;
import com.tapdata.entity.Connections;
import com.tapdata.entity.inspect.InspectDataSource;
import com.tapdata.entity.inspect.InspectResultStats;
import com.tapdata.entity.inspect.InspectTask;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.tm.commons.schema.Field;
import com.tapdata.tm.commons.schema.MetadataInstancesDto;
import com.tapdata.tm.commons.schema.bean.SourceDto;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.inspect.InspectJob;
import io.tapdata.inspect.InspectTaskContext;
import io.tapdata.inspect.util.InspectJobUtil;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.ExecuteResult;
import io.tapdata.pdk.apis.entity.FilterResults;
import io.tapdata.pdk.apis.entity.TapAdvanceFilter;
import io.tapdata.pdk.apis.entity.TapExecuteCommand;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.spec.TapNodeSpecification;
import io.tapdata.pdk.core.api.ConnectorNode;
import io.tapdata.pdk.core.tapnode.TapNodeInfo;
import io.tapdata.schema.TapTableMap;
import io.tapdata.utils.UnitTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class TableRowContentInspectJobTest {
    @Nested
    class generateFullFieldFn{
        TableRowContentInspectJob tableRowContentInspectJob;
        ClientMongoOperator clientMongoOperator;
        InspectResultStats stats;
        InspectDataSource sourceDataSource;
        InspectDataSource targetDataSource;

        @BeforeEach
        void before() {
            clientMongoOperator = mock(ClientMongoOperator.class);
            tableRowContentInspectJob = mock(TableRowContentInspectJob.class);
            ReflectionTestUtils.setField(tableRowContentInspectJob, "clientMongoOperator", clientMongoOperator);

            stats = new InspectResultStats();
            sourceDataSource = new InspectDataSource();
            targetDataSource = new InspectDataSource();
            stats.setSource(sourceDataSource);
            stats.setTarget(targetDataSource);
            UnitTestUtils.injectField(InspectJob.class, tableRowContentInspectJob, "stats", stats);
        }
        @DisplayName("test field inspect without replication or transform task ")
        @Test
        void test1(){
            InspectTaskContext inspectTaskContext=mock(InspectTaskContext.class);
            when(inspectTaskContext.getFlowId()).thenReturn(null);
            ReflectionTestUtils.setField(tableRowContentInspectJob, "inspectTaskContext", inspectTaskContext);
            doCallRealMethod().when(tableRowContentInspectJob).generateFullFieldFn();
            DefaultCompare defaultCompare = (DefaultCompare) tableRowContentInspectJob.generateFullFieldFn();
            Object sourceColumns = ReflectionTestUtils.getField(defaultCompare, "sourceColumns");
            Object targetColumns = ReflectionTestUtils.getField(defaultCompare, "targetColumns");
            assertEquals(null,sourceColumns);
            assertEquals(null,targetColumns);
        }
        @DisplayName("test field inspect when full field, metadataInstance is null ")
        @Test
        void test2(){
            generateInspectContextAndInspectTask();
            when(clientMongoOperator.findOne(anyMap(),eq(ConnectorConstant.METADATA_INSTANCE_COLLECTION + "/node/schemaPage"),any(Class.class))).thenReturn(null);
            DefaultCompare defaultCompare = (DefaultCompare) tableRowContentInspectJob.generateFullFieldFn();
            List<String> sourceColumns = (List<String>) ReflectionTestUtils.getField(defaultCompare, "sourceColumns");
            List<String> targetColumns = (List<String>) ReflectionTestUtils.getField(defaultCompare, "targetColumns");
            assertNotNull(sourceColumns);
            assertEquals(0,sourceColumns.size());
            assertEquals(sourceColumns, sourceDataSource.getColumns());
            assertNotNull(targetColumns);
            assertEquals(0, targetColumns.size());
            assertEquals(targetColumns, targetDataSource.getColumns());
        }
        @DisplayName("test field inspect when full field, metadataInstance is not null and field have normal、job_analyze and deleted")
        @Test
        void test3(){
            generateInspectContextAndInspectTask();
            MetadataInstancesDto metadataInstancesDto = generateMetadataInstance();
            when(clientMongoOperator.findOne(anyMap(),eq(ConnectorConstant.METADATA_INSTANCE_COLLECTION + "/node/schemaPage"),any(Class.class))).thenReturn(metadataInstancesDto);
            DefaultCompare defaultCompare = (DefaultCompare) tableRowContentInspectJob.generateFullFieldFn();
            List<String> sourceColumns = (List<String>) ReflectionTestUtils.getField(defaultCompare, "sourceColumns");
            List<String> targetColumns = (List<String>) ReflectionTestUtils.getField(defaultCompare, "targetColumns");
            assertNotNull(sourceColumns);
            assertEquals(2,sourceColumns.size());
            assertEquals(sourceColumns, sourceDataSource.getColumns());
            assertNotNull(targetColumns);
            assertEquals(2, targetColumns.size());
            assertEquals(targetColumns, targetDataSource.getColumns());
        }
        @DisplayName("test field inspect when full field, Mongo has nested documents")
        @Test
        void test4(){
            generateInspectContextAndInspectTask();
            MetadataInstancesDto metadataInstancesDto = generateMongoMetadataInstance();
            when(clientMongoOperator.findOne(anyMap(),eq(ConnectorConstant.METADATA_INSTANCE_COLLECTION + "/node/schemaPage"),any(Class.class))).thenReturn(metadataInstancesDto);
            DefaultCompare defaultCompare = (DefaultCompare) tableRowContentInspectJob.generateFullFieldFn();
            List<String> sourceColumns = (List<String>) ReflectionTestUtils.getField(defaultCompare, "sourceColumns");
            List<String> targetColumns = (List<String>) ReflectionTestUtils.getField(defaultCompare, "targetColumns");
            assertNotNull(sourceColumns);
            assertEquals(1,sourceColumns.size());
            assertEquals(sourceColumns, sourceDataSource.getColumns());
            assertNotNull(targetColumns);
            assertEquals(1, targetColumns.size());
            assertEquals(targetColumns, targetDataSource.getColumns());
        }

        @DisplayName("test field inspect when full field, Sybase has timestamp field")
        @Test
        void test5(){
            generateInspectContextAndInspectTask();
            MetadataInstancesDto metadataInstancesDto = generateSybaseMetadataInstance();
            when(clientMongoOperator.findOne(anyMap(),eq(ConnectorConstant.METADATA_INSTANCE_COLLECTION + "/node/schemaPage"),any(Class.class))).thenReturn(metadataInstancesDto);
            DefaultCompare defaultCompare = (DefaultCompare) tableRowContentInspectJob.generateFullFieldFn();
            List<String> sourceColumns = (List<String>) ReflectionTestUtils.getField(defaultCompare, "sourceColumns");
            List<String> targetColumns = (List<String>) ReflectionTestUtils.getField(defaultCompare, "targetColumns");
            assertNotNull(sourceColumns);
            assertEquals(2,sourceColumns.size());
            assertEquals(sourceColumns, sourceDataSource.getColumns());
            assertNotNull(targetColumns);
            assertEquals(2, targetColumns.size());
            assertEquals(targetColumns, targetDataSource.getColumns());
        }

        @DisplayName("test field inspect when full field, Sybase has timestamp field")
        @Test
        void test6(){
            generateInspectContextAndInspectTask();
            MetadataInstancesDto metadataInstancesDto = generateSqlServerMetadataInstance();
            when(clientMongoOperator.findOne(anyMap(),eq(ConnectorConstant.METADATA_INSTANCE_COLLECTION + "/node/schemaPage"),any(Class.class))).thenReturn(metadataInstancesDto);
            DefaultCompare defaultCompare = (DefaultCompare) tableRowContentInspectJob.generateFullFieldFn();
            List<String> sourceColumns = (List<String>) ReflectionTestUtils.getField(defaultCompare, "sourceColumns");
            List<String> targetColumns = (List<String>) ReflectionTestUtils.getField(defaultCompare, "targetColumns");
            assertNotNull(sourceColumns);
            assertEquals(2,sourceColumns.size());
            assertEquals(sourceColumns, sourceDataSource.getColumns());
            assertNotNull(targetColumns);
            assertEquals(2, targetColumns.size());
            assertEquals(targetColumns, targetDataSource.getColumns());
        }
        private void generateInspectContextAndInspectTask() {
            InspectTaskContext inspectTaskContext=mock(InspectTaskContext.class);
            when(inspectTaskContext.getFlowId()).thenReturn("testTaskId");
            ReflectionTestUtils.setField(tableRowContentInspectJob, "inspectTaskContext", inspectTaskContext);
            InspectDataSource source = mock(InspectDataSource.class);
            InspectDataSource target = mock(InspectDataSource.class);
            when(target.getTable()).thenReturn("testTable");
            when(target.getNodeId()).thenReturn("targetNodeId");
            InspectTask inspectTask = mock(InspectTask.class);
            when(inspectTask.getSource()).thenReturn(source);
            when(inspectTask.getTarget()).thenReturn(target);

            ReflectionTestUtils.setField(tableRowContentInspectJob, "inspectTask", inspectTask);
            doCallRealMethod().when(tableRowContentInspectJob).generateFullFieldFn();
        }
        MetadataInstancesDto generateMetadataInstance(){
            MetadataInstancesDto metadataInstancesDto = new MetadataInstancesDto();
            List<Field> fields = new ArrayList<>();
            Field field1 = new Field();
            field1.setFieldName("test1");
            field1.setSource("job_analyze");
            field1.setDeleted(false);
            fields.add(field1);
            Field field2 = new Field();
            field2.setFieldName("test2");
            field2.setDeleted(true);
            field2.setSource("test");
            fields.add(field2);
            Field field3 = new Field();
            field3.setFieldName("test3");
            field3.setDeleted(false);
            field3.setSource("test");
            field3.setOriginalFieldName("test3");
            fields.add(field3);
            Field field4 = new Field();
            field4.setFieldName("new_test4");
            field4.setDeleted(false);
            field4.setOriginalFieldName("test4");
            field4.setSource("test");
            fields.add(field4);
            metadataInstancesDto.setFields(fields);
            SourceDto sourceDto = new SourceDto();
            sourceDto.setDatabase_type("pg");
            metadataInstancesDto.setSource(sourceDto);
            return metadataInstancesDto;
        }

        MetadataInstancesDto generateMongoMetadataInstance(){
            MetadataInstancesDto metadataInstancesDto = new MetadataInstancesDto();
            List<Field> fields = new ArrayList<>();
            Field field2 = new Field();
            field2.setFieldName("test");
            field2.setDeleted(false);
            field2.setSource("test");
            fields.add(field2);
            Field field3 = new Field();
            field3.setFieldName("test.test");
            field3.setDeleted(false);
            field3.setSource("test");
            fields.add(field3);
            Field field4 = new Field();
            field4.setFieldName("test.test1");
            field4.setDeleted(false);
            field4.setSource("test");
            fields.add(field4);
            metadataInstancesDto.setFields(fields);
            SourceDto sourceDto = new SourceDto();
            sourceDto.setDatabase_type("MongoDB");
            metadataInstancesDto.setSource(sourceDto);
            return metadataInstancesDto;
        }

        MetadataInstancesDto generateSybaseMetadataInstance(){
            MetadataInstancesDto metadataInstancesDto = new MetadataInstancesDto();
            List<Field> fields = new ArrayList<>();
            Field field2 = new Field();
            field2.setFieldName("test");
            field2.setDeleted(false);
            field2.setSource("test");
            fields.add(field2);
            Field field3 = new Field();
            field3.setFieldName("test1");
            field3.setDataType("timestamp");
            field3.setDeleted(false);
            field3.setSource("test");
            fields.add(field3);
            Field field4 = new Field();
            field4.setFieldName("test2");
            field4.setDeleted(false);
            field4.setSource("test");
            fields.add(field4);
            metadataInstancesDto.setFields(fields);
            SourceDto sourceDto = new SourceDto();
            sourceDto.setDatabase_type("Sybase");
            metadataInstancesDto.setSource(sourceDto);
            return metadataInstancesDto;
        }

        MetadataInstancesDto generateSqlServerMetadataInstance(){
            MetadataInstancesDto metadataInstancesDto = new MetadataInstancesDto();
            List<Field> fields = new ArrayList<>();
            Field field2 = new Field();
            field2.setFieldName("test");
            field2.setDeleted(false);
            field2.setSource("test");
            fields.add(field2);
            Field field3 = new Field();
            field3.setFieldName("test1");
            field3.setDataType("timestamp");
            field3.setDeleted(false);
            field3.setSource("test");
            fields.add(field3);
            Field field4 = new Field();
            field4.setFieldName("test2");
            field4.setDeleted(false);
            field4.setSource("test");
            fields.add(field4);
            metadataInstancesDto.setFields(fields);
            SourceDto sourceDto = new SourceDto();
            sourceDto.setDatabase_type("SQL Server");
            metadataInstancesDto.setSource(sourceDto);
            return metadataInstancesDto;
        }
    }
    @Nested
    class queryForCursorTest{
        TableRowContentInspectJob tableRowContentInspectJob;
        List<List<Object>> diffKeyValues = new ArrayList<>();
        List<Object> objects = new ArrayList<>();
        List<String> dataKey = new ArrayList<>();
        ConnectorNode connectorNode;
        Connections connections;

        @BeforeEach
        void init(){
            tableRowContentInspectJob=mock(TableRowContentInspectJob.class);
            objects.add("testID");
            diffKeyValues.add(objects);
            dataKey.add("testID");
            connectorNode = new ConnectorNode();
            connections = new Connections();
            ConnectorFunctions connectorFunctions=new ConnectorFunctions();
            ReflectionTestUtils.setField(connectorNode,"connectorFunctions",connectorFunctions);
            connectorNode.getConnectorFunctions().supportExecuteCommandFunction(( connectorContext,  executeCommand, consumer)->{

            });
            connectorNode.getConnectorFunctions().supportQueryByAdvanceFilter((connectorContext, filter, table, consumer)->{

            });
        }

        @Test
        void test1(){
            TapConnectorContext tapConnectorContext = mock(TapConnectorContext.class);
            ReflectionTestUtils.setField(connectorNode,"connectorContext",tapConnectorContext);
            KVReadOnlyMap<TapTable> tapTableMap=mock(KVReadOnlyMap.class);
            when(tapConnectorContext.getTableMap()).thenReturn(tapTableMap);
            TapTable tapTable=new TapTable();
            LinkedHashMap<String, TapField> nameFieldMap =new LinkedHashMap<>();
            TapField tapField = new TapField("testID", "nvarchar(max)");
            nameFieldMap.put("testID",tapField);
            tapTable.setNameFieldMap(nameFieldMap);

            when(tapTableMap.get("testTable")).thenReturn(tapTable);

            InspectDataSource inspectDataSource=new InspectDataSource();
            inspectDataSource.setTable("testTable");

            Map<String, String> collateMap = new HashMap<>();
            collateMap.put("testID","C.UTF-8");
            inspectDataSource.setEnableCustomCommand(false);
            inspectDataSource.setCollate(collateMap);

            List<String> columns=new ArrayList<>();
            columns.add("testID");
            inspectDataSource.setColumns(columns);
            inspectDataSource.setCustomNullSort(0);

            when(tableRowContentInspectJob.queryForCursor(any(Connections.class),any(InspectDataSource.class),any(ConnectorNode.class),anyBoolean(),anyList(),anyList())).thenCallRealMethod();
            assertDoesNotThrow(()->{
                tableRowContentInspectJob.queryForCursor(connections,inspectDataSource,connectorNode,true,dataKey,diffKeyValues);
            });
        }
        @Test
        void test2(){
            TapConnectorContext tapConnectorContext = mock(TapConnectorContext.class);
            ReflectionTestUtils.setField(connectorNode,"connectorContext",tapConnectorContext);
            KVReadOnlyMap<TapTable> tapTableMap=mock(KVReadOnlyMap.class);
            when(tapConnectorContext.getTableMap()).thenReturn(tapTableMap);
            TapTable tapTable=new TapTable();
            LinkedHashMap<String, TapField> nameFieldMap =new LinkedHashMap<>();
            TapField tapField = new TapField("testID", "nvarchar(max)");
            nameFieldMap.put("testID",tapField);
            tapTable.setNameFieldMap(nameFieldMap);

            when(tapTableMap.get("testTable")).thenReturn(tapTable);

            InspectDataSource inspectDataSource=new InspectDataSource();
            inspectDataSource.setTable("testTable");
            Map<String, String> collateMap = new HashMap<>();
            collateMap.put("testID","C.UTF-8");
            inspectDataSource.setEnableCustomCommand(false);
            inspectDataSource.setCollate(collateMap);

            List<String> columns=new ArrayList<>();
            columns.add("testID");


            InspectJob inspectJob=mock(InspectJob.class);
            inspectDataSource.setColumns(columns);
            inspectDataSource.setCustomNullSort(1);
            inspectDataSource.setSortColumn("testId");
            when(tableRowContentInspectJob.getSortColumns("testId")).thenCallRealMethod();
            when(tableRowContentInspectJob.getSortColumns("testId",",")).thenCallRealMethod();
            when(tableRowContentInspectJob.queryForCursor(any(Connections.class),any(InspectDataSource.class),any(ConnectorNode.class),anyBoolean(),anyList(),anyList())).thenCallRealMethod();
            assertDoesNotThrow(()->{
                tableRowContentInspectJob.queryForCursor(connections,inspectDataSource,connectorNode,true,dataKey,diffKeyValues);
            });
        }
    }
    @Nested
    class testRecoveryPdkResult{
        @Test
        void test1(){
            List<List<Object>> diffKeyValues = new ArrayList<>();
            List<Object> objects = new ArrayList<>();
            objects.add("testID");
            diffKeyValues.add(objects);
            List<String> dataKey = new ArrayList<>();
            dataKey.add("testID");

            ConnectorNode connectorNode=new ConnectorNode();
            Connections connections=new Connections();

            ConnectorFunctions connectorFunctions=new ConnectorFunctions();
            ReflectionTestUtils.setField(connectorNode,"connectorFunctions",connectorFunctions);
            connectorNode.getConnectorFunctions().supportExecuteCommandFunction(( connectorContext,  executeCommand, consumer)->{

            });
            connectorNode.getConnectorFunctions().supportQueryByAdvanceFilter((connectorContext, filter, table, consumer)->{

            });
            TapConnectorContext tapConnectorContext = mock(TapConnectorContext.class);
            ReflectionTestUtils.setField(connectorNode,"connectorContext",tapConnectorContext);
            KVReadOnlyMap<TapTable> tapTableMap=mock(KVReadOnlyMap.class);
            when(tapConnectorContext.getTableMap()).thenReturn(tapTableMap);
            TapTable tapTable=new TapTable();
            LinkedHashMap<String, TapField> nameFieldMap =new LinkedHashMap<>();
            TapField tapField = new TapField("testID", "nvarchar(max)");
            nameFieldMap.put("testID",tapField);
            tapTable.setNameFieldMap(nameFieldMap);

            when(tapTableMap.get("testTable")).thenReturn(tapTable);



            InspectDataSource inspectDataSource=new InspectDataSource();
            inspectDataSource.setTable("testTable");

            Map<String, String> collateMap = new HashMap<>();
            collateMap.put("testID","C.UTF-8");
            inspectDataSource.setEnableCustomCommand(false);
            inspectDataSource.setCollate(collateMap);

            List<String> columns=new ArrayList<>();
            columns.add("testID");
            inspectDataSource.setColumns(columns);
            Set<String> columnsSet = new LinkedHashSet<>(inspectDataSource.getColumns());
            assertDoesNotThrow(()->{
                new RecoveryPdkResult(
                        columns,
                        connections,
                        inspectDataSource.getTable(),
                        columnsSet,
                        connectorNode,
                        true,
                        dataKey,
                        diffKeyValues,
                        null != inspectDataSource.getIsFilter() && inspectDataSource.getIsFilter() ? inspectDataSource.getConditions() : null,
                        inspectDataSource.isEnableCustomCommand(),inspectDataSource.getCustomCommand(),inspectDataSource.getCollate()
                );
            });
        }
    }

    @Nested
    class getCountSqlTest{
        @Test
        void testSqlContainsWhereExist(){
            String customSql = "select * from CAR_CLAIM_009 t1 where exists(select CLAIM_ID from CAR_CLAIM_999 t2 where t1.CLAIM_ID = t2.CLAIM_ID and t2.CLAIM_DATE >= TO_DATE('2018-10-01','YYYY-MM-DD'))";
            String actual = TableRowCountInspectJob.getCountSql(customSql);
            String expect = "SELECT COUNT(1) FROM (" + customSql + ")";
            assertEquals(expect, actual);
        }
        @Test
        void testSqlContainsGroupBy(){
            String customSql = "select CLAIM_ID, SUM(CLAIM_AMOUNT) from CAR_CLAIM_009 group by CLAIM_ID";
            String actual = TableRowCountInspectJob.getCountSql(customSql);
            String expect = "SELECT COUNT(1) FROM (" + customSql + ")";
            assertEquals(expect, actual);
        }
        @Test
        void testNormalSql(){
            String customSql = "select * from CAR_CLAIM_009";
            String actual = TableRowCountInspectJob.getCountSql(customSql);
            String expect = "SELECT COUNT(1) FROM CAR_CLAIM_009";
            assertEquals(expect, actual);
        }
    }

    @Nested
    class compareTest {
        TableRowContentInspectJob tableRowContentInspectJob;
        InspectTask inspectTask;
        Connections source;
        Connections target;
        InspectResultStats stats;
        TableRowContentInspectJob.CompareProgress compareProgress;
        InspectTaskContext inspectTaskContext;
        @BeforeEach
        void beforeEach() {
            tableRowContentInspectJob = mock(TableRowContentInspectJob.class);
            inspectTask = mock(InspectTask.class);
            source = mock(Connections.class);
            target = mock(Connections.class);
            stats = mock(InspectResultStats.class);
            compareProgress = mock(TableRowContentInspectJob.CompareProgress.class);
            inspectTaskContext = mock(InspectTaskContext.class);
            ReflectionTestUtils.setField(tableRowContentInspectJob, "inspectTaskContext", inspectTaskContext);
        }
        @Test
        void testCompareWithEx(){
            try (MockedStatic<InspectJobUtil> mb = Mockito
                    .mockStatic(InspectJobUtil.class)) {
                mb.when(()->InspectJobUtil.buildStatsErrorMsg(any(InspectResultStats.class), any(Throwable.class))).thenAnswer(invocationOnMock -> {return null;});
                when(inspectTask.isFullMatch()).thenReturn(false);
                InspectDataSource inspect1 = mock(InspectDataSource.class);
                InspectDataSource inspect2 = mock(InspectDataSource.class);
                when(inspectTask.getSource()).thenReturn(inspect1);
                when(inspectTask.getTarget()).thenReturn(inspect2);
                when(inspectTaskContext.getEnableRecovery()).thenReturn(false);
                doCallRealMethod().when(tableRowContentInspectJob).compare(inspectTask, source, target, stats, compareProgress);
                tableRowContentInspectJob.compare(inspectTask, source, target, stats, compareProgress);
                mb.verify(() -> InspectJobUtil.buildStatsErrorMsg(any(InspectResultStats.class), any(Throwable.class)),new Times(1));
            }
        }
    }
}
