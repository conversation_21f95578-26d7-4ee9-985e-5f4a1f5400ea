package io.tapdata.inspect.compare;

import io.tapdata.entity.schema.value.DateTime;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class DefaultCompareTest {
    @Nested
    class testCompareClass{
        @DisplayName("test val1 is boolean and val2 is not boolean")
        @Test
        void test1(){
            DefaultCompare defaultCompare = new DefaultCompare();
            boolean compare = defaultCompare.compare(true, "1");
            assertEquals(false,compare);
        }
        @DisplayName("test val2 is boolean and val1 is not boolean")
        @Test
        void test2(){
            DefaultCompare defaultCompare = new DefaultCompare();
            boolean compare = defaultCompare.compare("1", true);
            assertEquals(false,compare);
        }
        @DisplayName("test val2 and val1 is not boolean")
        @Test
        void test3(){
            DefaultCompare defaultCompare = new DefaultCompare();
            boolean compare = defaultCompare.compare("1", "2");
            assertEquals(true,compare);
        }

        @DisplayName("test val2 and val1 is datetime not ignoreTimePrecision")
        @Test
        void test4(){
            DefaultCompare defaultCompare = new DefaultCompare();
            defaultCompare.setIgnoreTimePrecision(false);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSSSSS");
            DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSS");
            DateTime vl1 = new DateTime(LocalDateTime.parse("2023-05-15 14:30:25.123456789", formatter));

            DateTime vl2 =new DateTime(LocalDateTime.parse("2023-05-15 14:30:25.12346", formatter2));
            boolean compareDateTime = defaultCompare.compare(vl1,vl2);
            assertEquals(true,compareDateTime);
            boolean compareInstant = defaultCompare.compare(vl1.toInstant(),vl2.toInstant());
            assertEquals(true,compareInstant);
        }

        @DisplayName("test val2 and val1 is datetime ignoreTimePrecision")
        @Test
        void test5(){
            DefaultCompare defaultCompare = new DefaultCompare();
            defaultCompare.setIgnoreTimePrecision(true);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSSSSS");
            DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSS");
            DateTime vl1 = new DateTime(LocalDateTime.parse("2023-05-15 14:30:25.123456789", formatter));

            DateTime vl2 =new DateTime(LocalDateTime.parse("2023-05-15 14:30:25.12346", formatter2));
            boolean compareDateTime = defaultCompare.compare(vl1,vl2);
            assertEquals(false,compareDateTime);
            boolean compareInstant = defaultCompare.compare(vl1.toInstant(),vl2.toInstant());
            assertEquals(false,compareInstant);
        }

        @DisplayName("test val2 and val1 is datetime ignoreTimePrecision")
        @Test
        void test6(){
            DefaultCompare defaultCompare = new DefaultCompare();
            defaultCompare.setIgnoreTimePrecision(true);
            DateTime vl1 = new DateTime("0000-00-00 00:00:00", DateTime.DATETIME_TYPE);
            DateTime vl2 = new DateTime("0000-00-00 00:00:00", DateTime.DATETIME_TYPE);
            boolean compareDateTime = defaultCompare.compare(vl1,vl2);
            assertEquals(false,compareDateTime);
        }
    }
}
