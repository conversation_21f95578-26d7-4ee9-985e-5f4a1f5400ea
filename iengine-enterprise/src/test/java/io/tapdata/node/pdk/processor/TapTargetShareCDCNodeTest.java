package io.tapdata.node.pdk.processor;

import com.hazelcast.jet.core.Processor;
import com.tapdata.constant.MapUtil;
import com.tapdata.entity.TapdataShareLogEvent;
import com.tapdata.entity.sharecdc.LogContent;
import com.tapdata.entity.task.context.DataProcessorContext;
import com.tapdata.tm.commons.dag.Node;
import com.tapdata.tm.commons.dag.logCollector.LogCollecotrConnConfig;
import com.tapdata.tm.commons.externalStorage.ExternalStorageDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import io.tapdata.aspect.TruncateTableFuncAspect;
import io.tapdata.aspect.utils.AspectUtils;
import io.tapdata.common.sharecdc.ShareCdcUtil;
import io.tapdata.construct.HazelcastConstruct;
import io.tapdata.entity.TapProcessorNodeContext;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.control.HeartbeatEvent;
import io.tapdata.entity.event.ddl.table.TapClearTableEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.error.TaskProcessorExCode_11;
import io.tapdata.error.TaskTargetShareCDCProcessorExCode_19;
import io.tapdata.exception.TapCodeException;
import io.tapdata.pdk.core.utils.CommonUtils;
import lombok.SneakyThrows;
import org.bson.BsonUndefined;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.ehcache.impl.internal.concurrent.ConcurrentHashMap;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

import static io.tapdata.flow.engine.V2.util.TapCodecUtil.OBJECT_SERIALIZABLE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@DisplayName("Class TapTargetShareCDCNode Test")
public class TapTargetShareCDCNodeTest {
	private TapTargetShareCDCNode tapTargetShareCDCNode;

	@BeforeEach
	void setUp() {
		tapTargetShareCDCNode = mock(TapTargetShareCDCNode.class);
	}

	@Nested
	@DisplayName("writeLogContent method test")
	class writeLogContentTest {

		@BeforeEach
		void setUp() {
			doCallRealMethod().when(tapTargetShareCDCNode).writeLogContent(any(LogContent.class));
		}

		@Test
		@DisplayName("main process test")
		@SneakyThrows
		void testMainProcess() {
			Map<String, Object> data = new HashMap<>();
			data.put("id", 1);
			LogContent logContent = new LogContent("test", data, null, null, System.currentTimeMillis());
			logContent.setConnectionId("1");
			try (
					MockedStatic<ShareCdcUtil> shareCdcUtilMockedStatic = mockStatic(ShareCdcUtil.class)
			) {
				shareCdcUtilMockedStatic.when(() -> ShareCdcUtil.getTableId(logContent)).thenReturn("test_table_id");
				HazelcastConstruct hazelcastConstruct = mock(HazelcastConstruct.class);
				when(hazelcastConstruct.insert(any(Document.class))).thenReturn(1);
				when(tapTargetShareCDCNode.getConstruct(anyString(), anyString(), anyString()))
						.thenAnswer(invocationOnMock -> {
							Object argument1 = invocationOnMock.getArgument(0);
							assertEquals("test_table_id", argument1);
							return hazelcastConstruct;
						});
				tapTargetShareCDCNode.writeLogContent(logContent);
			}
		}

		@Test
		@DisplayName("when construct insert error")
		@SneakyThrows
		void whenConstructInsertError() {
			Map<String, Object> data = new HashMap<>();
			data.put("id", 1);
			LogContent logContent = new LogContent("test", data, null, null, System.currentTimeMillis());
			logContent.setConnectionId("1");
			try (
					MockedStatic<ShareCdcUtil> shareCdcUtilMockedStatic = mockStatic(ShareCdcUtil.class)
			) {
				shareCdcUtilMockedStatic.when(() -> ShareCdcUtil.getTableId(logContent)).thenReturn("test_table_id");
				HazelcastConstruct hazelcastConstruct = mock(HazelcastConstruct.class);
				RuntimeException runtimeException = new RuntimeException("test error");
				when(hazelcastConstruct.insert(any(Document.class))).thenThrow(runtimeException);
				ExternalStorageDto externalStorageDto = mock(ExternalStorageDto.class);
				ReflectionTestUtils.setField(tapTargetShareCDCNode,"externalStorageDto",externalStorageDto);
				when(externalStorageDto.getType()).thenReturn("MongoDB");
				when(externalStorageDto.getName()).thenReturn("testName");
				when(tapTargetShareCDCNode.getConstruct(anyString(), anyString(), anyString()))
						.thenAnswer(invocationOnMock -> {
							Object argument1 = invocationOnMock.getArgument(0);
							assertEquals("test_table_id", argument1);
							return hazelcastConstruct;
						});
				TapCodeException tapCodeException = assertThrows(TapCodeException.class, () -> tapTargetShareCDCNode.writeLogContent(logContent));
				assertEquals(TaskTargetShareCDCProcessorExCode_19.WRITE_ONE_SHARE_LOG_FAILED, tapCodeException.getCode());
				assertEquals(runtimeException, tapCodeException.getCause());
			}
		}
		@Test
		@DisplayName("when construct insert error and externalStorageDto is null")
		@SneakyThrows
		void whenConstructInsertErrorWithExternalStorageDto(){
			Map<String, Object> data = new HashMap<>();
			data.put("id", 1);
			LogContent logContent = new LogContent("test", data, null, null, System.currentTimeMillis());
			logContent.setConnectionId("1");
			try (
					MockedStatic<ShareCdcUtil> shareCdcUtilMockedStatic = mockStatic(ShareCdcUtil.class)
			) {
				shareCdcUtilMockedStatic.when(() -> ShareCdcUtil.getTableId(logContent)).thenReturn("test_table_id");
				HazelcastConstruct hazelcastConstruct = mock(HazelcastConstruct.class);
				RuntimeException runtimeException = new RuntimeException("test error");
				when(hazelcastConstruct.insert(any(Document.class))).thenThrow(runtimeException);
				when(tapTargetShareCDCNode.getConstruct(anyString(), anyString(), anyString()))
						.thenAnswer(invocationOnMock -> {
							Object argument1 = invocationOnMock.getArgument(0);
							assertEquals("test_table_id", argument1);
							return hazelcastConstruct;
						});
				TapCodeException tapCodeException = assertThrows(TapCodeException.class, () -> tapTargetShareCDCNode.writeLogContent(logContent));
				assertEquals(TaskTargetShareCDCProcessorExCode_19.WRITE_ONE_SHARE_LOG_FAILED, tapCodeException.getCode());
				assertEquals(runtimeException, tapCodeException.getCause());
			}
		}
	}

	@Nested
	@DisplayName("wrapLogContent method test")
	class WrapLogContentMethodTest {
		private final String CLAIMID = "CL_000000001";
		private final String POLICYID = "PC_000000001";

		@BeforeEach
		void setUp() {
			doCallRealMethod().when(tapTargetShareCDCNode).wrapLogContent(any(TapdataShareLogEvent.class));

		}

		@DisplayName("TapUpdateRecordEvent is replace event")
		@Test
		void test1() {
			TapdataShareLogEvent shareLogEvent = new TapdataShareLogEvent();
			Map<String, Object> after = new Document();
			after.put("CLAIM_ID", CLAIMID);
			after.put("POLICY_ID", POLICYID);
			TapUpdateRecordEvent tapEvent = new TapUpdateRecordEvent().table("test").referenceTime(System.currentTimeMillis()).after(after).init();
			tapEvent.setIsReplaceEvent(true);
			shareLogEvent.setTapEvent(tapEvent);
			LogContent logContent = tapTargetShareCDCNode.wrapLogContent(shareLogEvent);
			assertEquals(Boolean.TRUE, logContent.getReplaceEvent());
		}

		@DisplayName("TapUpdateRecordEvent is not replace event")
		@Test
		void test2() {
			TapdataShareLogEvent shareLogEvent = new TapdataShareLogEvent();
			Map<String, Object> after = new Document();
			after.put("CLAIM_ID", CLAIMID);
			after.put("POLICY_ID", POLICYID);
			TapUpdateRecordEvent tapEvent = new TapUpdateRecordEvent().table("test").referenceTime(System.currentTimeMillis()).after(after).init();
			shareLogEvent.setTapEvent(tapEvent);
			LogContent logContent = tapTargetShareCDCNode.wrapLogContent(shareLogEvent);
			assertEquals(Boolean.FALSE, logContent.getReplaceEvent());
		}
	}

	@Nested
	@DisplayName("Method handleData test")
	class handleDataTest {
		@BeforeEach
		void setUp() {
			when(tapTargetShareCDCNode.handleData(any())).thenCallRealMethod();
		}

		@Test
		@DisplayName("test input null")
		void testNull() {
			Object result = assertDoesNotThrow(() -> tapTargetShareCDCNode.handleData(null));
			assertNull(result);
		}

		@Test
		@DisplayName("test input ObjectId")
		void testObjectId() {
			ObjectId objectId = new ObjectId("663a07b7de52aee7e9a18f3f");
			Object result = tapTargetShareCDCNode.handleData(objectId);
			assertInstanceOf(byte[].class, result);
			assertEquals("[99, 54, 54, 51, 97, 48, 55, 98, 55, 100, 101, 53, 50, 97, 101, 101, 55, 101, 57, 97, 49, 56, 102, 51, 102, 23]", Arrays.toString((byte[]) result));
		}

		@Test
		@DisplayName("test input BsonUndefined")
		void testBsonUndefined() {
			BsonUndefined bsonUndefined = new BsonUndefined();
			Object result = tapTargetShareCDCNode.handleData(bsonUndefined);
			assertNull(result);
		}

		@Test
		@DisplayName("test normal type value")
		void testNormalValueType() {
			Object result = assertDoesNotThrow(() -> tapTargetShareCDCNode.handleData("string"));
			assertEquals("string", result);
			result = assertDoesNotThrow(() -> tapTargetShareCDCNode.handleData(100));
			assertEquals(100, result);
			Date date = new Date();
			result = assertDoesNotThrow(() -> tapTargetShareCDCNode.handleData(date));
			assertEquals(date, result);
		}

		@Test
		@DisplayName("test input instant")
		void testInstant() {
			Instant instant = Instant.parse("2025-02-19T19:26:52.324388Z");
			Object result = assertDoesNotThrow(() -> tapTargetShareCDCNode.handleData(instant));
			assertInstanceOf(byte[].class, tapTargetShareCDCNode.handleData(instant));
			assertEquals("[98, 50, 48, 50, 53, 45, 48, 50, 45, 49, 57, 84, 49, 57, 58, 50, 54, 58, 53, 50, 46, 51, 50, 52, 51, 56, 56, 90, 22]", Arrays.toString((byte[]) result));
		}
	}

	@Nested
	class insertManyTest {
		@BeforeEach
		void setUp() {
			doCallRealMethod().when(tapTargetShareCDCNode).insertMany(any());
		}

		@Test
		@DisplayName("test when error")
		void testWhenError() {
			Map<String, Map<String, List<Document>>> batchCacheData = new ConcurrentHashMap<>();
			ReflectionTestUtils.setField(tapTargetShareCDCNode, "batchCacheData", batchCacheData);
			RuntimeException runtimeException = new RuntimeException("test");
			when(tapTargetShareCDCNode.findInMapByThreadName(eq(batchCacheData), any(Function.class))).thenThrow(runtimeException);
			TaskDto taskDto = new TaskDto();
			taskDto.setName("test task");
			DataProcessorContext dataProcessorContext = DataProcessorContext.newBuilder().withTaskDto(taskDto).build();
			ReflectionTestUtils.setField(tapTargetShareCDCNode, "dataProcessorContext", dataProcessorContext);
			ExternalStorageDto externalStorageDto = mock(ExternalStorageDto.class);
			ReflectionTestUtils.setField(tapTargetShareCDCNode,"externalStorageDto",externalStorageDto);
			when(externalStorageDto.getType()).thenReturn("MongoDB");
			when(externalStorageDto.getName()).thenReturn("testName");

			TapCodeException tapCodeException = assertThrows(TapCodeException.class, () -> tapTargetShareCDCNode.insertMany("test_table"));
			assertEquals(TaskTargetShareCDCProcessorExCode_19.INSERT_MANY_INTO_RINGBUFFER_FAILED, tapCodeException.getCode());
		}
		@Test
		@DisplayName("test when error")
		void testWhenErrorWithoutExternalStorageDto () {
			Map<String, Map<String, List<Document>>> batchCacheData = new ConcurrentHashMap<>();
			ReflectionTestUtils.setField(tapTargetShareCDCNode, "batchCacheData", batchCacheData);
			RuntimeException runtimeException = new RuntimeException("test");
			when(tapTargetShareCDCNode.findInMapByThreadName(eq(batchCacheData), any(Function.class))).thenThrow(runtimeException);
			TaskDto taskDto = new TaskDto();
			taskDto.setName("test task");
			DataProcessorContext dataProcessorContext = DataProcessorContext.newBuilder().withTaskDto(taskDto).build();
			ReflectionTestUtils.setField(tapTargetShareCDCNode, "dataProcessorContext", dataProcessorContext);

			TapCodeException tapCodeException = assertThrows(TapCodeException.class, () -> tapTargetShareCDCNode.insertMany("test_table"));
			assertEquals(TaskTargetShareCDCProcessorExCode_19.INSERT_MANY_INTO_RINGBUFFER_FAILED, tapCodeException.getCode());
		}
		@Test
		void testWhenErrorWithoutDocument () {
			Map<String, Map<String, List<Document>>> batchCacheData = new ConcurrentHashMap<>();
			ReflectionTestUtils.setField(tapTargetShareCDCNode, "batchCacheData", batchCacheData);
			Map map = new HashMap();
 			List documents = new ArrayList<>();
			documents.add(mock(Document.class));
			map.put("test_table", documents);
			when(tapTargetShareCDCNode.findInMapByThreadName(eq(batchCacheData), any(Function.class))).thenReturn(map);
			TaskDto taskDto = new TaskDto();
			taskDto.setName("test task");
			DataProcessorContext dataProcessorContext = DataProcessorContext.newBuilder().withTaskDto(taskDto).build();
			ReflectionTestUtils.setField(tapTargetShareCDCNode, "dataProcessorContext", dataProcessorContext);

			TapCodeException tapCodeException = assertThrows(TapCodeException.class, () -> tapTargetShareCDCNode.insertMany("test_table"));
			assertEquals(TaskTargetShareCDCProcessorExCode_19.INSERT_MANY_INTO_RINGBUFFER_FAILED, tapCodeException.getCode());
		}
	}
	@Nested
	class processShareLogTest{
		@BeforeEach
		void setUp(){
			doCallRealMethod().when(tapTargetShareCDCNode).processShareLog(anyList());
		}
		@DisplayName("test processShareLog exception for UNKNOWN_ERROR")
		@Test
		void test1(){
			List<TapdataShareLogEvent> tapdataShareLogEvents = new ArrayList<>();
			TapdataShareLogEvent tapdataShareLogEvent = new TapdataShareLogEvent();
			HeartbeatEvent heartbeatEvent = new HeartbeatEvent();
			tapdataShareLogEvent.setTapEvent(heartbeatEvent);
			tapdataShareLogEvents.add(tapdataShareLogEvent);
			doThrow(new RuntimeException("error")).when(tapTargetShareCDCNode).handleTapEvents(anyList());
			TapCodeException tapCodeException = assertThrows(TapCodeException.class, () -> {
				tapTargetShareCDCNode.processShareLog(tapdataShareLogEvents);
			});
			assertEquals(tapCodeException.getCode(),TaskTargetShareCDCProcessorExCode_19.UNKNOWN_ERROR);
		}
		@DisplayName("test processShareLog exception for CONVERT_START_TIME_SIGN_OBJ_TO_DOCUMENT_FAILED")
		@Test
		void test2(){
			List<TapdataShareLogEvent> tapdataShareLogEvents = new ArrayList<>();
			TapdataShareLogEvent tapdataShareLogEvent = new TapdataShareLogEvent();
			HeartbeatEvent heartbeatEvent = new HeartbeatEvent();
			tapdataShareLogEvent.setTapEvent(heartbeatEvent);
			tapdataShareLogEvents.add(tapdataShareLogEvent);
			doThrow(new TapCodeException(TaskTargetShareCDCProcessorExCode_19.CONVERT_START_TIME_SIGN_OBJ_TO_DOCUMENT_FAILED)).when(tapTargetShareCDCNode).handleTapEvents(anyList());
			TapCodeException tapCodeException = assertThrows(TapCodeException.class, () -> {
				tapTargetShareCDCNode.processShareLog(tapdataShareLogEvents);
			});
			assertEquals(tapCodeException.getCode(),TaskTargetShareCDCProcessorExCode_19.CONVERT_START_TIME_SIGN_OBJ_TO_DOCUMENT_FAILED);
		}
	}

	@Nested
	class logContent2DocumentTest {
		MockedStatic<TapTargetShareCDCNode> tapTargetShareCDCNodeMockedStatic;

		@BeforeEach
		void setUp() {
			tapTargetShareCDCNodeMockedStatic = mockStatic(TapTargetShareCDCNode.class);
		}

		@Test
		void testConvertLogContentError() {
			tapTargetShareCDCNodeMockedStatic.when(() -> {
				TapTargetShareCDCNode.logContent2Document(any());
			}).thenCallRealMethod();
			try (MockedStatic<MapUtil> mapUtilMockedStatic = mockStatic(MapUtil.class);) {
				mapUtilMockedStatic.when(() -> {
					MapUtil.obj2Document(any());
				}).thenThrow(new RuntimeException("convert failed"));
				LogContent startTimeSign = LogContent.createStartTimeSign();
				TapCodeException tapCodeException = assertThrows(TapCodeException.class, () -> TapTargetShareCDCNode.logContent2Document(startTimeSign));
				assertEquals(TaskTargetShareCDCProcessorExCode_19.CONVERT_LOG_CONTENT_TO_DOCUMENT_FAILED, tapCodeException.getCode());
			}

		}
	}

	@Nested
	class writeDropFieldFunction {
		TapTargetShareCDCNode targetShareCDCNode;

		@BeforeEach
		void init() {

			targetShareCDCNode = mock(TapTargetShareCDCNode.class);

		}
		@DisplayName("test writeTruncateTableFunction normal")
		@Test
		void test1(){
			TapClearTableEvent tapClearTableEvent = TapSimplify.clearTableEvent("testTable");
			AtomicReference<LogContent> ddlLogContent = new AtomicReference<>();
			byte[] bytes = OBJECT_SERIALIZABLE.fromObject(tapClearTableEvent);
			LogContent ddlLogContent1 = LogContent.createDDLLogContent(
					"test",
					System.currentTimeMillis(),
					"ddl",
					"string",
					bytes
			);
			ddlLogContent.set(ddlLogContent1);
			ReflectionTestUtils.setField(targetShareCDCNode,"ddlLogContent",ddlLogContent);
			try (MockedStatic<AspectUtils> aspectUtilsMockedStatic = mockStatic(AspectUtils.class)) {
				TruncateTableFuncAspect globalAspect = null;
				aspectUtilsMockedStatic
						.when(() -> AspectUtils.executeAspect(any(TruncateTableFuncAspect.class)))
						.thenAnswer(invocation -> {
							TruncateTableFuncAspect aspect = invocation.getArgument(0);
							assertEquals(TruncateTableFuncAspect.STATE_START, aspect.getState());

							return null;
						});
				aspectUtilsMockedStatic
						.when(()->AspectUtils.executeDataFuncAspect(any(),any(),any()))
								.thenAnswer(invocationOnMock -> {
									Callable callable=invocationOnMock.getArgument(1);
									callable.call();
									CommonUtils.AnyErrorConsumer argument = invocationOnMock.getArgument(2);
									argument.accept(globalAspect);
									return null;
								});
				doCallRealMethod().when(targetShareCDCNode).writeTruncateTableFunction(any());
				targetShareCDCNode.writeTruncateTableFunction(tapClearTableEvent);
			}

		}
	}
	@Nested
	class TestDoInit {
		private TapTargetShareCDCNode targetShareCDCNodeMock;
		Processor.Context context;
		TapProcessorNodeContext tapProcessorNodeContext;

		@BeforeEach
		void setUp() {
			targetShareCDCNodeMock = mock(TapTargetShareCDCNode.class);
			context = mock(Processor.Context.class);
			tapProcessorNodeContext = mock(TapProcessorNodeContext.class);
			doCallRealMethod().when(targetShareCDCNodeMock).doInit(any(Processor.Context.class), any(TapProcessorNodeContext.class));
			Node node = mock(Node.class);
			when(targetShareCDCNodeMock.getNode()).thenReturn(node);
			when(targetShareCDCNodeMock.getShareCdcTtlDay()).thenReturn(3);
			AtomicReference<String> objectAtomicReference = new AtomicReference<>();
			ReflectionTestUtils.setField(targetShareCDCNodeMock, "constructReferenceId", objectAtomicReference);
			ExternalStorageDto externalStorageDto = new ExternalStorageDto();
			ReflectionTestUtils.setField(targetShareCDCNodeMock, "externalStorageDto", externalStorageDto);
		}
		@DisplayName("test do init convert logContent failed")
		@Test
		void test1() {
			try (MockedStatic<MapUtil> mapUtilMockedStatic = mockStatic(MapUtil.class)) {
				mapUtilMockedStatic.when(() -> {
					MapUtil.obj2Document(any());
				}).thenThrow(new IllegalAccessException("convert failed"));
				TapCodeException tapCodeException = assertThrows(TapCodeException.class, () -> {
					targetShareCDCNodeMock.doInit(context, tapProcessorNodeContext);
				});
				assertEquals(TaskTargetShareCDCProcessorExCode_19.CONVERT_START_TIME_SIGN_OBJ_TO_DOCUMENT_FAILED, tapCodeException.getCode());
			}

		}

		@SneakyThrows
		@DisplayName("test do init write log content failed")
		@Test
		void test2() {
			List<LogCollecotrConnConfig> logCollecotrConnConfigs = new ArrayList<>();
			LogCollecotrConnConfig logCollecotrConnConfig = new LogCollecotrConnConfig();
			List<String> nameSpaces = new ArrayList<>();
			nameSpaces.add("namespaces");
			logCollecotrConnConfig.setNamespace(nameSpaces);
			List<String> tableNames = new ArrayList<>();
			tableNames.add("testTableName");
			logCollecotrConnConfig.setTableNames(tableNames);
			logCollecotrConnConfig.setConnectionId("testConnectionId");
			logCollecotrConnConfigs.add(logCollecotrConnConfig);
			ReflectionTestUtils.setField(targetShareCDCNodeMock, "logCollecotrConnConfigs", logCollecotrConnConfigs);
			when(targetShareCDCNodeMock.isRunning()).thenReturn(true);
			HazelcastConstruct hazelcastConstruct = mock(HazelcastConstruct.class);
			when(hazelcastConstruct.insert(any(Document.class))).thenThrow(new RuntimeException("插入失败"));
			when(hazelcastConstruct.isEmpty()).thenReturn(true);
			when(targetShareCDCNodeMock.getConstruct(anyString(), anyString(), anyString())).thenReturn(hazelcastConstruct);
			TapCodeException tapCodeException = assertThrows(TapCodeException.class, () -> {
				targetShareCDCNodeMock.doInit(context, tapProcessorNodeContext);
			});
			assertEquals(TaskTargetShareCDCProcessorExCode_19.WRITE_START_TIME_SIGN_FAILED, tapCodeException.getCode());
		}
	}
}
