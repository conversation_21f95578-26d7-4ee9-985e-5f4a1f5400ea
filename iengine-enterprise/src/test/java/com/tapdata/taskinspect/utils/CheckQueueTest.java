package com.tapdata.taskinspect.utils;

import com.tapdata.taskinspect.ICheckQueue;
import com.tapdata.taskinspect.vo.CheckItem;
import com.tapdata.tm.taskinspect.TaskInspectUtils;
import com.tapdata.tm.taskinspect.vo.TaskInspectMetrics;
import com.tapdata.tm.taskinspect.vo.MapCreator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 校验队列测试
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/5/13 14:32 Create
 */
class CheckQueueTest {
    private static final Logger logger = LogManager.getLogger(CheckQueueTest.class);

    static final String taskId = "test-task-id";
    static final String tableName = "test_table";
    ICheckQueue checkQueue;

    @BeforeEach
    void
    setup() {
        checkQueue = new CheckQueue(taskId);

        // ----- 用例调优参数 -----
        checkQueue.setCdcSyncDelay(1000);
        checkQueue.setCdcSampleInterval(60 * 1000);
        checkQueue.setCdcSampleLimit(10);
        checkQueue.setCdcMaxUpdateTimes(10);
    }

    /**
     * 增量校验测试
     */
    @Nested
    class CdcTest {
        /**
         * 进队不可阻塞 + 抽样限制
         */
        @Test
        void testOffer_CannotBeBlockedAndOverSampleLimit() {
            long acceptCounts = 0;
            boolean hasSampleLimit = false;

            for (int i = 0; i < 10000; i++) {
                long now = System.currentTimeMillis();
                int offerStatus = checkQueue.offerCdc(now, now, tableName, MapCreator.create("id", String.format("%d", i)));
                if (ICheckQueue.OFFER_CDC_CHECK_YES == offerStatus) {
                    acceptCounts++;
                } else if (ICheckQueue.OFFER_CDC_CHECK_OVER_SAMPLE == offerStatus) {
                    hasSampleLimit = true;
                    break;
                }

                // 进队列性能检查（进队不可阻塞）
                assertTrue(System.currentTimeMillis() - now < 500, "offer queue is blocked");
            }

            // 有返回抽样上限结果
            assertTrue(hasSampleLimit, "not hash sample limit");
            // 抽样限制检查
            assertTrue(acceptCounts < checkQueue.getCdcSampleLimit() * 2L, "sample limit is not work");
        }

        /**
         * 在检查点后消费 + 检查点前更新
         */
        @Test
        void testPoll_ConsumptionAfterCheckpointAndUpdateBeforeCheckpoint() throws ExecutionException, InterruptedException {
            long cdcRate = 100; // 增量事件频率
            int eventCounts = 2; // 测试事件数
            long timeout = checkQueue.getCdcSyncDelay() + 2000; // 测试时长：大于延迟时长，且波动小于 2 秒

            long beginTimes = System.currentTimeMillis(); // 开始时间
            long expectTimes = beginTimes + timeout; // 预期测试最大时长

            AtomicBoolean updateBeforeCheckpoint = new AtomicBoolean(false);
            AtomicBoolean checkItemAfterCheckpoint = new AtomicBoolean(false);

            CompletableFuture.allOf(CompletableFuture.runAsync(() -> {
                try {
                    for (int i = 0; i < eventCounts && System.currentTimeMillis() < expectTimes; i++) {
                        long now = System.currentTimeMillis();
                        checkQueue.offerCdc(now, now, tableName, MapCreator.create("id", "same_id_value"));
                        TimeUnit.MILLISECONDS.sleep(cdcRate);
                    }
                } catch (Exception e) {
                    logger.error("'testPoll_ConsumptionAfterCheckpoint' producer failed with InterruptedException");
                }
            }), CompletableFuture.runAsync(() -> {
                try {
                    while (System.currentTimeMillis() < expectTimes) {
                        CheckItem item = checkQueue.poll();
                        if (null == item) {
                            TimeUnit.MILLISECONDS.sleep(200);
                            continue;
                        }

                        updateBeforeCheckpoint.set(item.getUpdateTimes() > 0);
                        checkItemAfterCheckpoint.set(System.currentTimeMillis() > item.getTs() + checkQueue.getCdcSyncDelay());
                        break;
                    }
                } catch (InterruptedException ignore) {
                    logger.error("'testPoll_ConsumptionAfterCheckpoint' consumer failed with InterruptedException");
                }
            })).get();

            assertTrue(updateBeforeCheckpoint.get(), "can't update ts before checkpoint");
            assertTrue(checkItemAfterCheckpoint.get(), "the check-item can't appear before checkpoint");
        }

        /**
         * 超出更新次数
         */
        @Test
        void testPoll_OverMaxUpdateTimes() throws InterruptedException {
            long loopInterval = 50; // 遍历间隔

            long beginTimes = System.currentTimeMillis(); // 开始时间
            long expectTimes = beginTimes + 5 * 1000; // 预期测试最大时长

            AtomicBoolean overMaxUpdateTimes = new AtomicBoolean(false);
            AtomicBoolean checkItemBeforeCheckpoint = new AtomicBoolean(false);

            // 模拟增量事件
            for (int i = 0; i < checkQueue.getCdcMaxUpdateTimes() * 2; i++) {
                long now = System.currentTimeMillis();
                MapCreator<String, Object> keys = MapCreator.create("id", "same_id_value");
                if (ICheckQueue.OFFER_CDC_CHECK_OVER_UPDATE == checkQueue.offerCdc(now, now, tableName, keys)) {
                    overMaxUpdateTimes.set(true);
                    break;
                }
            }

            // 消费数据
            while (System.currentTimeMillis() < expectTimes) {
                CheckItem item = checkQueue.poll();
                if (null == item) {
                    TimeUnit.MILLISECONDS.sleep(loopInterval);
                    continue;
                }

                checkItemBeforeCheckpoint.set(System.currentTimeMillis() < item.getTs() + checkQueue.getCdcSyncDelay() + loopInterval);
                break;
            }

            assertTrue(overMaxUpdateTimes.get(), "over max update times");
            assertTrue(checkItemBeforeCheckpoint.get(), "the check-item can't appear after checkpoint");
        }
    }

    /**
     * 主流程测试
     */
    @Test
    void testCdcCheckFlow() {
        checkQueue.setCdcSyncDelay(-1000); // 设置增量延迟为 -1000，加快测试

        long cdcOpTs = System.currentTimeMillis();
        long cdcReadTs = System.currentTimeMillis();
        MapCreator<String, Object> keys = MapCreator.create("id", "same_id_value");

        CheckItem checkItem;
        int offerState = checkQueue.offerCdc(cdcReadTs, cdcOpTs, tableName, keys);
        assertEquals(ICheckQueue.OFFER_CDC_CHECK_YES, offerState, "can't offer cdc check item");
        checkItem = checkQueue.poll();
        assertNotNull(checkItem, "not found cdc check item");

        assertTrue(checkQueue.offerCdcRecheck(checkItem.getRowId()), "can't offer cdc recheck item");
        checkItem = checkQueue.poll();
        assertNotNull(checkItem, "not found cdc recheck item");
        checkQueue.difference(checkItem.getRowId());

        assertTrue(checkQueue.offerCdcRecover(checkItem.getRowId()), "can't offer cdc recover item");
        checkItem = checkQueue.poll();
        assertNotNull(checkItem, "not found cdc recover item");

        checkItem = checkQueue.recovered(checkItem.getRowId());
        assertNotNull(checkItem, "can't recovered item");

        assertEquals(0, checkQueue.size(), "can't clear check queue");

        TaskInspectMetrics metrics = checkQueue.nextMetrics();
        assertEquals(1, metrics.getCdcAccepts());
        assertEquals(0, metrics.getCdcIgnores());
        assertEquals(2, metrics.getDiffChanges());
        assertEquals(0, metrics.getDiffToTotals());

        metrics = checkQueue.nextMetrics();
        assertEquals(0, metrics.getDiffChanges(), "the new metrics not clean diff changes");
    }

    @Test
    void testRecovered() {
        checkQueue.setCdcSyncDelay(-1000); // 设置增量延迟为 -1000，加快测试

        long cdcOpTs = System.currentTimeMillis();
        long cdcReadTs = System.currentTimeMillis();
        MapCreator<String, Object> keys = MapCreator.create("id", "same_id_value");
        String rowId = TaskInspectUtils.toRowId(tableName, keys);

        String stepTip;
        int offerState;

        stepTip = "recovery not in queue";
        CheckItem checkItem = checkQueue.recovered(rowId);
        assertNull(checkItem, stepTip);
        assertFalse(checkQueue.contains(rowId), String.format("%s, can't contains in queue", stepTip));

        stepTip = "recovery in cdc queue";
        offerState = checkQueue.offerCdc(cdcReadTs, cdcOpTs, tableName, keys);
        assertEquals(ICheckQueue.OFFER_CDC_CHECK_YES, offerState, String.format("%s, offer state failed", stepTip));
        checkItem = checkQueue.recovered(rowId);
        assertNotNull(checkItem, String.format("%s, can't not recovery", stepTip));
        assertFalse(checkQueue.contains(rowId), String.format("%s, can't contains in queue", stepTip));

        stepTip = "recovery in outside queue";
        offerState = checkQueue.offerCdc(cdcReadTs, cdcOpTs, tableName, keys);
        assertEquals(ICheckQueue.OFFER_CDC_CHECK_YES, offerState, String.format("%s, offer state failed", stepTip));
        checkItem = checkQueue.poll();
        assertNotNull(checkItem, String.format("%s, can't not move to outside queue", stepTip));
    }
}
