package com.tapdata.taskinspect;

import com.tapdata.tm.taskinspect.TaskInspectConfig;
import com.tapdata.tm.taskinspect.exception.TaskInspectRuntimeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/4/16 15:33 Create
 */
@ExtendWith(MockitoExtension.class)
class TaskInspectRemoteServiceTest {

    TaskInspectRemoteService taskInspectRemoteService;

    @Mock
    TaskInspect taskInspect;
    @Mock
    TaskInspectConfig config;

    @BeforeEach
    void setUp() {
        taskInspectRemoteService = new TaskInspectRemoteService();
        reset(taskInspect, config);
    }

    @Nested
    class updateConfigTest {

        @Test
        void testUpdateConfig_TaskInspectNotNull() throws InterruptedException {
            // 预定义
            String taskId = "taskId";
            try (MockedStatic<TaskInspectHelper> inspectHelperMockedStatic = mockStatic(TaskInspectHelper.class)) {
                inspectHelperMockedStatic.when(() -> TaskInspectHelper.get(taskId)).thenReturn(taskInspect);

                // 行为
                taskInspectRemoteService.updateConfig(taskId, config);

                // 预期检查
                verify(taskInspect).refresh(config);
            }
        }

        @Test
        void testUpdateConfig_TaskInspectNull() throws InterruptedException {
            // 预定义
            String taskId = "taskId";
            try (MockedStatic<TaskInspectHelper> inspectHelperMockedStatic = mockStatic(TaskInspectHelper.class)) {
                inspectHelperMockedStatic.when(() -> TaskInspectHelper.get(taskId)).thenReturn(null);

                // 行为
                TaskInspectRuntimeException exception = assertThrows(TaskInspectRuntimeException.class, () -> taskInspectRemoteService.updateConfig(taskId, config));

                // 预期检查
                assertEquals(TaskInspectRuntimeException.CODE_UNINITIALIZED, exception.getCode());
                verify(taskInspect, never()).refresh(config);
            }
        }

        @Test
        void testUpdateConfig_InterruptedException() throws InterruptedException {
            // 预定义
            String taskId = "taskId";
            try (MockedStatic<TaskInspectHelper> inspectHelperMockedStatic = mockStatic(TaskInspectHelper.class)) {
                inspectHelperMockedStatic.when(() -> TaskInspectHelper.get(taskId)).thenReturn(taskInspect);
                doThrow(new InterruptedException("Thread interrupted")).when(taskInspect).refresh(config);

                // 行为
                Exception exception = assertThrows(InterruptedException.class, () -> taskInspectRemoteService.updateConfig(taskId, config));

                // 预期检查
                assertTrue(exception.getMessage().contains("Thread interrupted"));
                verify(taskInspect).refresh(config);
            }
        }
    }
}
