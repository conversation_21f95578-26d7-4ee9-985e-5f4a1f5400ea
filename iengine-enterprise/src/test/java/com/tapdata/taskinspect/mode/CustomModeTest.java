package com.tapdata.taskinspect.mode;

import com.tapdata.taskinspect.ICheckQueue;
import com.tapdata.taskinspect.TaskInspectContext;
import com.tapdata.taskinspect.TaskInspectRemoteService;
import com.tapdata.tm.taskinspect.TaskInspectConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.mockito.Mockito.reset;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>">Harsen</a>
 * @version v1.0 2025/5/23 16:50 Create
 */
@ExtendWith(MockitoExtension.class)
class CustomModeTest {
    CustomMode instance;
    TaskInspectConfig config;

    @Mock
    TaskInspectContext context;
    @Mock
    ICheckQueue checkQueue;

    @BeforeEach
    void setUp() {
        reset(context, checkQueue);
        config = new TaskInspectConfig();
        config.init(-1);
        instance = new CustomMode(context, checkQueue);
    }

    @Test
    void testMainFlow() throws Exception {
        long expectTimeout = System.currentTimeMillis() + 10 * 1000L;

        boolean stopped = false;
        while(System.currentTimeMillis() < expectTimeout) {
            if (instance.stop()) {
                stopped = true;
                break;
            }
            TimeUnit.SECONDS.sleep(1);
        }

        assertTrue(stopped, "timeout");
    }
}
