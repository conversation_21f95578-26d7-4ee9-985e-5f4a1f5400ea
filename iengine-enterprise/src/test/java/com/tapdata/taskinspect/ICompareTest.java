package com.tapdata.taskinspect;

import com.tapdata.tm.taskinspect.cons.DiffTypeEnum;
import com.tapdata.tm.taskinspect.vo.MapCreator;
import com.tapdata.tm.taskinspect.vo.ResultsReportVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.LinkedHashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/4/16 10:11 Create
 */
@ExtendWith(MockitoExtension.class)
class ICompareTest {

    ICompare compare;

    @BeforeEach
    void setUp() {
        compare = ICompare.EMPTY;
    }

    @Nested
    class compareWithEventTest {
        String rowId = "rowId";
        String sourceTable = "sourceTable";
        LinkedHashMap<String, Object> keys = MapCreator.create("id", 1);

        @Test
        void testCompareWithEvent_SourceNull_TargetNotNull() {
            // 预定义
            LinkedHashMap<String, Object> sourceRecord = null;
            String targetTable = "targetTable";
            LinkedHashMap<String, Object> targetRecord = MapCreator.<String, Object>create("id", 1).add("name", "test");

            // 行为
            ResultsReportVo result = compare.compare(
                rowId
                , keys
                , sourceTable
                , sourceRecord
                , targetTable
                , targetRecord
            );

            // 预期检查
            assertNotNull(result);
            assertEquals(DiffTypeEnum.MORE, result.getDiffType());
            assertEquals(rowId, result.getRowId());
            assertEquals(new LinkedHashMap<>(MapCreator.create("id", 1)), result.getKeys());
            assertEquals(sourceTable, result.getSourceTable());
            assertEquals(targetTable, result.getTargetTable());
            assertEquals(targetRecord, result.getTarget());
            assertNull(result.getSource());
            assertNull(result.getDiffFields());
        }

        @Test
        void testCompareWithEvent_SourceNotNull_TargetNull() {
            // 预定义
            LinkedHashMap<String, Object> sourceRecord = new LinkedHashMap<>();
            sourceRecord.put("id", 1);
            sourceRecord.put("name", "test");
            String targetTable = "targetTable";
            LinkedHashMap<String, Object> targetRecord = null;

            // 行为
            ResultsReportVo result = compare.compare(
                rowId
                , keys
                , sourceTable
                , sourceRecord
                , targetTable
                , targetRecord
            );

            // 预期检查
            assertNotNull(result);
            assertEquals(DiffTypeEnum.MISS, result.getDiffType());
            assertEquals(rowId, result.getRowId());
            assertEquals(MapCreator.create("id", 1), result.getKeys());
            assertEquals(sourceTable, result.getSourceTable());
            assertEquals(targetTable, result.getTargetTable());
            assertEquals(sourceRecord, result.getSource());
            assertNull(result.getTarget());
            assertNull(result.getDiffFields());
        }

        @Test
        void testCompareWithEvent_SourceNotNull_TargetNotNull_NoDiff() {
            // 预定义
            LinkedHashMap<String, Object> sourceRecord = new LinkedHashMap<>();
            sourceRecord.put("id", 1);
            sourceRecord.put("name", "test");
            String targetTable = "targetTable";
            LinkedHashMap<String, Object> targetRecord = new LinkedHashMap<>();
            targetRecord.put("id", 1);
            targetRecord.put("name", "test");

            // 行为
            ResultsReportVo result = compare.compare(
                rowId
                , keys
                , sourceTable
                , sourceRecord
                , targetTable
                , targetRecord
            );

            // 预期检查
            assertNull(result);
        }

        @Test
        void testCompareWithEvent_SourceNotNull_TargetNotNull_WithDiff() {
            // 预定义
            LinkedHashMap<String, Object> sourceRecord = new LinkedHashMap<>();
            sourceRecord.put("id", 1);
            sourceRecord.put("name", "test");
            String targetTable = "targetTable";
            LinkedHashMap<String, Object> targetRecord = new LinkedHashMap<>();
            targetRecord.put("id", 1);
            targetRecord.put("name", "different");

            // 行为
            ResultsReportVo result = compare.compare(
                rowId
                , keys
                , sourceTable
                , sourceRecord
                , targetTable
                , targetRecord
            );

            // 预期检查
            assertNotNull(result);
            assertEquals(DiffTypeEnum.DIFF, result.getDiffType());
            assertEquals("rowId", result.getRowId());
            assertEquals(MapCreator.create("id", 1), result.getKeys());
            assertEquals("sourceTable", result.getSourceTable());
            assertEquals(targetTable, result.getTargetTable());
            assertEquals(sourceRecord, result.getSource());
            assertEquals(targetRecord, result.getTarget());
            assertTrue(result.getDiffFields().contains("name"));
        }
    }

    @Nested
    class compareMapTest {

        @Test
        void testCompareMap_NoDiff() {
            // 预定义
            LinkedHashMap<String, Object> source = new LinkedHashMap<>();
            source.put("id", 1);
            source.put("name", "test");
            LinkedHashMap<String, Object> target = new LinkedHashMap<>();
            target.put("id", 1);
            target.put("name", "test");

            // 行为
            List<String> diffFields = compare.compareMap(source, target);

            // 预期检查
            assertTrue(diffFields.isEmpty());
        }

        @Test
        void testCompareMap_WithDiff() {
            // 预定义
            LinkedHashMap<String, Object> source = new LinkedHashMap<>();
            source.put("id", 1);
            source.put("name", "test");
            LinkedHashMap<String, Object> target = new LinkedHashMap<>();
            target.put("id", 1);
            target.put("name", "different");

            // 行为
            List<String> diffFields = compare.compareMap(source, target);

            // 预期检查
            assertTrue(diffFields.contains("name"));
        }
    }

    @Nested
    class compareObjTest {

        @Test
        void testCompareObj_SourceNull_TargetNull() {
            // 预定义
            Object source = null;
            Object target = null;

            // 行为
            int result = compare.compareObj(source, target);

            // 预期检查
            assertEquals(ICompare.COMPARE_OK, result);
        }

        @Test
        void testCompareObj_SourceNull_TargetNotNull() {
            // 预定义
            Object source = null;
            Object target = "test";

            // 行为
            int result = compare.compareObj(source, target);

            // 预期检查
            assertEquals(ICompare.COMPARE_LESS, result);
        }

        @Test
        void testCompareObj_SourceNotNull_TargetNull() {
            // 预定义
            Object source = "test";
            Object target = null;

            // 行为
            int result = compare.compareObj(source, target);

            // 预期检查
            assertEquals(ICompare.COMPARE_LAGE, result);
        }

        @Test
        void testCompareObj_SourceNotNull_TargetNotNull_Equal() {
            // 预定义
            Object source = "test";
            Object target = "test";

            // 行为
            int result = compare.compareObj(source, target);

            // 预期检查
            assertEquals(ICompare.COMPARE_OK, result);
        }

        @Test
        void testCompareObj_SourceNotNull_TargetNotNull_NotEqual() {
            // 预定义
            Object source = "test";
            Object target = "different";

            // 行为
            int result = compare.compareObj(source, target);

            // 预期检查
            assertEquals(ICompare.COMPARE_LESS, result);
        }
    }
}
