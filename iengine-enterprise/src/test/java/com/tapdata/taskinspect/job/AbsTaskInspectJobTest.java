package com.tapdata.taskinspect.job;

import com.tapdata.taskinspect.IOperator;
import com.tapdata.taskinspect.TaskInspectContext;
import com.tapdata.tm.taskinspect.config.IConfig;
import com.tapdata.tm.taskinspect.cons.JobStatusEnum;
import com.tapdata.tm.taskinspect.cons.JobTypeEnum;
import com.tapdata.tm.taskinspect.vo.JobReportVo;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> href="mailto:<EMAIL>">Ha<PERSON></a>
 * @version v1.0 2025/4/17 14:18 Create
 */
@ExtendWith(MockitoExtension.class)
class AbsTaskInspectJobTest {

    static class SampleConfig implements IConfig<SampleConfig> {
        @Override
        public SampleConfig init(int depth) {
            return this;
        }
    }

    @Mock
    private IOperator operator;

    @Mock
    private TaskInspectContext context;

    @Mock
    private SampleConfig config;

    private AbsTaskInspectJob<SampleConfig> job;

    @BeforeEach
    void setUp() {
        job = new AbsTaskInspectJob<SampleConfig>(JobTypeEnum.CDC, config, context) {
            @Override
            protected void doRun(String jobId) {
                // No-op for testing
            }
        };
        reset(operator, context, config);
    }

    @Nested
    class runTest {

        @Test
        void testRunWhenStatusIsNotNone() {
            job.status.set(JobStatusEnum.RUNNING);

            job.run();

            verify(operator, never()).jobStart(anyString(), any(JobTypeEnum.class), any(IConfig.class), any());
            verify(operator, never()).postJobStatus(anyString(), any(JobReportVo.class));
            assertEquals(JobStatusEnum.RUNNING, job.status.get());
        }

        @Test
        void testRunWhenJobStartFails() {
            String taskId = "test-task-id";
            doReturn(taskId).when(context).getTaskId();
            doReturn(operator).when(context).getOperator();
            doThrow(new RuntimeException("Start failed")).when(operator).jobStart(anyString(), any(JobTypeEnum.class), any(IConfig.class), any());

            job.run();

            verify(operator, never()).postJobStatus(anyString(), any(JobReportVo.class));
            assertEquals(JobStatusEnum.START_ERROR, job.status.get());
        }

        @Test
        void testRunWhenJobStartReturnsNull() {
            String taskId = "test-task-id";
            doReturn(taskId).when(context).getTaskId();
            doReturn(operator).when(context).getOperator();
            doReturn(null).when(operator).jobStart(anyString(), any(JobTypeEnum.class), any(), any());

            job.run();

            verify(operator, never()).postJobStatus(anyString(), any(JobReportVo.class));
        }

        @Test
        void testRunWhenJobStartSucceeds() {
            String jobId = new ObjectId().toHexString();

            doReturn(operator).when(context).getOperator();
            AbsTaskInspectJob<SampleConfig> spy = spy(job);
            doReturn(jobId).when(spy).startJob();

            spy.run();

            verify(operator).postJobStatus(eq(jobId), any(JobReportVo.class));
            assertEquals(JobStatusEnum.STOPPED, spy.status.get());
        }

        @Test
        void testRunWhenDoRunThrowsException() throws InterruptedException {
            String jobId = new ObjectId().toHexString();

            doReturn(operator).when(context).getOperator();
            AbsTaskInspectJob<SampleConfig> spy = spy(job);
            doThrow(new RuntimeException("Run failed")).when(spy).doRun(anyString());
            doReturn(jobId).when(spy).startJob();

            spy.run();

            verify(operator).postJobStatus(eq(jobId), any(JobReportVo.class));
            assertEquals(JobStatusEnum.START_ERROR, spy.status.get());
        }
    }

    @Nested
    class stopTest {

        @Test
        void testStopWhenJobIsRunning() {
            job.status.set(JobStatusEnum.RUNNING);

            boolean result = job.stop();

            assertFalse(result);
            assertEquals(JobStatusEnum.STOPPING, job.status.get());
        }

        @Test
        void testStopWhenJobIsNotRunning() {
            job.status.set(JobStatusEnum.STARTING);

            boolean result = job.stop();

            assertFalse(result);
            assertEquals(JobStatusEnum.STARTING, job.status.get());
        }

        @Test
        void testStopWhenJobIsStopped() {
            job.status.set(JobStatusEnum.STOPPED);

            boolean result = job.stop();

            assertTrue(result);
            assertEquals(JobStatusEnum.STOPPED, job.status.get());
        }
    }

    @Nested
    class isRunningTest {

        @Test
        void test_running_notStopping_notForceStop() {
            job.status.set(JobStatusEnum.RUNNING);
            when(context.isStopping()).thenReturn(false);

            boolean result = job.isRunning();

            assertTrue(result);
        }

        @Test
        void test_running_stopping_notForceStop() {
            job.status.set(JobStatusEnum.RUNNING);
            when(context.isStopping()).thenReturn(true);
            when(context.isForceStop()).thenReturn(true);

            boolean result = job.isRunning();

            assertFalse(result);
        }

        @Test
        void test_running_stopping_forceStop() {
            job.status.set(JobStatusEnum.RUNNING);
            when(context.isStopping()).thenReturn(true);
            when(context.isForceStop()).thenReturn(true);

            boolean result = job.isRunning();

            assertFalse(result);
        }

        @Test
        void test_stopped_notStopping() {
            job.status.set(JobStatusEnum.STOPPED);

            boolean result = job.isRunning();

            assertFalse(result);
        }
    }

    @Nested
    class UpdateJobStatusTest {

        @BeforeEach
        void setUp() {
            doReturn(operator).when(context).getOperator();
        }

        @Test
        void shouldUpdateJobStatusAndPostStatusSuccessfully() {
            // 预定义
            String jobId = "job123";
            JobStatusEnum jobStatusEnum = JobStatusEnum.STOPPED;
            String msg = "Job stopped successfully";
            doReturn(true).when(operator).postJobStatus(anyString(), any(JobReportVo.class));

            // 行为
            job.updateJobStatus(jobId, jobStatusEnum, msg);

            // 预期检查
            assertEquals(jobStatusEnum, job.status.get());
            verify(operator).postJobStatus(eq(jobId), any(JobReportVo.class));
        }

        @Test
        void shouldLogErrorWhenPostJobStatusFails() {
            // 预定义
            String jobId = "job123";
            JobStatusEnum jobStatusEnum = JobStatusEnum.START_ERROR;
            String msg = "Failed to start job";
            doThrow(new RuntimeException("Service error")).when(operator).postJobStatus(anyString(), any(JobReportVo.class));

            // 行为
            job.updateJobStatus(jobId, jobStatusEnum, msg);

            // 预期检查
            assertEquals(jobStatusEnum, job.status.get());
        }
    }
}

