package com.tapdata.taskinspect;

import com.tapdata.tm.taskinspect.cons.DiffTypeEnum;
import com.tapdata.tm.taskinspect.vo.ResultsReportVo;
import lombok.NonNull;

import java.util.*;

/**
 * 数据对比实现
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/3/26 18:31 Create
 */
public interface ICompare {

    ICompare EMPTY = new ICompare() {
    };

    /**
     * 相等
     */
    int COMPARE_OK = 0;
    /**
     * 源大
     */
    int COMPARE_LAGE = 1;
    /**
     * 目标大
     */
    int COMPARE_LESS = -1;

    default ResultsReportVo compare(String rowId
        , LinkedHashMap<String, Object> keys
        , String sourceTable
        , LinkedHashMap<String, Object> sourceRecord
        , String targetTable
        , LinkedHashMap<String, Object> targetRecord
    ) {
        // 源记录为空，但目标记录不为空，表示源数据库中不存在此记录，但目标数据库中存在
        if (null == sourceRecord) {
            if (null != targetRecord) {
                // 创建并返回一个DiffRecordVo对象，记录目标数据库中的记录
                return ResultsReportVo.create(DiffTypeEnum.MORE, rowId)
                    .keys(keys)
                    .sourceTable(sourceTable)
                    .targetTable(targetTable)
                    .target(targetRecord);
            }
            // 目标记录为空，但源记录不为空，表示目标数据库中不存在此记录，但源数据库中存在
        } else if (null == targetRecord) {
            // 创建并返回一个DiffRecordVo对象，记录源数据库中的记录
            return ResultsReportVo.create(DiffTypeEnum.MISS, rowId)
                .keys(keys)
                .sourceTable(sourceTable)
                .targetTable(targetTable)
                .source(sourceRecord);
            // 如果源记录和目标记录都不为空，则比较两者是否存在差异
        } else {
            List<String> diffFields = compareMap(sourceRecord, targetRecord);
            // 如果存在差异，创建并返回一个DiffRecordVo对象，记录源数据库和目标数据库中的记录以及差异字段
            if (!diffFields.isEmpty()) {
                return ResultsReportVo.create(DiffTypeEnum.DIFF, rowId)
                    .keys(keys)
                    .sourceTable(sourceTable)
                    .source(sourceRecord)
                    .targetTable(targetTable)
                    .target(targetRecord)
                    .diffFields(diffFields);
            }
        }
        // 如果没有差异，返回null
        return null;
    }

    default @NonNull List<String> compareMap(@NonNull LinkedHashMap<String, Object> source, @NonNull LinkedHashMap<String, Object> target) {
        List<String> diffFields = new ArrayList<>();
        Iterator<Map.Entry<String, Object>> sourceIt = source.entrySet().iterator();
        Iterator<Object> targetIt = target.values().iterator();

        Map.Entry<String, Object> sourceEn;
        Object targetObj;
        while (sourceIt.hasNext() && targetIt.hasNext()) {
            sourceEn = sourceIt.next();
            targetObj = targetIt.next();
            if (0 != compareObj(sourceEn.getValue(), targetObj)) {
                diffFields.add(sourceEn.getKey());
            }
        }
        return diffFields;
    }

    default int compareObj(Object source, Object target) {
        if (null == source) {
            if (null != target) {
                return COMPARE_LESS; // 目标大
            }
            return COMPARE_OK;
        } else if (null == target) {
            return COMPARE_LAGE; // 源大
        } else if (source.equals(target)) {
            return COMPARE_OK;
        }
        return COMPARE_LESS;
    }
}
