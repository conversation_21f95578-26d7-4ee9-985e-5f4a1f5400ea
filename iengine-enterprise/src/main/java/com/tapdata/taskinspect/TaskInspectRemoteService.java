package com.tapdata.taskinspect;

import com.tapdata.tm.taskinspect.TaskInspectConfig;
import com.tapdata.tm.taskinspect.exception.TaskInspectRuntimeException;
import io.tapdata.service.skeleton.annotation.RemoteService;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务内校验 RPC 调用接口
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/3/28 19:41 Create
 */
@Slf4j
@RemoteService
public class TaskInspectRemoteService {

    public void updateConfig(String taskId, TaskInspectConfig config) throws InterruptedException {
        ITaskInspect taskInspect = TaskInspectHelper.get(taskId);
        if (null == taskInspect) {
            throw TaskInspectRuntimeException.uninitialized(taskId);
        }

        taskInspect.refresh(config);
    }
}
