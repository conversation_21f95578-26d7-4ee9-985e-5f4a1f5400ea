package com.tapdata.taskinspect;

import com.tapdata.taskinspect.vo.CheckItem;
import com.tapdata.tm.taskinspect.vo.TaskInspectMetrics;

import java.util.LinkedHashMap;

/**
 * 任务内校验-队列
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/5/13 14:22 Create
 */
public interface ICheckQueue {

    // 基础进队状态
    int OFFER_OVER_CAPACITY = -1;
    int OFFER_SKIP_TABLE = -2;
    int OFFER_SKIP_ROW_ID = -3;
    int OFFER_YES = 0;

    // 增量校验-进队状态
    int OFFER_CDC_CHECK_SKIP_TABLE = OFFER_SKIP_TABLE;
    int OFFER_CDC_CHECK_SKIP_ROW_ID = OFFER_SKIP_ROW_ID;
    int OFFER_CDC_CHECK_OVER_CAPACITY = OFFER_OVER_CAPACITY;
    int OFFER_CDC_CHECK_OVER_SAMPLE = -5;
    int OFFER_CDC_CHECK_YES = OFFER_YES;
    int OFFER_CDC_CHECK_UPDATE = 2;
    int OFFER_CDC_CHECK_OVER_UPDATE = 3;

    boolean isIgnoreTable(String tableName);

    boolean ignoreTable(String tableName);

    int offerCdc(long cdcReadTs, long cdcOpTs, String tableName, LinkedHashMap<String, Object> keys);

    boolean offerCdcRecheck(String rowId);

    boolean offerCdcRecover(String rowId);

//    int offerManualRecheck(String jobId, String userId, String userName, String rowId);
//
//    int offerManualRecover(String jobId, String userId, String userName, String rowId);

    CheckItem pollHighPriority();

    CheckItem poll();

//    CheckQueueItem poll(long timeout, TimeUnit unit) throws InterruptedException;

    void difference(String rowId);

    CheckItem recovered(String rowId);

    boolean contains(String rowId);

    boolean isWaitPoll(String rowId);

    int size();

    TaskInspectMetrics nextMetrics();

    // ----- 以下是队列配置控制 -----

    int getCapacity();

    void setCapacity(int capacity);

    long getCdcTimeout();

    void setCdcTimeout(long timeout);

    int getCdcMaxUpdateTimes();

    void setCdcMaxUpdateTimes(int cdcMaxUpdateTimes);

    long getCdcSyncDelay();

    void setCdcSyncDelay(long cdcSyncDelay);

    long getCdcSampleInterval();

    void setCdcSampleInterval(long cdcSampleInterval);

    long getCdcSampleLimit();

    void setCdcSampleLimit(long cdcSampleLimit);

}
