package com.tapdata.taskinspect;

import com.tapdata.entity.TapdataEvent;
import com.tapdata.entity.TapdataRecoveryEvent;
import com.tapdata.entity.task.context.DataProcessorContext;
import com.tapdata.mongo.HttpClientMongoOperator;
import com.tapdata.taskinspect.job.AbsInnerJob;
import com.tapdata.taskinspect.job.inner.HighPriorityCheckerJob;
import com.tapdata.taskinspect.mode.CustomMode;
import com.tapdata.taskinspect.utils.CheckQueue;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.taskinspect.TaskInspectConfig;
import com.tapdata.tm.taskinspect.TaskInspectMode;
import com.tapdata.tm.taskinspect.TaskInspectUtils;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 任务内校验入口
 *
 * <AUTHOR> href="mailto:<EMAIL>">Harsen</a>
 * @version v1.0 2025/3/17 11:44 Create
 */
public class TaskInspect implements ITaskInspect {
    private static final Logger log = LogManager.getLogger(TaskInspect.class);
    protected static final long MAX_TIMEOUT = TimeUnit.SECONDS.toMillis(60);
    private final TaskInspectContext context;
    private final TaskInspectChecker checker;
    private final ICheckQueue checkQueue;
    private HighPriorityCheckerJob highPriorityCheckerJob;
    private ITaskInspectMode modeJob;

    public TaskInspect(TaskDto task, HttpClientMongoOperator clientMongoOperator) {
        this.context = new TaskInspectContext(task, clientMongoOperator);
        this.checkQueue = new CheckQueue(context.getTaskId());
        this.modeJob = create(TaskInspectMode.CLOSE, context, checkQueue);
        this.checker = new TaskInspectChecker(context, checkQueue);
        this.context.setChecker(checker);
        init();
    }

    protected void init() {
        try {
            this.highPriorityCheckerJob = new HighPriorityCheckerJob(context, checkQueue);
            TaskInspectUtils.submit(this.highPriorityCheckerJob);
            IOperator operator = context.getOperator();
            String taskId = context.getTaskId();
            TaskInspectConfig config = operator.getConfig(taskId);
            if (null != config) {
                refresh(config);
            }
        } catch (InterruptedException ignore) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("{} init failed: {}", TaskInspectUtils.MODULE_NAME, e.getMessage(), e);
        }
    }

    @Override
    public void setSyncDelay(long syncDelay) {
        checkQueue.setCdcSyncDelay(syncDelay + 1000);
    }

    @Override
    public void acceptCdcEvent(DataProcessorContext dataProcessorContext, TapdataEvent event) {
        if (event instanceof TapdataRecoveryEvent) return;

        if (event.getTapEvent() instanceof TapInsertRecordEvent recordEvent) {
            LinkedHashMap<String, Object> keys = getKeys(dataProcessorContext, recordEvent.getTableId(), recordEvent.getAfter());
            if (!keys.isEmpty()) {
                modeJob.acceptCdcEvent(
                    Optional.ofNullable(recordEvent.getReferenceTime()).orElseGet(System::currentTimeMillis)
                    , Optional.ofNullable(recordEvent.getTime()).orElseGet(System::currentTimeMillis)
                    , recordEvent.getTableId()
                    , keys
                );
            }
        } else if (event.getTapEvent() instanceof TapUpdateRecordEvent recordEvent) {
            LinkedHashMap<String, Object> keys = getKeys(dataProcessorContext, recordEvent.getTableId(), recordEvent.getAfter());
            String afterRowId = TaskInspectUtils.toRowId(recordEvent.getTableId(), keys);
            if (!keys.isEmpty()) {
                modeJob.acceptCdcEvent(
                    Optional.ofNullable(recordEvent.getReferenceTime()).orElseGet(System::currentTimeMillis)
                    , Optional.ofNullable(recordEvent.getTime()).orElseGet(System::currentTimeMillis)
                    , recordEvent.getTableId()
                    , keys
                );
            }
            keys = getKeys(dataProcessorContext, recordEvent.getTableId(), recordEvent.getBefore());
            if (!keys.isEmpty()) {
                String beforeRowId = TaskInspectUtils.toRowId(recordEvent.getTableId(), keys);
                if (!afterRowId.equals(beforeRowId)) {
                    modeJob.acceptCdcEvent(
                        Optional.ofNullable(recordEvent.getReferenceTime()).orElseGet(System::currentTimeMillis)
                        , Optional.ofNullable(recordEvent.getTime()).orElseGet(System::currentTimeMillis)
                        , recordEvent.getTableId()
                        , keys
                    );
                }
            }
        } else if (event.getTapEvent() instanceof TapDeleteRecordEvent recordEvent) {
            LinkedHashMap<String, Object> keys = getKeys(dataProcessorContext, recordEvent.getTableId(), recordEvent.getBefore());
            if (!keys.isEmpty()) {
                modeJob.acceptCdcEvent(
                    Optional.ofNullable(recordEvent.getReferenceTime()).orElseGet(System::currentTimeMillis)
                    , Optional.ofNullable(recordEvent.getTime()).orElseGet(System::currentTimeMillis)
                    , recordEvent.getTableId()
                    , keys
                );
            }
        }
    }

    /**
     * 刷新任务检查配置
     * <p>
     * 该方法用于更新任务检查配置，确保当前的检查模式与配置中指定的模式一致
     * 如果模式不一致，将停止当前模式的检查，并根据新配置的模式重新初始化检查模式
     *
     * @param config 新的任务检查配置
     * @throws InterruptedException 如果在等待模式停止时被中断
     */
    @Override
    public synchronized void refresh(TaskInspectConfig config) throws InterruptedException {
        if (context.isStopping()) return;
        // 初始化配置，保证取值不异常
        config.init(-1);

        // 获取新的检查模式
        TaskInspectMode mode = config.getMode();

        // 检查当前模式与新配置的模式是否不同
        if (modeJob.getMode() != mode) {
            // 如果模式不同，优雅地停止当前模式的检查，并等待其停止完成
            TaskInspectUtils.stop(MAX_TIMEOUT, () -> modeJob.stop());
            // 根据新的检查模式创建并初始化新的检查模式实例
            modeJob = create(mode, context, checkQueue);
        }

        // 使用新的配置刷新当前检查模式
        checkQueue.setCapacity(config.getQueueCapacity());
        checkQueue.setCdcTimeout(config.getCdcTimeout());
        checkQueue.setCdcMaxUpdateTimes(config.getCdcMaxUpdateTimes());
        modeJob.refresh(config);
    }

    @Override
    public boolean stop(boolean force) {
        context.setStop(force);

        AtomicBoolean isStop = new AtomicBoolean(true);
        isStop.compareAndSet(true, modeJob.stop());
        isStop.compareAndSet(true, Optional.ofNullable(this.highPriorityCheckerJob).map(AbsInnerJob::stop).orElse(true));
        return isStop.get();
    }

    @Override
    public void stop(long timeout) throws InterruptedException {
        TaskInspectUtils.stop(timeout, () -> stop(context.isForceStop()));
    }

    /**
     * 关闭资源释放实例
     * <p>
     * 此方法确保在释放与任务关联的资源时，还从全局实例管理中移除对应的实例
     * 它首先尝试通过调用stop方法来停止任务，然后无论停止操作是否成功，都会从INSTANCES中移除任务的实例
     *
     * @throws Exception 如果关闭过程中有异常发生
     */
    @Override
    public void close() throws Exception {
        log.info("{} release instance...", TaskInspectUtils.MODULE_NAME);
        try {
            // 尝试停止任务，使用最大超时时间
            stop(MAX_TIMEOUT);
        } finally {
            // 确保在任何情况下都移除任务实例
            TaskInspectHelper.remove(context.getTaskId());
            TaskInspectUtils.close(checker);
        }
    }

    /**
     * 根据检查模式创建具体的作业执行策略实例
     * 此方法首先尝试根据提供的模式类名实例化对象如果失败，将尝试使用默认模式进行实例化
     *
     * @param mode       检查模式，包含实现类名
     * @param context    任务检查上下文，传递给构造函数
     * @param checkQueue 校验列队
     * @return IModeJob 实例，用于执行特定模式的检查任务
     */
    protected static ITaskInspectMode create(TaskInspectMode mode, TaskInspectContext context, ICheckQueue checkQueue) {
        // 获取模式的具体实现类名
        return switch (mode) {
            case CUSTOM -> new CustomMode(context, checkQueue);
            default -> ITaskInspectMode.EMPTY;
        };
    }


    protected LinkedHashMap<String, Object> getKeys(DataProcessorContext dataProcessorContext, String tableId, Map<String, Object> data) {
        LinkedHashMap<String, Object> result = new LinkedHashMap<>();

        // 数据为空直接返回
        if (null == data) {
            return result;
        }

        Optional.ofNullable(dataProcessorContext.getTapTableMap())
            .map(tapTableMap -> tapTableMap.get(tableId))
            .map(tapTable -> tapTable.primaryKeys(true))
            .ifPresent(keys -> {
                for (String k : keys) {
                    result.put(k, data.get(k));
                }
            });
        return result;
    }
}
