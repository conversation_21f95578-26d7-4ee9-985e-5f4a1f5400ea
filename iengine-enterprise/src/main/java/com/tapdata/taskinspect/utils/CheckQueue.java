package com.tapdata.taskinspect.utils;

import com.tapdata.taskinspect.ICheckQueue;
import com.tapdata.taskinspect.vo.CheckItem;
import com.tapdata.tm.taskinspect.TaskInspectUtils;
import com.tapdata.tm.taskinspect.exception.TaskInspectRuntimeException;
import com.tapdata.tm.taskinspect.vo.TaskInspectMetrics;
import com.tapdata.tm.utils.TimeMaxAccepter;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentSkipListMap;

/**
 * 任务内校验-检查队列
 *
 * <AUTHOR> href="mailto:<EMAIL>">Ha<PERSON></a>
 * @version v1.0 2025/5/13 16:29 Create
 */
public class CheckQueue implements ICheckQueue {

    private final String taskId;
    private final TimeMaxAccepter cdcSampleRate; // 控制抽样频率
    private final Set<String> ignoreTables = new HashSet<>();
    private final ConcurrentSkipListMap<String, CheckItem> outsideQueueMap = new ConcurrentSkipListMap<>();
    private final ConcurrentSkipListMap<String, CheckItem> highPriorityMap = new ConcurrentSkipListMap<>();
    private final ConcurrentSkipListMap<String, CheckItem> cdcCheckMap = new ConcurrentSkipListMap<>();

    private final Set<String> metricRowSet;
    private long metricAccepts;
    private long metricIgnores;
    private int metricChangeTotals; // 最后一次轮询是否存在差异变化
    private long metricFirstTs; // 第一次发生差异的时间
    private long metricLastTs;
    private int metricLastTotals;

    @Setter
    @Getter
    private int capacity = 1000;
    @Setter
    @Getter
    private long cdcTimeout = 60 * 1000L;
    @Setter
    @Getter
    private int cdcMaxUpdateTimes = 10;
    @Setter
    @Getter
    private long cdcSyncDelay = 1000;

    public CheckQueue(String taskId) {
        this.taskId = taskId;
        this.cdcSampleRate = new TimeMaxAccepter(1000, 10); // 控制抽样频率
        this.metricRowSet = new HashSet<>();
        this.metricAccepts = 0L;
        this.metricIgnores = 0L;
        this.metricChangeTotals = 0;
        this.metricFirstTs = System.currentTimeMillis();
        this.metricLastTs = System.currentTimeMillis();
        this.metricLastTotals = 0;
    }

    @Override
    public boolean isIgnoreTable(String tableName) {
        return ignoreTables.contains(tableName);
    }

    @Override
    public boolean ignoreTable(String tableName) {
        return ignoreTables.add(tableName);
    }

    @Override
    public synchronized int offerCdc(long cdcReadTs, long cdcOpTs, String tableName, LinkedHashMap<String, Object> keys) {
        if (isIgnoreTable(tableName)) {
            metricIgnores++;
            return OFFER_CDC_CHECK_SKIP_TABLE;
        }

        String rowId = TaskInspectUtils.toRowId(tableName, keys);
        // 不能生成行号，退出
        if (rowId.isEmpty()) {
            metricIgnores++;
            return OFFER_CDC_CHECK_SKIP_ROW_ID;
        }

        // 在高优队列中，更新并返回
        CheckItem item = highPriorityMap.get(rowId);
        if (null != item) {
            updateItem(item, cdcOpTs);
            metricAccepts++;
            return OFFER_CDC_CHECK_UPDATE;
        }

        item = cdcCheckMap.get(rowId);
        if (null == item) {
            // 超出增量抽样阈值
            if (!cdcSampleRate.check()) {
                metricIgnores++;
                return OFFER_CDC_CHECK_OVER_SAMPLE;
            }

            CheckItem removeItem = outsideQueueMap.remove(rowId);
            if (null == removeItem) {
                // 超出队列容量
                if (size() >= getCapacity()) {
                    metricIgnores++;
                    return OFFER_CDC_CHECK_OVER_CAPACITY;
                }
            }

            // 移除检查项，重新添加
            item = CheckItem.of(rowId, cdcReadTs, cdcOpTs, tableName, keys);
            cdcCheckMap.put(rowId, item);
            metricAccepts++;
            return OFFER_CDC_CHECK_YES;
        }

        CheckItem removeItem = cdcCheckMap.remove(rowId);
        if (item.equals(removeItem)) {
            metricAccepts++;
            updateItem(item, cdcOpTs);

            // 增加并判断超出最大更新次数，超出则直接返回
            if (item.incrementalUpdateTimesGet() > getCdcMaxUpdateTimes()) {
                highPriorityMap.put(rowId, item);
                return OFFER_CDC_CHECK_OVER_UPDATE;
            }

            // 没有超出更新次数：重新进增量检查队列
            cdcCheckMap.put(rowId, item);
            return OFFER_CDC_CHECK_UPDATE;
        }

        metricIgnores++;
        throw TaskInspectRuntimeException.logicError(taskId, "the offer-cdc-item is not remove-item");
    }

    @Override
    public synchronized boolean offerCdcRecheck(String rowId) {
        if (highPriorityMap.containsKey(rowId)) {
            return true;
        }

        CheckItem item = cdcCheckMap.remove(rowId);
        if (null == item) {
            item = outsideQueueMap.remove(rowId);
            if (null == item) {
                return false;
            }
        }

        item.setTs(System.currentTimeMillis());
        item.setStep(CheckItem.STEP_RECHECK);
        cdcCheckMap.put(rowId, item);
        return true;
    }

    @Override
    public synchronized boolean offerCdcRecover(String rowId) {
        if (highPriorityMap.containsKey(rowId)) {
            return true;
        }

        CheckItem item = cdcCheckMap.remove(rowId);
        if (null == item) {
            item = outsideQueueMap.remove(rowId);
            if (null == item) {
                return false;
            }
        }

        item.setTs(System.currentTimeMillis());
        item.setStep(CheckItem.STEP_RECOVER);
        cdcCheckMap.put(rowId, item);
        return true;
    }

    @Override
    public synchronized CheckItem pollHighPriority() {
        Map.Entry<String, CheckItem> entry = highPriorityMap.pollLastEntry();
        if (null == entry) {
            return null;
        }
        CheckItem item = entry.getValue();
        outsideQueueMap.put(entry.getKey(), entry.getValue());
        return item.clone();
    }

    @Override
    public synchronized CheckItem poll() {
        CheckItem item = pollHighPriority();
        if (null != item) {
            return item.clone();
        }

        boolean isSkip = true;
        Map.Entry<String, CheckItem> entry = cdcCheckMap.lastEntry();
        if (null != entry) {
            item = entry.getValue();

            if (item.getUpdateTimes() >= getCdcMaxUpdateTimes()) {
                isSkip = false; // 超出更新次数
            } else if (getCdcTimeout() < System.currentTimeMillis() - item.getCdcReadTs()) {
                isSkip = false; // 进队超时
            } else if (System.currentTimeMillis() - item.getTs() > getCdcSyncDelay()) {
                isSkip = false; // 到达检查点
            }
        }

        if (isSkip) {
            return null;
        }

        Map.Entry<String, CheckItem> removeEntry = cdcCheckMap.pollLastEntry();
        if (entry.equals(removeEntry)) {
            outsideQueueMap.put(removeEntry.getKey(), removeEntry.getValue());
            return item.clone();
        }

        cdcCheckMap.put(removeEntry.getKey(), removeEntry.getValue());
        throw TaskInspectRuntimeException.logicError(taskId, "the poll-item is not remove-item");
    }

    @Override
    public synchronized void difference(String rowId) {
        if (contains(rowId) && metricRowSet.add(rowId)) {
            metricChangeTotals++;
        }
    }

    @Override
    public synchronized CheckItem recovered(String rowId) {
        if (metricRowSet.remove(rowId)) {
            metricChangeTotals++;
        }

        CheckItem removeItem = outsideQueueMap.remove(rowId);
        if (null == removeItem) {
            removeItem = cdcCheckMap.remove(rowId);
            if (null == removeItem) {
                removeItem = highPriorityMap.remove(rowId);
            }
        }
        return removeItem;
    }

    @Override
    public synchronized boolean contains(String rowId) {
        return isWaitPoll(rowId) || outsideQueueMap.containsKey(rowId);
    }

    @Override
    public synchronized boolean isWaitPoll(String rowId) {
        return cdcCheckMap.containsKey(rowId) || highPriorityMap.containsKey(rowId);
    }

    @Override
    public synchronized int size() {
        return cdcCheckMap.size() + outsideQueueMap.size() + highPriorityMap.size();
    }

    @Override
    public synchronized TaskInspectMetrics nextMetrics() {
        TaskInspectMetrics ins = new TaskInspectMetrics(
            metricAccepts,
            metricIgnores,
            metricChangeTotals,
            metricFirstTs,
            metricLastTs,
            metricLastTotals,
            System.currentTimeMillis(),
            metricRowSet.size()
        );
        metricChangeTotals = 0;
        metricLastTs = ins.getDiffToTs();
        metricLastTotals = ins.getDiffToTotals();

        // 差异恢复，重设差异开始时间
        if (ins.getDiffToTotals() <= 0) {
            metricFirstTs = System.currentTimeMillis();
        }
        return ins;
    }

    @Override
    public long getCdcSampleInterval() {
        return cdcSampleRate.getTimes() / 1000;
    }

    @Override
    public void setCdcSampleInterval(long cdcSampleInterval) {
        cdcSampleRate.setTimes(cdcSampleInterval * 1000L);
    }

    @Override
    public long getCdcSampleLimit() {
        return cdcSampleRate.getMax();
    }

    @Override
    public void setCdcSampleLimit(long cdcSampleLimit) {
        cdcSampleRate.setMax(cdcSampleLimit);
    }

    private void updateItem(CheckItem item, long cdcOpTs) {
        item.setTs(System.currentTimeMillis());
        item.setCdcOpTs(cdcOpTs);
    }
}
