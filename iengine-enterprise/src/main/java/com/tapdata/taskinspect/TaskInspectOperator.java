package com.tapdata.taskinspect;

import com.tapdata.mongo.HttpClientMongoOperator;
import com.tapdata.tm.taskinspect.TaskInspectConfig;
import com.tapdata.tm.taskinspect.cons.JobStatusEnum;
import com.tapdata.tm.taskinspect.cons.JobTypeEnum;
import com.tapdata.tm.taskinspect.dto.TaskInspectHistoriesDto;
import com.tapdata.tm.taskinspect.dto.TaskInspectResultsDto;
import com.tapdata.tm.taskinspect.vo.JobReportVo;
import com.tapdata.tm.taskinspect.vo.MapCreator;
import lombok.Getter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.mongodb.core.query.Query;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/3/24 12:17 Create
 */
public class TaskInspectOperator implements IOperator {
    private static final Logger log = LogManager.getLogger(TaskInspectOperator.class);

    @Getter
    private final HttpClientMongoOperator operator;

    public TaskInspectOperator(HttpClientMongoOperator operator) {
        this.operator = operator;
    }

    @Override
    public TaskInspectConfig getConfig(String taskId) {
        Query query = new Query();
        String url = String.format("task-inspect/%s", taskId);

        HttpClientMongoOperator httpOperator = getOperator();
        return httpOperator.findOne(query, url, TaskInspectConfig.class);
    }

    @Override
    public TaskInspectHistoriesDto jobStart(String taskId, JobTypeEnum type, Serializable config, Map<String, Serializable> ext) {
        String url = String.format("task-inspect/%s/histories", taskId);
        MapCreator<String, Object> data = MapCreator
            .<String, Object>create(TaskInspectHistoriesDto.FIELD_TYPE, type.name())
            .add(TaskInspectHistoriesDto.FIELD_STATUS, JobStatusEnum.RUNNING.name())
            .add(TaskInspectHistoriesDto.FIELD_CONFIG, config)
            .add(TaskInspectHistoriesDto.FIELD_BEGIN_TIME, System.currentTimeMillis())
            .add(TaskInspectHistoriesDto.FIELD_PING_TIME, System.currentTimeMillis());
        if (null != ext) {
            data.putAll(ext);
        }

        HttpClientMongoOperator httpOperator = getOperator();
        return httpOperator.postOne(data, url, TaskInspectHistoriesDto.class);
    }

    @Override
    public boolean postJobStatus(String jobId, JobReportVo vo) {
        String url = String.format("task-inspect-histories/%s/status/report", jobId);
        HttpClientMongoOperator httpOperator = getOperator();
        Boolean bool = httpOperator.postOne(vo, url, Boolean.class);
        return Boolean.TRUE.equals(bool);
    }

    @Override
    public void recoverResult(String taskId, LinkedHashMap<String, Object> reportData) {
        String rowId = reportData.get(TaskInspectResultsDto.FIELD_ROW_ID).toString();
        String url = String.format("task-inspect/%s/recover-result/%s", taskId, rowId);
        Boolean isOk = operator.postOne(reportData, url, Boolean.class);
    }

    @Override
    public void reportResult(String taskId, LinkedHashMap<String, Object> reportData) {
        String url = String.format("task-inspect/%s/report-result", taskId);
        Boolean isOk = operator.postOne(reportData, url, Boolean.class);
    }
}
