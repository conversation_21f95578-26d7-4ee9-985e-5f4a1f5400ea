package com.tapdata.taskinspect;

import com.tapdata.entity.TapdataRecoveryEvent;
import com.tapdata.pdk.IPdkConnector;
import com.tapdata.pdk.TaskPdkConnector;
import com.tapdata.tm.taskinspect.TaskInspectUtils;
import com.tapdata.tm.taskinspect.exception.TaskInspectNotFoundTableFieldMappingException;
import com.tapdata.tm.taskinspect.exception.TaskInspectNotFoundTableMappingException;
import com.tapdata.tm.taskinspect.vo.ResultOperationsVo;
import com.tapdata.tm.taskinspect.vo.ResultsReportVo;
import com.tapdata.tm.vo.TaskNodeTableFieldTraceVo;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.inspect.AutoRecovery;
import io.tapdata.inspect.AutoRecoveryClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * 任务内校验-对比器
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/5/11 21:07 Create
 */
public class TaskInspectChecker implements AutoCloseable {
    private static final Logger log = LogManager.getLogger(TaskInspectChecker.class);

    private final String id;
    private final ICompare compare = ICompare.EMPTY;
    private final TaskInspectContext context;
    private final ICheckQueue checkQueue;
    private final TaskPdkConnector taskPdkConnector;
    private IPdkConnector sourceConnector;
    private IPdkConnector targetConnector;
    private AutoRecoveryClient autoRecoveryClient;
    private Map<String, TaskNodeTableFieldTraceVo> tableFieldTrace;


    public TaskInspectChecker(TaskInspectContext context, ICheckQueue checkQueue) {
        this.context = context;
        this.checkQueue = checkQueue;
        this.id = UUID.randomUUID().toString();
        this.taskPdkConnector = TaskPdkConnector.of(context.getTask());
    }

    public void init() throws Exception {
        this.sourceConnector = this.taskPdkConnector.createSource(String.format("%s-%s-checker-source", context.getTaskId(), TaskInspectUtils.MODULE_NAME));
        this.sourceConnector.init();

        this.targetConnector = this.taskPdkConnector.createTarget(String.format("%s-%s-checker-target", context.getTaskId(), TaskInspectUtils.MODULE_NAME));
        this.targetConnector.init();

        this.autoRecoveryClient = AutoRecovery.initClient(context.getTaskId(), this.id, tapdataRecoveryEvent -> {
            String rowId = tapdataRecoveryEvent.getRowId();
            checkQueue.offerCdcRecover(rowId);
        });

        this.tableFieldTrace = createTableFieldTrace();
    }

    public ResultsReportVo compare(String rowId, String tableName, LinkedHashMap<String, Object> sourceKeys) throws TaskInspectNotFoundTableMappingException, TaskInspectNotFoundTableFieldMappingException {
        initFirst();
        TaskNodeTableFieldTraceVo fieldTraceVo = tableFieldTrace.get(tableName);
        // 没有映射的表不校验
        if (null == fieldTraceVo) {
            throw new TaskInspectNotFoundTableMappingException(context.getTaskId(), tableName);
        }

        // 根据主键查询数据
        String sourceTableName = fieldTraceVo.getSourceTable();
        List<String> sourceFields = fieldTraceVo.getSourceFields();
        LinkedHashMap<String, Object> sourceRecord = sourceConnector.findOneByKeys(sourceTableName, sourceKeys, sourceFields);

        String targetTable = fieldTraceVo.getTargetTable();
        List<String> targetFields = fieldTraceVo.getTargetFields();
        LinkedHashMap<String, Object> targetKeys = doProcessKeys(fieldTraceVo, sourceKeys);
        if (targetKeys.isEmpty()) {
            throw new TaskInspectNotFoundTableFieldMappingException(context.getTaskId(), tableName);
        }
        LinkedHashMap<String, Object> targetRecord = targetConnector.findOneByKeys(targetTable, targetKeys, targetFields);

        // 对比数据并上报结果
        ResultsReportVo reportVo = compare.compare(rowId, sourceKeys, sourceTableName, sourceRecord, targetTable, targetRecord);
        if (null != reportVo) {
            reportVo.setSourceFields(sourceFields);
            reportVo.setTargetFields(targetFields);
        }
        return reportVo;
    }

    public void sendRecover(String rowId, String tableName, LinkedHashMap<String, Object> keys) throws TaskInspectNotFoundTableMappingException {
        context.getResultReporter().reportOperation(rowId, ResultOperationsVo.OP_AUTO_RECOVER, context.getUserId(), context.getUserName(), "");

        TaskNodeTableFieldTraceVo fieldTraceVo = tableFieldTrace.get(tableName);
        // 没有映射的表不校验
        if (null == fieldTraceVo) {
            throw new TaskInspectNotFoundTableMappingException(context.getTaskId(), tableName);
        }

        List<String> sourceFields = fieldTraceVo.getSourceFields();
        LinkedHashMap<String, Object> sourceRecord = sourceConnector.findOneByKeys(tableName, keys, sourceFields);
        if (null == sourceRecord) {
            log.warn("row data is deleted");
        } else {
            autoRecoveryClient.enqueue(TapdataRecoveryEvent.createInsert(this.id, rowId, tableName, sourceRecord));
        }
    }

    @Override
    public void close() throws Exception {
        TaskInspectUtils.close(sourceConnector, targetConnector, autoRecoveryClient);
    }

    protected synchronized void initFirst() {
        if (null == tableFieldTrace) {
            try {
                TaskInspectUtils.close(sourceConnector, targetConnector, autoRecoveryClient);
                init();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    protected Map<String, TaskNodeTableFieldTraceVo> createTableFieldTrace() {
        Map<String, TaskNodeTableFieldTraceVo> tableFieldTrace = new HashMap<>();
        targetConnector.eachAllTable(tapTable -> {
            String sourceTableName = tapTable.getAncestorsName();
            if (Optional.ofNullable(tapTable.primaryKeys(true)).map(Collection::isEmpty).orElse(true)) {
                log.info("ignore inspect table '{}' because not exists logic keys", sourceTableName);
            } else {
                Set<String> sourceFields = Optional.ofNullable(sourceConnector.getTapTable(sourceTableName))
                    .map(TapTable::getNameFieldMap)
                    .map(LinkedHashMap::keySet).orElse(new HashSet<>());
                TaskNodeTableFieldTraceVo fieldTrace = TaskNodeTableFieldTraceVo.ofTargetTable(tapTable, sourceFields);
                if (null == fieldTrace) {
                    log.info("ignore inspect table '{}' because not exists fields", sourceTableName);
                } else {
                    tableFieldTrace.put(tapTable.getAncestorsName(), fieldTrace);
                }
            }
            return true;
        });
        return tableFieldTrace;
    }

    protected LinkedHashMap<String, Object> doProcessKeys(TaskNodeTableFieldTraceVo fieldTraceVo, LinkedHashMap<String, Object> sourceKeys) {
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        for (Map.Entry<String, Object> en : sourceKeys.entrySet()) {
            String targetField = fieldTraceVo.getFieldMap().get(en.getKey());
            // 主键没映射，忽略
            if (null == targetField) {
                resultMap.clear();
                break;
            }

            resultMap.put(targetField, en.getValue());
        }
        return resultMap;
    }
}
