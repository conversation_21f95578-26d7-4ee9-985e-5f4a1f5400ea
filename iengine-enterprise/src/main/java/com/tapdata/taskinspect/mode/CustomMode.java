package com.tapdata.taskinspect.mode;

import com.tapdata.taskinspect.ICheckQueue;
import com.tapdata.taskinspect.ITaskInspectMode;
import com.tapdata.taskinspect.TaskInspectContext;
import com.tapdata.taskinspect.job.inner.ResultReporterJob;
import com.tapdata.tm.taskinspect.TaskInspectConfig;
import com.tapdata.tm.taskinspect.TaskInspectMode;
import com.tapdata.tm.taskinspect.TaskInspectUtils;
import com.tapdata.tm.taskinspect.config.Custom;
import com.tapdata.tm.taskinspect.exception.TaskInspectRuntimeException;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/3/18 11:12 Create
 */
public class CustomMode implements ITaskInspectMode {
    private final TaskInspectContext context;
    private final ResultReporterJob resultReporterJob;
    private final CustomCdcMode cdcMode;
    @Getter
    private Custom config;

    public CustomMode(TaskInspectContext context, ICheckQueue checkQueue) {
        this.context = context;
        this.cdcMode = new CustomCdcMode(context, checkQueue);

        this.resultReporterJob = new ResultReporterJob(context);
        TaskInspectUtils.submit(this.resultReporterJob);
        context.setResultReporter(this.resultReporterJob);
    }

    @Override
    public TaskInspectMode getMode() {
        return TaskInspectMode.CUSTOM;
    }

    @Override
    public void refresh(TaskInspectConfig config) throws InterruptedException {
        if (context.isStopping()) return;
        if (getMode() != config.getMode()) {
            throw TaskInspectRuntimeException.illegalMode(context.getTaskId(), config.getMode());
        }

        this.config = config.getCustom();
        context.setCdcRecover(getConfig().getRecover().getEnable());
        context.setCdcRecheck(getConfig().getDiff().getEnable());
        cdcMode.refresh(getConfig().getCdc());
    }

    @Override
    public void acceptCdcEvent(long cdcReadTs, long cdcOpTs, String tableName, LinkedHashMap<String, Object> keys) {
        if (context.isStopping()) return;
        cdcMode.acceptCdcEvent(cdcReadTs, cdcOpTs, tableName, keys);
    }

    @Override
    public boolean stop() {
        AtomicBoolean isStop = new AtomicBoolean(true);
        isStop.compareAndSet(true, cdcMode.stop());
        isStop.compareAndSet(true, resultReporterJob.stop());
        return isStop.get();
    }
}
