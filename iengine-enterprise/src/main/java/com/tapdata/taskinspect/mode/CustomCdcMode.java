package com.tapdata.taskinspect.mode;

import com.tapdata.taskinspect.ICheckQueue;
import com.tapdata.taskinspect.TaskInspectContext;
import com.tapdata.taskinspect.job.CustomCdcSampleJob;
import com.tapdata.taskinspect.job.ICdcJob;
import com.tapdata.tm.taskinspect.TaskInspectUtils;
import com.tapdata.tm.taskinspect.config.CustomCdc;
import com.tapdata.tm.taskinspect.config.CustomCdcSample;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.BooleanSupplier;

/**
 * 自定义增量模式校验
 *
 * <AUTHOR> href="mailto:<EMAIL>">Ha<PERSON></a>
 * @version v1.0 2025/3/26 11:22 Create
 */
public class CustomCdcMode implements ICustomCdcMode {
    private final TaskInspectContext context;
    private final ICheckQueue checkQueue;
    private ICdcJob job;

    public CustomCdcMode(TaskInspectContext context, ICheckQueue checkQueue) {
        this.context = context;
        this.checkQueue = checkQueue;
    }

    /**
     * 刷新配置
     * <pre>
     * 此方法用于根据新的配置更新当前对象的状态
     * 如果配置启用且类型为SAMPLE，则更新样本作业配置
     * 如果配置未启用或类型不支持，则停止当前作业
     * </pre>
     *
     * @param config 新的配置对象，包含作业的配置信息
     * @throws InterruptedException 如果在等待某些资源或条件时线程被中断
     */
    @Override
    public synchronized void refresh(CustomCdc config) throws InterruptedException {
        // 检查配置是否启用
        if (config.getEnable()) {
            // 按作业类型初始化或更新配置
            switch (config.getType()) {
                case SAMPLE:
                    // 更新样本作业配置
                    refreshJobConfig(config.getSample());
                    return;
                case CLOSE:
                default:
                    // 默认行为或关闭作业配置，不做任何操作
                    break;
            }
        }

        // 未支持的作业或配置未启用，直接停止
        doStop();
    }

    @Override
    public void acceptCdcEvent(long cdcReadTs, long cdcOpTs, String tableName, LinkedHashMap<String, Object> keys) {
        Optional.ofNullable(job).ifPresent(job -> job.acceptCdcEvent(cdcReadTs, cdcOpTs, tableName, keys));
    }

    @Override
    public boolean stop() {
        return Optional.ofNullable(job).map(ICdcJob::stop).orElse(true);
    }

    /**
     * 停止执行中的任务，并将job设置为null
     * <p>
     * 本方法尝试停止当前正在执行的任务如果任务存在的话
     *
     * @throws InterruptedException 如果在等待任务停止时线程被中断
     */
    protected void doStop() throws InterruptedException {
        List<BooleanSupplier> stopList = new ArrayList<>();
        Optional.ofNullable(this.job).ifPresent(o -> stopList.add(o::stop));
//        Optional.ofNullable(this.recheckJob).ifPresent(o -> stopList.add(o::stop));

        // 使用 TaskInspectUtils 工具类的 stop 方法来停止任务，最大等待时间为 10 秒
        TaskInspectUtils.stop(10 * 1000L, stopList.toArray(new BooleanSupplier[0]));

//        recheckQueue.clear();

        // 任务停止后，将 job 引用设置为 null，表示任务已完成或已被停止
        this.job = null;
//        this.recheckJob = null;
    }

    /**
     * 刷新作业配置
     * <p>
     * 当配置更新失败时，将停止当前作业并创建一个新的作业实例
     *
     * @param config 新的作业配置
     * @throws InterruptedException 如果停止作业时被中断
     */
    protected void refreshJobConfig(CustomCdcSample config) throws InterruptedException {
        // 同实例，更新配置
        if (job instanceof CustomCdcSampleJob) {
            // 配置更新失败时，重启
            CustomCdcSampleJob customCdcSampleJob = (CustomCdcSampleJob) job;
            if (customCdcSampleJob.refresh(config)) {
                return;
            }
        }

        // 作业已存在，先停止
        doStop();

        // 创建新作业
        this.job = new CustomCdcSampleJob(config, context, checkQueue);
        TaskInspectUtils.submit(job);
//        this.recheckJob = new CdcRecheckJob(context, checkQueue, recheckQueue);
//        TaskInspectUtils.submit(recheckJob);
    }

    protected ICdcJob getJob() {
        return job;
    }
}
