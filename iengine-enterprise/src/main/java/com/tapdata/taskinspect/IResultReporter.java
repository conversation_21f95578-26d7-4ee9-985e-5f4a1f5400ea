package com.tapdata.taskinspect;

import com.tapdata.tm.taskinspect.vo.ResultsReportVo;

/**
 * 校验结果上报接口
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/5/14 00:01 Create
 */
public interface IResultReporter {
    boolean reportDiff(String op, String userId, String user, String msg, ResultsReportVo vo);

    boolean reportRecovered(String rowId);

    boolean reportOperation(String rowId, String op, String userId, String user, String msg);
}
