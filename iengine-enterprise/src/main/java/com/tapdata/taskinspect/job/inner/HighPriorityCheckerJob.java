package com.tapdata.taskinspect.job.inner;

import com.alibaba.fastjson.JSONObject;
import com.tapdata.taskinspect.ICheckQueue;
import com.tapdata.taskinspect.TaskInspectContext;
import com.tapdata.taskinspect.job.AbsInnerJob;
import com.tapdata.taskinspect.vo.CheckItem;
import com.tapdata.tm.taskinspect.exception.TaskInspectNotFoundTableFieldMappingException;
import com.tapdata.tm.taskinspect.exception.TaskInspectNotFoundTableMappingException;
import com.tapdata.tm.taskinspect.exception.TaskInspectNotSupportedStepException;
import com.tapdata.tm.taskinspect.vo.ResultOperationsVo;
import com.tapdata.tm.taskinspect.vo.ResultsReportVo;
import com.tapdata.tm.utils.TimeMaxAccepter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.LinkedHashMap;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 高优检查项-工作线程
 *
 * <AUTHOR> href="mailto:<EMAIL>">Harsen</a>
 * @version v1.0 2025/5/7 16:28 Create
 */
public class HighPriorityCheckerJob extends AbsInnerJob {
    private static final Logger log = LogManager.getLogger(HighPriorityCheckerJob.class);

    private final TimeMaxAccepter compareErrorLogRate; // 控制对比异常输出频率
    private final ICheckQueue checkQueue;

    public HighPriorityCheckerJob(TaskInspectContext context, ICheckQueue checkQueue) {
        super("high-priority-checker", context);
        this.compareErrorLogRate = new TimeMaxAccepter(context.getErrorRate(), 1);
        this.checkQueue = checkQueue;
    }

    @Override
    protected void doRun(String jobId) throws InterruptedException {
        CheckItem checkItem;
        while (isRunning()) {
            checkItem = checkQueue.pollHighPriority();

            // 防止空转
            if (null == checkItem) {
                TimeUnit.MILLISECONDS.sleep(context.getLoopMilliseconds());
                continue;
            }

            try {
                // 对比数据并上报结果
                doCheck(context, checkQueue, checkItem);
            } catch (TaskInspectNotFoundTableMappingException e) {
                if (checkQueue.ignoreTable(e.getTableName())) {
                    log.warn(e.getMessage());
                }
            } catch (TaskInspectNotFoundTableFieldMappingException e) {
                if (checkQueue.ignoreTable(e.getTableName())) {
                    log.warn(e.getMessage());
                }
            } catch (Exception e) {
                if (compareErrorLogRate.check()) {
                    log.error("compare failed: {}", e.getMessage(), e);
                }
            }
        }
    }

    public static void doCheck(TaskInspectContext context, ICheckQueue checkQueue, CheckItem item) throws TaskInspectNotFoundTableMappingException
        , TaskInspectNotFoundTableFieldMappingException, TaskInspectNotSupportedStepException {
        // 对比数据并上报结果
        String rowId = item.getRowId();
        String tableName = item.getTableName();
        LinkedHashMap<String, Object> keys = item.getKeys();

        ResultsReportVo resultsReportVo = context.getChecker().compare(rowId, tableName, keys);
        if (null == resultsReportVo) {
            log.debug(()-> String.format("recovered-%s-%s '%s':%s"
                , item.getStep()
                , item.getRowId()
                , item.getTableName()
                , JSONObject.toJSONString(item)
            ));

            checkQueue.recovered(rowId);
            context.getResultReporter().reportRecovered(rowId);
            return;
        }

        log.debug(()-> String.format("check-%s-%s '%s':%s"
            , item.getStep()
            , item.getRowId()
            , item.getTableName()
            , JSONObject.toJSONString(item)
        ));

        checkQueue.difference(rowId);
        doCheckResult(context, checkQueue, item.getStep(), resultsReportVo);
    }

    public static void doCheckResult(TaskInspectContext context, ICheckQueue checkQueue, int step, ResultsReportVo vo) throws TaskInspectNotFoundTableMappingException, TaskInspectNotSupportedStepException {
        String rowId = vo.getRowId();
        String tableName = vo.getSourceTable();
        LinkedHashMap<String, Object> keys = vo.getKeys();

        switch (step) {
            case CheckItem.STEP_CHECK -> {
                context.getResultReporter().reportDiff(ResultOperationsVo.OP_CDC_CHECK, context.getUserId(), context.getUserName(), null, vo);

                if (context.isCdcRecheck()) {
                    checkQueue.offerCdcRecheck(rowId);
                    return;
                }

                if (context.isCdcRecover()) {
                    context.getChecker().sendRecover(rowId, tableName, keys);
                }
            }
            case CheckItem.STEP_RECHECK -> {
                context.getResultReporter().reportDiff(ResultOperationsVo.OP_AUTO_RECHECK, context.getUserId(), context.getUserName(), null, vo);

                if (context.isCdcRecover()) {
                    context.getChecker().sendRecover(rowId, tableName, keys);
                }
            }
            case CheckItem.STEP_RECOVER -> {
                context.getResultReporter().reportDiff(ResultOperationsVo.OP_AUTO_RECOVER_CHECK, context.getUserId(), context.getUserName(), null, vo);
            }
            default -> throw new TaskInspectNotSupportedStepException(context.getTaskId(), step);
        }
    }
}
