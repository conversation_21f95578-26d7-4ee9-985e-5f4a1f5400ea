package com.tapdata.taskinspect.job;

import com.tapdata.taskinspect.IOperator;
import com.tapdata.taskinspect.TaskInspectContext;
import com.tapdata.taskinspect.TaskInspectOperator;
import com.tapdata.tm.commons.base.dto.BaseDto;
import com.tapdata.tm.taskinspect.TaskInspectUtils;
import com.tapdata.tm.taskinspect.config.IConfig;
import com.tapdata.tm.taskinspect.cons.JobStatusEnum;
import com.tapdata.tm.taskinspect.cons.JobTypeEnum;
import com.tapdata.tm.taskinspect.dto.TaskInspectHistoriesDto;
import com.tapdata.tm.taskinspect.job.IJob;
import com.tapdata.tm.taskinspect.vo.JobReportVo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.types.ObjectId;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 任务基类
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/3/19 15:56 Create
 */
public abstract class AbsTaskInspectJob<T extends IConfig<T>> implements IJob {
    private static final Logger log = LogManager.getLogger(AbsTaskInspectJob.class);

    protected final JobTypeEnum type;
    protected final T config;
    protected final TaskInspectContext context;
    protected final AtomicReference<JobStatusEnum> status = new AtomicReference<>(JobStatusEnum.NONE);
    protected final Map<String, Object> attrs = new ConcurrentHashMap<>();

    protected AbsTaskInspectJob(JobTypeEnum type, T config, TaskInspectContext context) {
        this.type = type;
        this.config = config;
        this.context = context;
    }

    @Override
    public void run() {
        if (!statusNone2Running()) {
            return;
        }

        String jobId = startJob();
        if (jobId == null) {
            return;
        }

        try {
            String threadName = String.format("%s-%s-%s", context.getTaskId(), TaskInspectUtils.MODULE_NAME, jobId);
            Thread.currentThread().setName(threadName);
            doRun(jobId);
            updateJobStatus(jobId, JobStatusEnum.STOPPED, null);
        } catch (InterruptedException e) {
            log.info("stop with interrupted");
            Thread.currentThread().interrupt();
        } catch (Throwable e) {
            log.error("run failed: {}", e.getMessage(), e);
            updateJobStatus(jobId, JobStatusEnum.START_ERROR, e.getMessage());
        } finally {
            log.info("exit");
        }
    }

    @Override
    public boolean stop() {
        status.compareAndSet(JobStatusEnum.RUNNING, JobStatusEnum.STOPPING);
        return status.get().isStopped();
    }

    /**
     * 检查作业是否正在运行
     * <p>
     * 此方法通过检查作业状态和上下文来判断作业是否正在运行首先，它检查当前作业状态是否为“运行中”（JobStatus.RUNNING），
     * 然后检查上下文是否已经触发了停止作业的指令（context.isStopping()）只有当作业状态为“运行中”且上下文未触发停止指令时，
     * 此方法才返回true，表示作业正在运行
     *
     * @return boolean 表示作业是否正在运行true表示正在运行，false表示未运行或即将停止
     */
    protected boolean isRunning() {
        if (JobStatusEnum.RUNNING == status.get()) {
            return !(context.isStopping() && context.isForceStop());
        }
        return false;
    }

    protected abstract void doRun(String jobId) throws InterruptedException;

    /**
     * 尝试将作业状态设置为运行中
     *
     * @return boolean 表示是否成功设置状态
     */
    protected boolean statusNone2Running() {
        if (status.compareAndSet(JobStatusEnum.NONE, JobStatusEnum.RUNNING)) {
            return true;
        }
        log.warn("can't start, status: {}", status.get());
        return false;
    }

    /**
     * 启动作业并获取作业ID
     *
     * @return String 作业ID
     */
    protected String startJob() {
        try {
            IOperator operator = context.getOperator();
            String taskId = context.getTaskId();
            TaskInspectHistoriesDto dto = operator.jobStart(taskId, type, config, null);
            return Optional.ofNullable(dto)
                .map(BaseDto::getId)
                .map(ObjectId::toHexString)
                .orElse(null);
        } catch (Exception e) {
            log.error("start failed: {}", e.getMessage(), e);
            status.set(JobStatusEnum.START_ERROR);
            return null;
        }
    }

    /**
     * 更新作业状态
     *
     * @param jobId 作业ID
     * @param jobStatusEnum 作业状态
     */
    protected boolean updateJobStatus(String jobId, JobStatusEnum jobStatusEnum, String msg) {
        try {
            status.set(jobStatusEnum);
            JobReportVo reportVo = JobReportVo.create(jobStatusEnum).attrs(attrs).message(msg);

            IOperator operator = context.getOperator();
            return operator.postJobStatus(jobId, reportVo);
        } catch (Exception e) {
            log.error("update job status failed: {}", e.getMessage(), e);
            return false;
        }
    }

}
