package com.tapdata.taskinspect.job;

import com.alibaba.fastjson.JSON;
import com.tapdata.taskinspect.ICheckQueue;
import com.tapdata.taskinspect.IOperator;
import com.tapdata.taskinspect.TaskInspectContext;
import com.tapdata.taskinspect.job.inner.HighPriorityCheckerJob;
import com.tapdata.taskinspect.vo.CheckItem;
import com.tapdata.tm.taskinspect.TaskInspectUtils;
import com.tapdata.tm.taskinspect.config.CustomCdcSample;
import com.tapdata.tm.taskinspect.cons.JobTypeEnum;
import com.tapdata.tm.taskinspect.exception.TaskInspectNotFoundTableFieldMappingException;
import com.tapdata.tm.taskinspect.exception.TaskInspectNotFoundTableMappingException;
import com.tapdata.tm.taskinspect.vo.TaskInspectMetrics;
import com.tapdata.tm.taskinspect.vo.JobReportVo;
import com.tapdata.tm.utils.TimeMaxAccepter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.LinkedHashMap;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 自定义校验-增量抽样
 *
 * <AUTHOR> href="mailto:<EMAIL>">Harsen</a>
 * @version v1.0 2025/3/25 17:48 Create
 */
public class CustomCdcSampleJob extends AbsTaskInspectJob<CustomCdcSample> implements ICdcJob {
    private static final Logger log = LogManager.getLogger(CustomCdcSampleJob.class);

    private final TimeMaxAccepter compareErrorLogRate; // 控制对比异常输出频率
    protected final ICheckQueue checkQueue;
    private long nextReportTimes;


    public CustomCdcSampleJob(CustomCdcSample config, TaskInspectContext context, ICheckQueue checkQueue) {
        super(JobTypeEnum.CDC, config, context);
        this.compareErrorLogRate = new TimeMaxAccepter(context.getErrorRate(), 1);
        this.checkQueue = checkQueue;
        this.checkQueue.setCdcSampleInterval(this.config.getInterval());
        this.checkQueue.setCdcSampleLimit(this.config.getLimit());
    }

    @Override
    protected void doRun(String jobId) throws InterruptedException {
        Thread.currentThread().setName(String.format("%s-cdc", Thread.currentThread().getName()));
        log.info("start");

        while (isRunning()) {
            CheckItem checkItem = checkQueue.poll();
            if (null != checkItem) {
                try {
                    // 对比数据并上报结果
                    HighPriorityCheckerJob.doCheck(context, checkQueue, checkItem);
                } catch (TaskInspectNotFoundTableMappingException e) {
                    if (checkQueue.ignoreTable(e.getTableName())) {
                        log.warn(e.getMessage());
                    }
                } catch (TaskInspectNotFoundTableFieldMappingException e) {
                    if (checkQueue.ignoreTable(e.getTableName())) {
                        log.warn(e.getMessage());
                    }
                } catch (Exception e) {
                    if (compareErrorLogRate.check()) {
                        log.error("compare failed: {}", e.getMessage(), e);
                    }
                }
                continue;
            }

            reportStatus(jobId);
            TimeUnit.MILLISECONDS.sleep(context.getLoopMilliseconds());
        }
    }

    @Override
    public void acceptCdcEvent(long cdcReadTs, long cdcOpTs, String tableName, LinkedHashMap<String, Object> keys) {
        log.debug(() -> String.format("%s accept cdc event: %s: %s"
            , TaskInspectUtils.MODULE_NAME
            , tableName
            , JSON.toJSONString(keys)
        ));

        checkQueue.offerCdc(cdcReadTs, cdcOpTs, tableName, keys);
    }

    /**
     * 更新配置信息
     * <p>
     * 当新配置的容量与当前配置容量相同时，更新配置的限制和时间间隔，并相应更新队列的限制和时间间隔
     * 如果作业ID不为空，则向服务报告作业状态
     *
     * @param config 新的配置信息
     * @return 如果配置更新成功，则返回true；否则返回false
     */
    public boolean refresh(CustomCdcSample config) throws InterruptedException {
        // 新旧配置的容量不同，则无法更新，需要重启
        if (!Objects.equals(this.config.getCapacity(), config.getCapacity())) {
            return false;
        }

        // 更新配置的限制和时间间隔
        this.config.fill(config);
        // 更新队列的限制和时间间隔
        checkQueue.setCdcSampleInterval(this.config.getInterval());
        checkQueue.setCdcSampleLimit(this.config.getLimit());
        return true;
    }

    protected void reportStatus(String jobId) {
        if (nextReportTimes < System.currentTimeMillis()) {
            try {
                TaskInspectMetrics metrics = checkQueue.nextMetrics();
                metrics.toMap(this.attrs);
                JobReportVo reportData = JobReportVo.create(status.get())
                    .config(config)
                    .attrs(attrs);

                IOperator operator = context.getOperator();
                boolean reportSuccess = operator.postJobStatus(jobId, reportData);
                if (reportSuccess) {
                    nextReportTimes = System.currentTimeMillis() + context.getReportInterval();
                }
            } catch (Exception e) {
                log.warn("report status failed: {}", e.getMessage(), e);
            }
        }
    }
}
