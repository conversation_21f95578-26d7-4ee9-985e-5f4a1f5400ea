package com.tapdata.taskinspect.job;

import com.tapdata.tm.taskinspect.job.IJob;

import java.util.LinkedHashMap;

/**
 * 增量校验作业
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/3/26 11:40 Create
 */
public interface ICdcJob extends IJob {

    /**
     * @param cdcReadTs 增量读取时间
     * @param cdcOpTs   增量变更时间
     * @param tableName 表名
     * @param keys      行主键
     */
    void acceptCdcEvent(long cdcReadTs, long cdcOpTs, String tableName, LinkedHashMap<String, Object> keys);
}
