package com.tapdata.taskinspect.job.inner;

import com.alibaba.fastjson.JSONObject;
import com.tapdata.taskinspect.IOperator;
import com.tapdata.taskinspect.IResultReporter;
import com.tapdata.taskinspect.TaskInspectContext;
import com.tapdata.taskinspect.job.AbsInnerJob;
import com.tapdata.tm.taskinspect.cons.DiffTypeEnum;
import com.tapdata.tm.taskinspect.dto.TaskInspectResultsDto;
import com.tapdata.tm.taskinspect.vo.MapCreator;
import com.tapdata.tm.taskinspect.vo.ResultOperationsVo;
import com.tapdata.tm.taskinspect.vo.ResultsReportVo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 差异记录上报
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/5/8 14:23 Create
 */
public class ResultReporterJob extends AbsInnerJob implements IResultReporter {
    private static final Logger log = LogManager.getLogger(ResultReporterJob.class);

    private final ConcurrentSkipListMap<String, MapCreator<String, Object>> reportData = new ConcurrentSkipListMap<>();

    public ResultReporterJob(TaskInspectContext context) {
        super("result-reporter", context);
    }

    @Override
    protected void doRun(String jobId) throws InterruptedException {
        while (isRunning()) {
            MapCreator<String, Object> reportData = poll();
            if (null == reportData) {
                TimeUnit.MILLISECONDS.sleep(200);
                continue;
            }

            try {
                if (DiffTypeEnum.RECOVERED.equals(reportData.get(TaskInspectResultsDto.FIELD_DIFF_TYPE))) {
                    log.debug("recover: {}", () -> JSONObject.toJSONString(reportData));

                    IOperator operator = context.getOperator();
                    String taskId = context.getTaskId();
                    operator.recoverResult(taskId, reportData);
                } else {
                    log.debug("report: {}", () -> JSONObject.toJSONString(reportData));

                    IOperator operator = context.getOperator();
                    String taskId = context.getTaskId();
                    operator.reportResult(taskId, reportData);
                }
            } catch (Exception e) {
                log.warn("report results failed: {}", e.getMessage());
            }
        }
    }

    protected synchronized MapCreator<String, Object> poll() {
        Map.Entry<String, MapCreator<String, Object>> entry = reportData.pollLastEntry();
        if (null == entry) {
            return null;
        }

        return entry.getValue();
    }

    public synchronized boolean reportDiff(String op, String userId, String user, String msg, ResultsReportVo vo) {
        String rowId = vo.getRowId();

        MapCreator<String, Object> reportMap = getReportData(rowId, o -> o
            .add(TaskInspectResultsDto.FIELD_KEYS, vo.getKeys())
            .add(TaskInspectResultsDto.FIELD_SOURCE_TABLE, vo.getSourceTable())
            .add(TaskInspectResultsDto.FIELD_SOURCE_FIELDS, vo.getSourceFields())
            .add(TaskInspectResultsDto.FIELD_TARGET_TABLE, vo.getTargetTable())
            .add(TaskInspectResultsDto.FIELD_TARGET_FIELDS, vo.getTargetFields())
        );

        // 设置需要更新的属性
        reportMap.add(TaskInspectResultsDto.FIELD_DIFF_TYPE, vo.getDiffType());
        reportMap.add(TaskInspectResultsDto.FIELD_DIFF_FIELDS, vo.getDiffFields());
        reportMap.add(TaskInspectResultsDto.FIELD_SOURCE, vo.getSource());
        reportMap.add(TaskInspectResultsDto.FIELD_TARGET, vo.getTarget());

        List<ResultOperationsVo> operations = getOperations(reportMap);
        operations.add(ResultOperationsVo.create(op, userId, user, msg));
        return true;
    }

    public synchronized boolean reportRecovered(String rowId) {
        MapCreator<String, Object> reportMap = getReportData(rowId, o -> o);

        // 上报队列满直接返回
        if (null == reportMap) {
            return false;
        }

        reportMap.add(TaskInspectResultsDto.FIELD_DIFF_TYPE, DiffTypeEnum.RECOVERED);
        return true;
    }

    @Override
    public synchronized boolean reportOperation(String rowId, String op, String userId, String user, String msg) {
        MapCreator<String, Object> reportMap = getReportData(rowId, o -> o);

        // 上报队列满直接返回
        if (null == reportMap) {
            return false;
        }

        List<ResultOperationsVo> operations = getOperations(reportMap);
        operations.add(ResultOperationsVo.create(op, userId, user, msg));
        return true;
    }

    protected List<ResultOperationsVo> getOperations(MapCreator<String, Object> map) {
        Object o = map.get(TaskInspectResultsDto.FIELD_OPERATIONS);
        return (List<ResultOperationsVo>) o;
    }

    protected synchronized MapCreator<String, Object> getReportData(String rowId, Function<MapCreator<String, Object>, MapCreator<String, Object>> initFn) {
        // 上报列队按 rowId 去重，未上报的数据多次更新只上报一次
        MapCreator<String, Object> data = reportData.get(rowId);
        if (null == data) {
            // 回调初始化方法
            data = initFn.apply(MapCreator.<String, Object>create(TaskInspectResultsDto.FIELD_ROW_ID, rowId)
                .add(TaskInspectResultsDto.FIELD_TASK_ID, context.getTaskId())
                .add(TaskInspectResultsDto.FIELD_OPERATIONS, new ArrayList<>())
            );

            reportData.put(rowId, data);
        }
        return data;
    }

}
