package com.tapdata.taskinspect.job;

import com.tapdata.taskinspect.TaskInspectContext;
import com.tapdata.tm.taskinspect.TaskInspectUtils;
import com.tapdata.tm.taskinspect.cons.JobStatusEnum;
import com.tapdata.tm.taskinspect.job.IJob;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.atomic.AtomicReference;

/**
 *
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/5/8 06:57 Create
 */
public abstract class AbsInnerJob implements IJob {
    private static final Logger log = LogManager.getLogger(AbsInnerJob.class);

    protected final String jobId;
    protected final TaskInspectContext context;
    protected final AtomicReference<JobStatusEnum> status = new AtomicReference<>(JobStatusEnum.NONE);

    public AbsInnerJob(String jobId, TaskInspectContext context) {
        this.jobId = jobId;
        this.context = context;
    }

    @Override
    public boolean stop() {
        status.compareAndSet(JobStatusEnum.RUNNING, JobStatusEnum.STOPPING);
        return status.get().isStopped();
    }

    @Override
    public void run() {
        if (!statusNone2Running()) {
            return;
        }

        try {
            String threadName = String.format("%s-%s-%s", context.getTaskId(), TaskInspectUtils.MODULE_NAME, jobId);
            Thread.currentThread().setName(threadName);
            log.info("start");
            doRun(jobId);
            status.set(JobStatusEnum.STOPPED);
        } catch (InterruptedException e) {
            log.info("stop with interrupted");
            Thread.currentThread().interrupt();
        } catch (Throwable e) {
            log.error("run failed: {}", e.getMessage(), e);
            status.set(JobStatusEnum.START_ERROR);
        } finally {
            log.info("exit");
        }
    }

    protected abstract void doRun(String jobId) throws InterruptedException;

    protected boolean isRunning() {
        if (JobStatusEnum.RUNNING == status.get()) {
            return !(context.isStopping() && context.isForceStop());
        }
        return false;
    }

    /**
     * 尝试将作业状态设置为运行中
     *
     * @return boolean 表示是否成功设置状态
     */
    protected boolean statusNone2Running() {
        if (status.compareAndSet(JobStatusEnum.NONE, JobStatusEnum.RUNNING)) {
            return true;
        }
        log.warn("can't start, status: {}", status.get());
        return false;
    }
}
