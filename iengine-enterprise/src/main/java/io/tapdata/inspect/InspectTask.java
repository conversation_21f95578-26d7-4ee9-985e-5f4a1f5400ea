package io.tapdata.inspect;

import com.tapdata.constant.*;
import com.tapdata.entity.Connections;
import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.inspect.*;
import com.tapdata.exception.NonsupportMethodDifferenceInspectException;
import com.tapdata.exception.NotfoundLastResultInspectException;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.tm.commons.task.dto.ParentTaskDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.utils.InstanceFactory;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.flow.engine.V2.entity.PdkStateMap;
import io.tapdata.flow.engine.V2.log.LogFactory;
import io.tapdata.flow.engine.V2.util.PdkUtil;
import io.tapdata.inspect.cdc.InspectCdcUtils;
import io.tapdata.pdk.apis.functions.PDKMethod;
import io.tapdata.pdk.core.api.ConnectorNode;
import io.tapdata.pdk.core.api.PDKIntegration;
import io.tapdata.pdk.core.monitor.PDKInvocationMonitor;
import io.tapdata.schema.ConnectionTapTableMap;
import io.tapdata.schema.PdkTableMap;
import io.tapdata.schema.TapTableUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/9/10 7:19 上午
 * @description
 */
public abstract class InspectTask implements Runnable {

	private final Inspect inspect;
	private final InspectService inspectService;
	private ClientMongoOperator clientMongoOperator;
	private Logger logger = LogManager.getLogger(InspectTask.class);
	private static final String INSPECT_THREAD_NAME_PREFIX = "INSPECT-RUNNER-";
	private static final String PROCESS_ID = ConfigurationCenter.processId;
	private final AtomicBoolean isDoStop = new AtomicBoolean(false);
	private final List<Future<?>> jobFutures = new ArrayList<>();

	private long syncTaskDelay;

	private String syncTaskStatus;

	private String syncTaskType;

	public InspectTask(InspectService inspectService, Inspect inspect, ClientMongoOperator clientMongoOperator) {
		this.inspectService = inspectService;
		this.inspect = inspect;
		this.clientMongoOperator = clientMongoOperator;
	}

	public String getInspectId() {
		return inspect.getId();
	}

	public void doStop() {
		synchronized (jobFutures) {
			isDoStop.set(true);
			for (Future<?> future : jobFutures) {
				try {
					logger.info("stop begin");
					future.cancel(true);
					logger.info("stop completed");
				} catch (Exception e) {
					logger.warn("Stop inspect failed: {}", e.getMessage(), e);
				}
			}
		}
	}

	@Override
	public void run() {
		String name = INSPECT_THREAD_NAME_PREFIX + inspect.getName() + "(" + inspect.getId() + ")";
		Thread.currentThread().setName(name);

		initTaskDelayAndStatus();
		InspectResult _inspectResult = null;
		if (StringUtils.isNotEmpty(inspect.getInspectResultId())) {
			_inspectResult = inspectService.getInspectResultById(inspect.getInspectResultId());

			if (_inspectResult == null) {
				logger.info("Not found inspect result on restart inspect task: {}({}, {})",
						inspect.getName(), inspect.getId(), inspect.getInspectResultId());

				inspectService.updateStatus(inspect.getId(), InspectStatus.ERROR, "Not found inspect result by id: "
						+ inspect.getInspectResultId());

				return;
			}
			_inspectResult.setPartStats(true);
		}
		InspectResult inspectResult = _inspectResult != null ? _inspectResult : new InspectResult();
		Map<String, ConnectorNode> connectorNodeMap = null;
		try {
			logger.info("Start execute data inspect: {}({})", inspect.getName(), inspect.getId());

			inspectService.updateStatus(inspect.getId(), InspectStatus.RUNNING, null);

			List<Connections> connections = inspectService.getInspectConnectionsById(inspect);
			Map<String, Connections> connectionsMap = new HashMap<>();
			connections.forEach(connection -> connectionsMap.put(connection.getId(), connection));
			connectorNodeMap = initConnectorNodeMap(connectionsMap);

			// Parallel check of multiple tables
			ThreadPoolExecutor executorService = new ThreadPoolExecutor(0, 10, 0,
					TimeUnit.SECONDS, new LinkedBlockingQueue<>());

			inspectResult.setInspect_id(inspect.getId());
			inspectResult.setInspectVersion(inspect.getVersion());
			inspectResult.setStats(Collections.synchronizedList(new ArrayList<>()));
			inspectResult.setThreads(5);
			inspectResult.setStatus("running");
			inspectResult.setProgress(0);
			inspectResult.setStart(new Date());
			inspectResult.setAgentId(PROCESS_ID);
			inspectResult.setCustomId(inspect.getCustomId());
			inspectResult.setUser_id(inspect.getUser_id());

			// 如果是差异校验，firstCheckId 有值
			String firstCheckId = inspect.getByFirstCheckId();
			if (null != firstCheckId && !firstCheckId.isEmpty()) {
				initDifferenceInspect(inspectResult, firstCheckId);
			} else {
				inspectResult.setInspect(inspect);
				inspectResult.getInspect().getTasks().forEach(task -> {
					InspectResultStats stats = new InspectResultStats();

					stats.setStart(new Date());
					stats.setStatus(InspectStatus.RUNNING.getCode());
					stats.setProgress(0);
					stats.setTaskId(task.getTaskId());
					stats.setSource(task.getSource());
					stats.setTarget(task.getTarget());

					inspectResult.getStats().add(stats);
				});
				inspectService.upsertInspectResult(inspectResult, false);
				InspectCdcUtils.setCdcRunProfilesByLastResult(inspectService, inspectResult); // 填充增量运行配置

				// 初次校验，取当前结果编号
				inspectResult.setFirstCheckId(inspectResult.getId());
				inspectService.upsertInspectResult(inspectResult, false);
			}

			List<InspectDetail> inspectDetailQueue = new ArrayList<>();

			// async report inspect stats
			final Object lock = new Object();
			Thread reportThread = new Thread(() -> {
				while (!Thread.currentThread().isInterrupted()) {
					inspectService.upsertInspectResult(inspectResult);
					List<InspectDetail> temp;
					synchronized (inspectDetailQueue) {
						temp = new ArrayList<>(inspectDetailQueue);
						inspectDetailQueue.clear();
					}
					inspectService.insertInspectDetails(temp);

					synchronized (lock) {
						try {
							lock.wait();
						} catch (InterruptedException e) {
							logger.info("Interrupted data inspect async report thread");
							break;
						}
					}
				}
			});
			reportThread.setName(name + ".reportThread");
			reportThread.start();

			ScheduledExecutorService heartBeatThreads = new ScheduledThreadPoolExecutor(1);
			heartBeatThreads.scheduleAtFixedRate(() -> inspectService.inspectHeartBeat(inspect.getId()), 5, 5, TimeUnit.SECONDS);

			// Create a verification task for each table and submit it for execution
			Map<String, ConnectorNode> finalConnectorNodeMap = connectorNodeMap;
			inspectResult.getInspect().getTasks().forEach(task -> {
				task.setLimit(inspectResult.getInspect().getLimit());
				Connections source = connectionsMap.get(task.getSource().getConnectionId()),
						target = connectionsMap.get(task.getTarget().getConnectionId());
				if (source == null) {
					logger.error("Not found source Connections by connectionId " + task.getSource().getConnectionId());
					return;
				}
				if (target == null) {
					logger.error("Not found target Connections by connectionId " + task.getTarget().getConnectionId());
					return;
				}
				ConnectorNode sourceNode = finalConnectorNodeMap.get(task.getSource().getNodeId()),
						targetNode = finalConnectorNodeMap.get(task.getTarget().getNodeId());

				AtomicLong atomicLong = new AtomicLong(System.currentTimeMillis());
				// submit verification task
				InspectTaskContext inspectTaskContext = new InspectTaskContext(
						name,
                        inspect.getFlowId(),
						task,
						source,
						target,
						inspectResult.getParentId(),
						inspect.getInspectDifferenceMode(),
                        inspect.getEnableRecovery(),
						(inspectTask, inspectResultStats, inspectDetails) -> {
							Optional<InspectResultStats> optional = inspectResult.getStats().stream()
									.filter((stats) -> stats.getTaskId().equals(inspectResultStats.getTaskId()))
									.findFirst();
							if (optional.isPresent()) {
								InspectResultStats existsStats = optional.get();
								if (existsStats.getStatus().equals(InspectStatus.ERROR.getCode())) {
									existsStats.setResult("");
								}
								if (existsStats != inspectResultStats) {
									inspectResultStats.setFirstSourceTotal(existsStats.getFirstSourceTotal());
									inspectResultStats.setFirstTargetTotal(existsStats.getFirstTargetTotal());
									inspectResult.getStats().remove(existsStats);
									inspectResult.getStats().add(inspectResultStats);
								}
							} else {
								inspectResult.getStats().add(inspectResultStats);
							}

							composeInspectResult(inspectResult);
							inspectResult.setThreads(executorService.getActiveCount());
							inspectResult.setRepeat(task.isRepeat());

							// async report stats and progress
							long current = System.currentTimeMillis();
							boolean notify = false;
							if ((current - atomicLong.get())> 5000 ) {
								atomicLong.set(current);
								notify = true;
							}
							if (inspectDetails != null && inspectDetails.size() > 0) {
								inspectDetails.forEach(detail -> {
									detail.setInspect_id(inspect.getId());
									detail.setTaskId(inspectTask.getTaskId());
									detail.setInspectResultId(inspectResult.getId());
									detail.setUser_id(inspect.getUser_id());
									detail.setCustomId(inspect.getCustomId());
								});
								synchronized (inspectDetailQueue) {
									inspectDetailQueue.addAll(inspectDetails);
								}
								notify = true;
							}
							if (notify) {
								synchronized (lock) {
									lock.notify();
								}
							}
						}, sourceNode, targetNode, clientMongoOperator,inspectService
				);
				synchronized (jobFutures) {
					jobFutures.add(executorService.submit(createTableInspectJob(inspectTaskContext)));
				}
				logger.info("Inspect job[{}] started, source: {}, target: {}", inspect.getName(), inspectTaskContext.getTask().getSource(), inspectTaskContext.getTask().getTarget());
			});

			boolean hasError = false;
			String errorMessage = "";
			for (Future<?> future : jobFutures) {
				try {
					future.get();
				} catch (InterruptedException | ExecutionException e) {
					logger.error("Execute data inspect failed", e);
					hasError = true;
					errorMessage += e.getMessage() + ";";
				}
			}

			reportThread.interrupt();
			ExecutorUtil.shutdown(executorService, 10L, TimeUnit.SECONDS);
			ExecutorUtil.shutdown(heartBeatThreads, 10L, TimeUnit.SECONDS);
			inspectResult.setEnd(new Date());
            if(!inspectResult.isRepeat()) {
				inspectService.upsertInspectResult(inspectResult);
			}
			synchronized (inspectDetailQueue) {

				inspectService.insertInspectDetails(inspectDetailQueue);
				inspectDetailQueue.clear();
			}

			if (InspectStatus.ERROR.getCode().equals(inspectResult.getStatus())) {
				hasError = true;
				errorMessage = inspectResult.getErrorMsg();
			}

			InspectStatus inspectStatus = null;
			if (isDoStop.get()) {
				inspectStatus = InspectStatus.ERROR;
				errorMessage = "Exit of user stop";
			} else if (hasError) {
				inspectStatus = InspectStatus.ERROR;
			} else {
				switch (Inspect.Mode.fromValue(inspect.getMode())) {
					case CRON:
						inspectStatus = InspectStatus.WAITING;
						break;
					case MANUAL:
						inspectStatus = InspectStatus.DONE;
						break;
					default:
						break;
				}
			}
			String msg = errorMessage;
			Optional.ofNullable(inspectStatus).ifPresent(status -> inspectService.updateStatus(inspect.getId(), status, msg));
			logger.info("Execute inspect task[{}] verification done, status: {}, msg: {}", inspect.getName(), inspectStatus.getCode(), msg);
		} catch (Throwable e) {
			logger.error("Execute inspect task[{}] verification failed", inspect.getName(), e);
			if (isDoStop.get()) {
				inspectResult.setErrorMsg("Failed of user stop");
				inspectService.updateStatus(inspect.getId(), InspectStatus.ERROR, "Failed of user stop: " + e.getMessage());
			} else if (null == inspectResult.getErrorMsg()) {
				inspectResult.setErrorMsg("Execute data verification error: " + e.getMessage());
				inspectService.updateStatus(inspect.getId(), InspectStatus.ERROR, "Execute data verification error: " + e.getMessage());
			}
		} finally {
			inspectService.onInspectStopped(inspect);
			releaseConectorNodes(connectorNodeMap);
			if(judgeAutomaticCheck(inspectResult)){
				inspectService.handleDiffInspect(inspect, syncTaskDelay,inspectResult);
			}
		}
	}

    protected void initDifferenceInspect(InspectResult inspectResult, String firstCheckId) throws NonsupportMethodDifferenceInspectException {
        InspectResult lastInspectResult;
        try {
            if (!(InspectMethod.FIELD.equalsString(inspect.getInspectMethod())
                || InspectMethod.JOINTFIELD.equalsString(inspect.getInspectMethod()))) {
                throw new NotfoundLastResultInspectException(inspect.getId(), inspect.getInspectMethod());
            } else {
                // 取最后一次有差异结果
                lastInspectResult = inspectService.getLastDifferenceInspectResult(inspect.getId(), firstCheckId);
                inspectResult.setFirstCheckId(firstCheckId);
                if (null == lastInspectResult) {
                    throw new NonsupportMethodDifferenceInspectException(inspect.getId(), firstCheckId);
                }
            }
        } catch (NotfoundLastResultInspectException|NonsupportMethodDifferenceInspectException e) {
            inspectResult.setInspect(inspect);
            inspectResult.setEnd(new Date());
            inspectResult.setStatus(InspectStatus.ERROR.getCode());
            inspectResult.setErrorMsg(e.getMessage());
            inspectResult.setStats(Collections.singletonList(new InspectResultStats()));
            inspectService.upsertInspectResult(inspectResult, false);
            throw e;
        }

        inspectResult.setId(null); // 使用新ID保存差异结果
        inspectResult.setInspect(lastInspectResult.getInspect());
        Map<String, com.tapdata.entity.inspect.InspectTask> configTaskMap = new HashMap<>();
        inspect.getTasks().forEach(configTask -> configTaskMap.put(configTask.getTaskId(), configTask));
        inspectResult.getInspect().getTasks().forEach(task -> {
            InspectResultStats stats = new InspectResultStats();

            stats.setStart(new Date());
            stats.setStatus("running");
            stats.setProgress(0);
            stats.setTaskId(task.getTaskId());
            stats.setSource(task.getSource());
            stats.setTarget(task.getTarget());

            configTaskMap.computeIfPresent(task.getTaskId(), (k, v) -> {
                task.getSource().setNodeId(v.getSource().getNodeId());
                task.getTarget().setNodeId(v.getTarget().getNodeId());
                return v;
            });

            inspectResult.getStats().add(stats);
        });
        inspectResult.setParentId(lastInspectResult.getId());
        inspectResult.setFirstSourceTotal(lastInspectResult.getFirstSourceTotal());
        inspectResult.setFirstTargetTotal(lastInspectResult.getFirstTargetTotal());
        for (int i = 0, len1 = inspectResult.getStats().size(), len2 = lastInspectResult.getStats().size(); i < len1 && i < len2; i++) {
            inspectResult.getStats().get(i).setFirstSourceTotal(lastInspectResult.getStats().get(i).getFirstSourceTotal());
            inspectResult.getStats().get(i).setFirstTargetTotal(lastInspectResult.getStats().get(i).getFirstTargetTotal());
        }
		List<InspectResultStats> lastPassedStats = lastInspectResult.getStats().stream().filter(lastResult -> "passed".equals(lastResult.getResult())).collect(Collectors.toList());
		List<String> passedTaskIds = new ArrayList<>();
		lastPassedStats.forEach(passed -> {
			passedTaskIds.add(passed.getTaskId());
		});

		List<InspectResultStats> collect = inspectResult.getStats().stream().filter(t -> passedTaskIds.contains(t.getTaskId())).collect(Collectors.toList());
		inspectResult.getStats().removeAll(collect);
		inspectResult.getStats().addAll(lastPassedStats);
		List<com.tapdata.entity.inspect.InspectTask> tasks = inspectResult.getInspect().getTasks().stream().filter(t -> !passedTaskIds.contains(t.getTaskId())).collect(Collectors.toList());
		inspectResult.getInspect().setTasks(tasks);

        inspectService.upsertInspectResult(inspectResult, false);
    }

	public boolean judgeAutomaticCheck(InspectResult inspectResult) {
		String inspectMethod = inspect.getInspectMethod();
		if (!"field".equals(inspectMethod) && !"jointField".equals(inspectMethod)) {
			return false;
		}
		if ("cron".equals(inspect.getMode()) || StringUtils.isEmpty(inspect.getFlowId())) {
			return false;
		} else {
			if (null == inspect.getDiffInspectTimes() && StringUtils.isEmpty(inspect.getByFirstCheckId())) {
				// 如果是初次校验初始化自动校验次数
				inspect.setDiffInspectTimes(2);
			}
			if (null == inspect.getDiffInspectTimes() || inspect.getDiffInspectTimes() <= 0) {
				return false;
			}
		}

        // 单表差异数达到阈值，不进行差异校验
        if (Optional.ofNullable(inspectResult.getInspect())
            .map(Inspect::getLimit)
            .map(InspectLimit::getKeep)
            .map(limit -> {
            for (InspectResultStats stats : inspectResult.getStats()) {
                if (stats.getRow_failed() >= limit) return true;
            }
            return false;
        }).orElse(false)) return false;

		if (TaskDto.STATUS_RUNNING.equals(syncTaskStatus) &&
				(ParentTaskDto.TYPE_INITIAL_SYNC_CDC.equals(syncTaskType) || ParentTaskDto.TYPE_CDC.equals(syncTaskType))
				&& judgeJobStatus(inspectResult)) {
			return true;
		}
		return false;
	}

	public void initTaskDelayAndStatus() {
		if (StringUtils.isEmpty(inspect.getFlowId())) {
			return;
		}
		TaskDto task = clientMongoOperator.findOne(
				new Query(Criteria.where("_id").is(inspect.getFlowId())), ConnectorConstant.TASK_COLLECTION, TaskDto.class);
		if (task != null) {
			this.syncTaskDelay = task.getDelayTime();
			this.syncTaskStatus = task.getStatus();
			this.syncTaskType = task.getType();
		}
	}

	public boolean judgeJobStatus(InspectResult inspectResult) {
		boolean flag = false;
		List<InspectResultStats> stats = inspectResult.getStats();
		for (InspectResultStats inspectResultStats : stats) {
			if (InspectStatus.ERROR.getCode().equals(inspectResultStats.getStatus())) {
				return false;
			} else if ("failed".equals(inspectResultStats.getResult())
					&& InspectStatus.DONE.getCode().equals(inspectResultStats.getStatus())) {
				flag = true;
			}
		}
		return flag;
	}


	private Map<String, ConnectorNode> initConnectorNodeMap(Map<String, Connections> connectionsMap) {
		Map<String, ConnectorNode> connectorNodeMap = new HashMap<>();
		for (com.tapdata.entity.inspect.InspectTask task : inspect.getTasks()) {
			// Init source ConnectorNode
			String sourceNodeId = task.getSource().getNodeId();
			String sourceKey = sourceNodeId;
			if (StringUtils.isBlank(sourceKey)) {
				sourceKey = ObjectId.get().toHexString();
				task.getSource().setNodeId(sourceKey);
			}
			Connections sourceConn = connectionsMap.get(task.getSource().getConnectionId());
			if (!connectorNodeMap.containsKey(sourceKey)) {
				connectorNodeMap.put(sourceKey, initConnectorNode(sourceNodeId, sourceConn));
			}

			// Init target ConnectorNode
			String targetNodeId = task.getTarget().getNodeId();
			String targetKey = targetNodeId;
			if (StringUtils.isBlank(targetKey)) {
				targetKey = ObjectId.get().toHexString();
				task.getTarget().setNodeId(targetKey);
			}
			Connections targetConn = connectionsMap.get(task.getTarget().getConnectionId());
			if (!connectorNodeMap.containsKey(targetKey)) {
				connectorNodeMap.put(targetKey, initConnectorNode(targetNodeId, targetConn));
			}
		}
		return connectorNodeMap;
	}

	private ConnectorNode initConnectorNode(String nodeId, Connections connection) {
		DatabaseTypeEnum.DatabaseType databaseType = ConnectionUtil.getDatabaseType(clientMongoOperator, connection.getPdkHash());
		String associateId = InspectTask.class.getSimpleName() + "-" + UUID.randomUUID();
		KVReadOnlyMap<TapTable> tapTableMap;
		if (null == nodeId || nodeId.isEmpty()) {
			associateId += "-conn-" + connection.getId();
			tapTableMap = new ConnectionTapTableMap(connection.getId(), connection.getName());
		} else {
			associateId += "-node-" + nodeId;
			tapTableMap = new PdkTableMap(TapTableUtil.getTapTableMapByNodeId(nodeId));
		}

		return PdkUtil.createNode(
				inspect.getFlowId(),
				databaseType,
				clientMongoOperator,
				associateId,
				connection.getConfig(),
				tapTableMap,
				new PdkStateMap(connection.getId(), HazelcastUtil.getInstance()),
				PdkStateMap.globalStateMap(HazelcastUtil.getInstance()),
				InstanceFactory.instance(LogFactory.class).getLog()
		);
	}

	private void releaseConectorNodes(Map<String, ConnectorNode> connectorNodeMap) {
		if (MapUtils.isEmpty(connectorNodeMap)) return;
		for (ConnectorNode connectorNode : connectorNodeMap.values()) {
			if (null == connectorNode || StringUtils.isBlank(connectorNode.getAssociateId()))
				continue;
			PDKInvocationMonitor.invoke(connectorNode, PDKMethod.STOP, connectorNode::connectorStop, this.getClass().getSimpleName());
			PDKIntegration.releaseAssociateId(connectorNode.getAssociateId());
		}
	}

	/**
	 * create a table Inspect job
	 *
	 * @param inspectTaskContext
	 * @return
	 * @name
	 */
	public abstract Runnable createTableInspectJob(InspectTaskContext inspectTaskContext);

	/**
	 * compose inspect result
	 *
	 * @param inspectResult
	 */
	private void composeInspectResult(InspectResult inspectResult) {

		if (inspectResult.getStats() != null) {

			AtomicBoolean isRunning = new AtomicBoolean(false);
			AtomicBoolean hasError = new AtomicBoolean(false);
			AtomicReference<Double> progressSum = new AtomicReference<>((double) 0);
			AtomicLong firstSourceTotal = new AtomicLong(0);
			AtomicLong firstTargetTotal = new AtomicLong(0);
			AtomicLong sourceTotal = new AtomicLong(0);
			AtomicLong targetTotal = new AtomicLong(0);
			StringBuilder sb = new StringBuilder();

			for (int i = 0; i < inspectResult.getStats().size(); i++) {
				InspectResultStats stats = inspectResult.getStats().get(i);
				if ("running".equalsIgnoreCase(stats.getStatus())) {
					isRunning.set(true);
				}
				if ("error".equalsIgnoreCase(stats.getStatus())) {
					hasError.set(true);
					sb.append(stats.getErrorMsg());
				}
				if (inspectResult.getId().equals(inspectResult.getFirstCheckId())) {
					stats.setFirstSourceTotal(stats.getSource_total());
					stats.setFirstTargetTotal(stats.getTarget_total());
				}
				progressSum.updateAndGet(v -> v + stats.getProgress());
				firstSourceTotal.updateAndGet(v -> v + stats.getFirstSourceTotal());
				firstTargetTotal.updateAndGet(v -> v + stats.getFirstTargetTotal());
				sourceTotal.updateAndGet(v -> v + stats.getSource_total());
				targetTotal.updateAndGet(v -> v + stats.getTarget_total());
			}

			inspectResult.setStatus(hasError.get() ? "error" : isRunning.get() ? "running" : "done");
			inspectResult.setErrorMsg(hasError.get() ? sb.toString() : null);
			inspectResult.setFirstSourceTotal(firstSourceTotal.get());
			inspectResult.setFirstTargetTotal(firstTargetTotal.get());
			inspectResult.setSource_total(sourceTotal.get());
			inspectResult.setTarget_total(targetTotal.get());
			inspectResult.setProgress(progressSum.get() / inspectResult.getStats().size());
		}

	}
}
