package io.tapdata.inspect.util;

import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.Log4jUtil;
import com.tapdata.entity.inspect.InspectDataSource;
import com.tapdata.entity.inspect.InspectResultStats;
import com.tapdata.tm.commons.util.MetaType;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.exception.TapPdkBaseException;
import io.tapdata.inspect.InspectTaskContext;
import io.tapdata.pdk.apis.entity.QueryOperator;
import io.tapdata.pdk.apis.entity.TapAdvanceFilter;
import io.tapdata.pdk.core.error.TapPdkRunnerUnknownException;
import io.tapdata.pdk.core.utils.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Consumer;

public class InspectJobUtil {

    public static TapAdvanceFilter wrapFilter(List<QueryOperator> srcConditions) {
        TapAdvanceFilter tapAdvanceFilter = TapAdvanceFilter.create();
        tapAdvanceFilter.setOperators(srcConditions);
        DataMap match = new DataMap();
        if (null != srcConditions) {
            srcConditions.stream()
                    .filter(op-> op.getOperator() == 5)
                    .forEach(op-> match.put(op.getKey(), op.getValue()));
        }
        tapAdvanceFilter.setMatch(match);
        return tapAdvanceFilter;
    }

    public static TapTable getTapTable(InspectDataSource inspectDataSource, InspectTaskContext inspectTaskContext) {
        Map<String, Object> params = new HashMap<>();
        params.put("connectionId", inspectDataSource.getConnectionId());
        params.put("metaType", MetaType.table.name());
        String table = inspectDataSource.getTable();
        params.put("tableName", table);
        TapTable tapTable = null == inspectTaskContext ?
                null :
                inspectTaskContext
                        .getClientMongoOperator()
                        .findOne(params, ConnectorConstant.METADATA_INSTANCE_COLLECTION + "/metadata/v2", TapTable.class);
        if (null == tapTable) {
            tapTable = new TapTable(table);
        }
        if(CollectionUtils.isNotEmpty(inspectDataSource.getColumns())){
            LinkedHashMap<String, TapField> fieldMap = tapTable.getNameFieldMap();
            LinkedHashMap<String, TapField> finalFieldMap = new LinkedHashMap<>();
            for (String column: inspectDataSource.getColumns()){
                if(null != fieldMap && fieldMap.get(column) != null)finalFieldMap.put(column,fieldMap.get(column));
            }
            tapTable.setNameFieldMap(finalFieldMap);
        }
        return tapTable;
    }

    public static List<String> checkRowCountInspectTaskDataSource(String prefix, InspectDataSource dataSource) {
        List<String> errorMsg = new ArrayList<>();
        if (null == dataSource){
            errorMsg.add(prefix + ".inspectDataSource can not be null");
            return errorMsg;
        }
        if (StringUtils.isEmpty(dataSource.getConnectionId())) {
            errorMsg.add(prefix + ".connectionId can not be empty");
        }
        if (StringUtils.isEmpty(dataSource.getTable())) {
            errorMsg.add(prefix + ".table can not be empty");
        }
        return errorMsg;
    }

    public static void buildStatsErrorMsg(InspectResultStats stats, Throwable e) {
        Throwable throwable = CommonUtils.matchThrowable(e, TapPdkBaseException.class);
        if (null != throwable) {
            stats.setErrorMsg("Inspect task occur an error when inspect table: [ " +((TapPdkBaseException)throwable).getTableName() + " ]\n" + e.getMessage() + "\n" + Log4jUtil.getStackString(e));
            return;
        }
        throwable = CommonUtils.matchThrowable(e, TapPdkRunnerUnknownException.class);
        if (null != throwable) {
            stats.setErrorMsg("Inspect task occur an error when inspect table: [ " +((TapPdkRunnerUnknownException)throwable).getTableName() + " ]\n" + e.getMessage() + "\n" + Log4jUtil.getStackString(e));
        } else {
            stats.setErrorMsg(e.getMessage() + "\n" + Log4jUtil.getStackString(e));
        }
    }

    public static Consumer<RuntimeException> buildErrorConsumer(String tableName) {
        return err -> {
            Throwable pdkBaseThrowable = CommonUtils.matchThrowable(err, TapPdkBaseException.class);
            if (null != pdkBaseThrowable) {
                ((TapPdkBaseException)err).setTableName(tableName);
                throw err;
            }
            Throwable pdkUnknownThrowable = CommonUtils.matchThrowable(err, TapPdkRunnerUnknownException.class);
            if (null != pdkUnknownThrowable) {
                ((TapPdkRunnerUnknownException)err).setTableName(tableName);
                throw err;
            }
            throw err;
        };
    }
}