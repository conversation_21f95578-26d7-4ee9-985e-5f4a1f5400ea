package io.tapdata.storage.errors;

/**
 * <AUTHOR>
 */
public interface StorageErrors {

	int SEQUENCE_STORAGE_EXISTS = 16000;
	int SEQUENCE_STORAGE_DOESNT_EXISTS = 16001;
	int INITIALIZE_ON_WRONG_STATE = 16002;
	int ILLEGAL_ARGUMENT = 16003;
	int OPEN_OUTPUT_STREAM_FAILED = 16004;
	int ADD_OBJECT_FAILED = 16005;
	int ADD_OBJECT_ON_WRONG_STATE = 16006;
	int ITERATE_ON_WRONG_STATE = 16007;
	int NEXT_READ_FULLY_FAILED = 16008;
	int NEXT_LENGTH_READ_FAILED = 16009;
	int OPEN_INPUT_STREAM_FAILED = 16010;
	int KV_STORAGE_PUT_FAILED = 16011;
	int KV_STORAGE_GET_FAILED = 16012;
	int KV_STORAGE_DELETE_FAILED = 16013;
	int UNKNOWN_ERROR_IN_STATE_MACHINE = 16014;
}
