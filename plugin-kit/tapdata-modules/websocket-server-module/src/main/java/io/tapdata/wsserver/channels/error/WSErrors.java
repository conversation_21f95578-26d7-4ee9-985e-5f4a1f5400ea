package io.tapdata.wsserver.channels.error;

public interface WSErrors {
	int ERROR_ENCODER_NOT_FOUND = 6001;
	int ERROR_UNKNOWN = 6002;
	int ERROR_ILLEGAL_OFFSET = 6003;
	int ERROR_ILLEGAL_LIMIT = 6004;
	int ERROR_ILLEGAL_PARAMETERS = 6005;
	int ERROR_ILLEGAL_SORT = 6006;
	int ERROR_UNAUTHORISED_SERVICE_CALL = 6007;
	int ERROR_ILLEGAL_TOKEN = 6008;
	int ERROR_GATEWAY_TOKEN_NOT_FOUND = 6009;
	int ERROR_GATEWAY_USER_NOT_EXIST = 6010;
	int ERROR_CHANNEL_KICKED = 6011;
	int ERROR_VERIFY_AUTHORISED_TOKEN_FAILED = 6012;
	int ERROR_USER_CHANNEL_NULL = 6013;
	int ERROR_CHANNEL_KICKED_BY_DEVICE = 6014;
	int ERROR_CHANNEL_KICKED_BY_CONCURRENT = 6015;
	int ERROR_LOGIN_FAILED_DEVICE_TOKEN_CHANGED = 6016;
	int ERROR_EXCEED_USER_CHANNEL_CAPACITY = 6017;
	int ERROR_GATEWAY_CHANNEL_NOT_EXIST = 6018;
	int ERROR_CHANNEL_USER_CLOSED = 6019;
	int ERROR_USER_NOT_EXIST = 6020;
	int ERROR_GATEWAY_SESSION_MANAGER_NOT_STARTED = 6021;
	int ERROR_GATEWAY_SESSION_HANDLER_CLASS_IS_NULL = 6022;
	int ERROR_USER_SESSION_NOT_EXIST = 6023;
	int ERROR_WRONG_NODE = 6024;
	int ERROR_CHANNEL_BYE_BYE = 6025;
}
