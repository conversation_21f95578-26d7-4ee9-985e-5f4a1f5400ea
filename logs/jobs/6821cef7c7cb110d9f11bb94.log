[TRACE] 2025-05-12 10:35:36.506 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Start task milestones: 6821cef7c7cb110d9f11bb94(CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537) 
[INFO ] 2025-05-12 10:35:36.531 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Loading table structure completed 
[TRACE] 2025-05-12 10:35:36.564 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-12 10:35:36.579 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - The engine receives CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-12 10:35:36.584 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Task started 
[TRACE] 2025-05-12 10:35:36.595 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] start preload schema,table counts: 1 
[TRACE] 2025-05-12 10:35:36.595 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] preload schema finished, cost 0 ms 
[TRACE] 2025-05-12 10:35:36.620 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-05-12 10:35:36.623 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-12 10:35:36.702 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6821c4393a705b6e41793e4a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6821bcc1c7cb110d9f11b68a_t1, version=v2, tableName=t1, externalStorageTableName=ExternalStorage_SHARE_CDC_88906171, shareCdcTaskId=6821c439c7cb110d9f11b6ad, connectionId=6821bcc1c7cb110d9f11b68a) 
[INFO ] 2025-05-12 10:35:36.769 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_taptest_ci_jdk17', table='null', ttlDay=3] 
[INFO ] 2025-05-12 10:35:37.125 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Source connector(qa_mongodb_repl_42240_share_1717403468657_3537) initialization completed 
[TRACE] 2025-05-12 10:35:37.125 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" read batch size: 100 
[TRACE] 2025-05-12 10:35:37.125 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" event queue capacity: 200 
[TRACE] 2025-05-12 10:35:37.125 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-12 10:35:37.204 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Use existing stream offset: {"cdcOffset":1747046137,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-05-12 10:35:37.327 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Batch read completed. 
[TRACE] 2025-05-12 10:35:37.329 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting stream read, table list: [t1, _tapdata_heartbeat_table], offset: {"cdcOffset":1747046137,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-05-12 10:35:37.329 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting incremental sync using database log parser 
[TRACE] 2025-05-12 10:35:37.329 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t1, _tapdata_heartbeat_table], data change syncing 
[TRACE] 2025-05-12 10:36:18.964 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] running status set to false 
[TRACE] 2025-05-12 10:36:18.967 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_e038b228fd8d46b4b8695e2793e62ec1_1747046137029 
[TRACE] 2025-05-12 10:36:18.967 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkShareCDCNode_e038b228fd8d46b4b8695e2793e62ec1_1747046137029 
[TRACE] 2025-05-12 10:36:18.967 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] schema data cleaned 
[TRACE] 2025-05-12 10:36:18.967 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] monitor closed 
[TRACE] 2025-05-12 10:36:18.967 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] close complete, cost 2 ms 
[TRACE] 2025-05-12 10:36:18.967 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] running status set to false 
[TRACE] 2025-05-12 10:36:18.978 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-05-12 10:36:18.978 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-05-12 10:36:18.978 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] schema data cleaned 
[TRACE] 2025-05-12 10:36:18.978 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] monitor closed 
[TRACE] 2025-05-12 10:36:18.978 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] close complete, cost 11 ms 
[TRACE] 2025-05-12 10:36:19.558 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-12 10:36:19.561 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@57bbdd44 
[TRACE] 2025-05-12 10:36:19.561 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Stop task milestones: 6821cef7c7cb110d9f11bb94(CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537)  
[TRACE] 2025-05-12 10:36:19.682 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Stopped task aspect(s) 
[TRACE] 2025-05-12 10:36:19.682 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Snapshot order controller have been removed 
[INFO ] 2025-05-12 10:36:19.683 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Task stopped. 
[TRACE] 2025-05-12 10:36:19.792 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Remove memory task client succeed, task: CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537[6821cef7c7cb110d9f11bb94] 
[TRACE] 2025-05-12 10:36:19.792 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Destroy memory task client cache succeed, task: CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537[6821cef7c7cb110d9f11bb94] 
[TRACE] 2025-05-12 10:36:46.307 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Start task milestones: 6821cef7c7cb110d9f11bb94(CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537) 
[INFO ] 2025-05-12 10:36:46.365 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Loading table structure completed 
[TRACE] 2025-05-12 10:36:46.365 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-12 10:36:46.385 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - The engine receives CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-12 10:36:46.385 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Task started 
[TRACE] 2025-05-12 10:36:46.401 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] start preload schema,table counts: 2 
[TRACE] 2025-05-12 10:36:46.401 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] preload schema finished, cost 0 ms 
[TRACE] 2025-05-12 10:36:46.424 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-05-12 10:36:46.424 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-12 10:36:46.500 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6821c4663a705b6e41794985, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6821bcc1c7cb110d9f11b68a_t2, version=v2, tableName=t2, externalStorageTableName=ExternalStorage_SHARE_CDC_88906172, shareCdcTaskId=6821c439c7cb110d9f11b6ad, connectionId=6821bcc1c7cb110d9f11b68a) 
[INFO ] 2025-05-12 10:36:46.500 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6821c4393a705b6e41793e4a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6821bcc1c7cb110d9f11b68a_t1, version=v2, tableName=t1, externalStorageTableName=ExternalStorage_SHARE_CDC_88906171, shareCdcTaskId=6821c439c7cb110d9f11b6ad, connectionId=6821bcc1c7cb110d9f11b68a) 
[INFO ] 2025-05-12 10:36:46.503 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_taptest_ci_jdk17', table='null', ttlDay=3] 
[INFO ] 2025-05-12 10:36:46.625 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Source connector(qa_mongodb_repl_42240_share_1717403468657_3537) initialization completed 
[TRACE] 2025-05-12 10:36:46.626 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" read batch size: 100 
[TRACE] 2025-05-12 10:36:46.626 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" event queue capacity: 200 
[INFO ] 2025-05-12 10:36:46.626 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-05-12 10:36:46.691 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Use existing stream offset: {"_data":{"value":"826821CF21000000012B022C0100296E5A1004CD1A864068C14C799F7449AB2457562F46645F696400646821C440C402E8208C3114710004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false}} 
[INFO ] 2025-05-12 10:36:46.691 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Batch read completed. 
[TRACE] 2025-05-12 10:36:46.692 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting stream read, table list: [t1, t2, _tapdata_heartbeat_table], offset: {"_data":{"value":"826821CF21000000012B022C0100296E5A1004CD1A864068C14C799F7449AB2457562F46645F696400646821C440C402E8208C3114710004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false}} 
[INFO ] 2025-05-12 10:36:46.692 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting incremental sync using database log parser 
[TRACE] 2025-05-12 10:36:46.893 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t1, t2, _tapdata_heartbeat_table], data change syncing 
[TRACE] 2025-05-12 10:36:56.428 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] running status set to false 
[TRACE] 2025-05-12 10:36:56.428 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_e038b228fd8d46b4b8695e2793e62ec1_1747046206537 
[TRACE] 2025-05-12 10:36:56.428 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkShareCDCNode_e038b228fd8d46b4b8695e2793e62ec1_1747046206537 
[TRACE] 2025-05-12 10:36:56.428 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] schema data cleaned 
[TRACE] 2025-05-12 10:36:56.428 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] monitor closed 
[TRACE] 2025-05-12 10:36:56.428 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] close complete, cost 4 ms 
[TRACE] 2025-05-12 10:36:56.428 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] running status set to false 
[TRACE] 2025-05-12 10:36:56.440 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-05-12 10:36:56.440 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-05-12 10:36:56.440 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] schema data cleaned 
[TRACE] 2025-05-12 10:36:56.440 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] monitor closed 
[TRACE] 2025-05-12 10:36:56.643 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] close complete, cost 12 ms 
[TRACE] 2025-05-12 10:37:00.621 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-12 10:37:00.621 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@15852d4a 
[TRACE] 2025-05-12 10:37:00.621 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Stop task milestones: 6821cef7c7cb110d9f11bb94(CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537)  
[TRACE] 2025-05-12 10:37:00.742 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Stopped task aspect(s) 
[TRACE] 2025-05-12 10:37:00.742 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Snapshot order controller have been removed 
[INFO ] 2025-05-12 10:37:00.742 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Task stopped. 
[TRACE] 2025-05-12 10:37:00.811 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Remove memory task client succeed, task: CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537[6821cef7c7cb110d9f11bb94] 
[TRACE] 2025-05-12 10:37:00.811 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Destroy memory task client cache succeed, task: CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537[6821cef7c7cb110d9f11bb94] 
[TRACE] 2025-05-12 10:37:46.555 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Start task milestones: 6821cef7c7cb110d9f11bb94(CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537) 
[INFO ] 2025-05-12 10:37:46.644 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Loading table structure completed 
[TRACE] 2025-05-12 10:37:46.644 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-12 10:37:46.672 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - The engine receives CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-12 10:37:46.672 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Task started 
[TRACE] 2025-05-12 10:37:46.694 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] start preload schema,table counts: 3 
[TRACE] 2025-05-12 10:37:46.694 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] preload schema finished, cost 0 ms 
[TRACE] 2025-05-12 10:37:46.704 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-05-12 10:37:46.704 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-12 10:37:46.709 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6821c4663a705b6e41794985, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6821bcc1c7cb110d9f11b68a_t2, version=v2, tableName=t2, externalStorageTableName=ExternalStorage_SHARE_CDC_88906172, shareCdcTaskId=6821c439c7cb110d9f11b6ad, connectionId=6821bcc1c7cb110d9f11b68a) 
[INFO ] 2025-05-12 10:37:46.709 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6821c4393a705b6e41793e4a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6821bcc1c7cb110d9f11b68a_t1, version=v2, tableName=t1, externalStorageTableName=ExternalStorage_SHARE_CDC_88906171, shareCdcTaskId=6821c439c7cb110d9f11b6ad, connectionId=6821bcc1c7cb110d9f11b68a) 
[INFO ] 2025-05-12 10:37:46.709 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6821c48c3a705b6e41795357, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6821bcc1c7cb110d9f11b68a_t3, version=v2, tableName=t3, externalStorageTableName=ExternalStorage_SHARE_CDC_88906173, shareCdcTaskId=6821c439c7cb110d9f11b6ad, connectionId=6821bcc1c7cb110d9f11b68a) 
[INFO ] 2025-05-12 10:37:46.718 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost/tapdata_taptest_ci_jdk17', table='null', ttlDay=3] 
[INFO ] 2025-05-12 10:37:46.864 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Source connector(qa_mongodb_repl_42240_share_1717403468657_3537) initialization completed 
[TRACE] 2025-05-12 10:37:46.864 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" read batch size: 100 
[TRACE] 2025-05-12 10:37:46.864 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Source node "qa_mongodb_repl_42240_share_1717403468657_3537" event queue capacity: 200 
[INFO ] 2025-05-12 10:37:46.864 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-05-12 10:37:46.922 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Use existing stream offset: {"_data":{"value":"826821CF46000000012B022C0100296E5A1004CD1A864068C14C799F7449AB2457562F46645F696400646821C440C402E8208C3114710004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false}} 
[INFO ] 2025-05-12 10:37:46.923 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Batch read completed. 
[TRACE] 2025-05-12 10:37:46.923 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting stream read, table list: [t1, t2, t3, _tapdata_heartbeat_table], offset: {"_data":{"value":"826821CF46000000012B022C0100296E5A1004CD1A864068C14C799F7449AB2457562F46645F696400646821C440C402E8208C3114710004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false}} 
[INFO ] 2025-05-12 10:37:46.923 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Starting incremental sync using database log parser 
[TRACE] 2025-05-12 10:37:47.128 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t1, t2, t3, _tapdata_heartbeat_table], data change syncing 
[TRACE] 2025-05-12 10:42:09.382 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] running status set to false 
[TRACE] 2025-05-12 10:42:09.390 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_e038b228fd8d46b4b8695e2793e62ec1_1747046266770 
[TRACE] 2025-05-12 10:42:09.390 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkShareCDCNode_e038b228fd8d46b4b8695e2793e62ec1_1747046266770 
[TRACE] 2025-05-12 10:42:09.391 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] schema data cleaned 
[TRACE] 2025-05-12 10:42:09.391 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] monitor closed 
[TRACE] 2025-05-12 10:42:09.391 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][qa_mongodb_repl_42240_share_1717403468657_3537] - Node qa_mongodb_repl_42240_share_1717403468657_3537[e038b228fd8d46b4b8695e2793e62ec1] close complete, cost 10 ms 
[TRACE] 2025-05-12 10:42:09.391 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] running status set to false 
[TRACE] 2025-05-12 10:42:09.400 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-05-12 10:42:09.400 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-05-12 10:42:09.400 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] schema data cleaned 
[TRACE] 2025-05-12 10:42:09.400 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] monitor closed 
[TRACE] 2025-05-12 10:42:09.606 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c4d75bae44d9434abac78fe3237d72a7] close complete, cost 9 ms 
[TRACE] 2025-05-12 10:42:12.710 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-12 10:42:12.710 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@665d1400 
[TRACE] 2025-05-12 10:42:12.828 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Stop task milestones: 6821cef7c7cb110d9f11bb94(CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537)  
[TRACE] 2025-05-12 10:42:12.828 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Stopped task aspect(s) 
[TRACE] 2025-05-12 10:42:12.895 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Snapshot order controller have been removed 
[INFO ] 2025-05-12 10:42:12.900 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Task stopped. 
[TRACE] 2025-05-12 10:42:12.901 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Remove memory task client succeed, task: CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537[6821cef7c7cb110d9f11bb94] 
[TRACE] 2025-05-12 10:42:12.901 - [CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537] - Destroy memory task client cache succeed, task: CDC log cache task from qa_mongodb_repl_42240_share_1717403468657_3537[6821cef7c7cb110d9f11bb94] 
