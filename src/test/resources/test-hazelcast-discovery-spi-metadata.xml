<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<hazelcast xmlns="http://www.hazelcast.com/schema/config"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://www.hazelcast.com/schema/config
           http://www.hazelcast.com/schema/config/hazelcast-config-5.2.xsd">

    <properties>
        <property name="hazelcast.discovery.enabled">true</property>
    </properties>

    <network>
        <join>
            <multicast enabled="false"/>
            <tcp-ip enabled="false"/>
            <aws enabled="false"/>
            <discovery-strategies>
                <discovery-strategy enabled="true"
                                    class="com.hazelcast.spi.discovery.DiscoverySpiTest$MetadataProvidingDiscoveryStrategy">
                    <properties>
                        <property name="key-string">foo</property>
                        <property name="key-int">123</property>
                        <property name="key-boolean">true</property>
                    </properties>
                </discovery-strategy>
            </discovery-strategies>
        </join>
    </network>

</hazelcast>
