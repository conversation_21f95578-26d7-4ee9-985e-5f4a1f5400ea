/*
 * Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.hazelcast.cp.event;

import java.util.EventListener;

/**
 * CPMembershipListener is notified when a CP member is added to
 * or removed from the CP Subsystem.
 *
 * @see com.hazelcast.cp.CPSubsystemManagementService
 * @see com.hazelcast.cp.CPSubsystem#addMembershipListener(CPMembershipListener)
 * @since 4.1
 */
public interface CPMembershipListener extends EventListener {

    /**
     * Called when a new CP member is added to the CP Subsystem.
     *
     * @param event membership event
     */
    void memberAdded(CPMembershipEvent event);

    /**
     * Called when a CP member is removed from the CP Subsystem.
     *
     * @param event membership event
     */
    void memberRemoved(CPMembershipEvent event);
}
