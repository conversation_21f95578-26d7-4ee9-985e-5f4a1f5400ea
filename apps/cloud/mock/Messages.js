const Mock = require('mockjs')
const Random = Mock.Random

module.exports = {
  '/tm/api/Messages': {
    data: {
      items: [
        {
          id: '60f9178e94e7326360d582e8',
          groupId: '45192760f91779be0f180057739639',
          read: false,
          createTime: Random.datetime(),
          email: '<EMAIL>',
          level: 'ERROR',
          msg: 'encounterERRORSkipped',
          serverName: '新任务@下午2:59:28',
          sourceId: '60f91779be0f180057739639',
          system: 'migration',
          time: '2021-07-22T07:28:17.458Z',
          title: 'encounterERRORSkipped',
          userId: '5fcf01b8f89acdf892e8bf78',
          username: 'admin',
        },
        {
          id: '60f91df9be0f18005773a7f2',
          level: 'ERROR',
          system: 'migration',
          msg: 'stoppedByError',
          title: 'stoppedByError',
          serverName: '新任务@下午2:59:28',
          sourceId: '60f91779be0f180057739639',
          userId: '5fcf01b8f89acdf892e8bf78',
          email: '<EMAIL>',
          username: 'admin',
          mappingTemplate: 'cluster-clone',
          read: false,
          time: '2021-07-22T07:27:53.916Z',
          last_updated: '2021-07-22T07:27:53.917Z',
          createTime: '2021-07-22T07:27:53.917Z',
        },
        {
          id: '60f91decbe0f18005773a7c4',
          level: 'INFO',
          system: 'migration',
          msg: 'started',
          title: 'started',
          serverName: '新任务@下午2:59:28',
          sourceId: '60f91779be0f180057739639',
          userId: '5fcf01b8f89acdf892e8bf78',
          email: '<EMAIL>',
          username: 'admin',
          mappingTemplate: 'cluster-clone',
          read: false,
          time: '2021-07-22T07:27:40.935Z',
          last_updated: '2021-07-22T07:27:40.937Z',
          createTime: '2021-07-22T07:27:40.937Z',
        },
        {
          id: '60f9179bbe0f1800577396a1',
          level: 'ERROR',
          system: 'migration',
          msg: 'stoppedByError',
          title: 'stoppedByError',
          serverName: '新任务@下午2:59:28',
          sourceId: '60f91779be0f180057739639',
          userId: '5fcf01b8f89acdf892e8bf78',
          email: '<EMAIL>',
          username: 'admin',
          mappingTemplate: 'cluster-clone',
          read: false,
          time: '2021-07-22T07:00:43.463Z',
          last_updated: '2021-07-22T07:00:43.466Z',
          createTime: '2021-07-22T07:00:43.466Z',
        },
        {
          id: '60f91783be0f180057739651',
          level: 'INFO',
          system: 'migration',
          msg: 'started',
          title: 'started',
          serverName: '新任务@下午2:59:28',
          userId: '5fcf01b8f89acdf892e8bf78',
          email: '<EMAIL>',
          username: 'admin',
          mappingTemplate: 'cluster-clone',
          last_updated: '2021-07-22T07:00:19.626Z',
          createTime: '2021-07-22T07:00:19.626Z',
        },
        {
          id: '60f8eeb694e73263605c256a',
          groupId: '325385355inspectValue',
          createTime: '2021-07-22T04:06:14.470Z',
          diff: 641,
          email: '<EMAIL>',
          inspectId: '60dfd3a0d843f6005764dbab',
          jobName: 'PG-TO-MY-DATE2',
          level: 'ERROR',
          msg: 'inspectValue',
          read: false,
          resultId: '60f8eeb0be0f1800577321fc',
          serverName: 'PG-TO-MY-DATE2',
          system: 'inspect',
          time: '2021-07-22T04:06:14.497Z',
          title: 'inspectValue',
          type: 'field',
          userId: '5fcf01b8f89acdf892e8bf78',
          username: 'admin',
        },
        {
          id: '60f8ed5a94e7326360585a8a',
          groupId: '5649160f61cf4be0f1800576ba967',
          read: true,
          CDCTime: 87576,
          cpu: '12.01',
          createTime: '2021-07-22T04:00:26.202Z',
          email: null,
          host: 'tapdaas-test-tapdaas-5bdff4bf5-7cgjw',
          javaCpu: 0,
          javaMem: '1.96',
          level: 'WARN',
          mem: '98.91',
          msg: 'CDCLag',
          network:
            '&nbsp&nbsp&nbsp&nbspRx : 11.61 Mbit per second<br/>&nbsp&nbsp&nbsp&nbspTx : 1.89 Mbit per second<br/>',
          serverName: '多表全量+增量sync，mysql到mongodb的demo',
          sourceId: '60f61cf4be0f1800576ba967',
          system: 'dataFlow',
          time: '2021-07-22T06:54:26.238Z',
          title: 'CDCLag',
          userId: '60d9346b0b18594e04e14ead',
          username: null,
          last_updated: '2021-07-22T06:54:35.422Z',
        },
        {
          id: '60f8ed5a94e7326360585a8b',
          groupId: '5649160f61cf4be0f1800576ba967',
          read: true,
          CDCTime: 87576,
          cpu: '12.04',
          createTime: '2021-07-22T04:00:26.202Z',
          email: null,
          host: 'tapdaas-test-tapdaas-5bdff4bf5-7cgjw',
          javaCpu: 0.5,
          javaMem: '1.96',
          level: 'WARN',
          mem: '99.18',
          msg: 'CDCLag',
          network:
            '&nbsp&nbsp&nbsp&nbspRx : 12.78 Mbit per second<br/>&nbsp&nbsp&nbsp&nbspTx : 2.55 Mbit per second<br/>',
          serverName: '多表全量+增量sync，mysql到mongodb的demo',
          sourceId: '60f61cf4be0f1800576ba967',
          system: 'dataFlow',
          time: '2021-07-22T04:00:26.202Z',
          title: 'CDCLag',
          userId: '60d9346b0b18594e04e14ead',
          username: null,
          last_updated: '2021-07-22T06:49:44.070Z',
        },
        {
          id: '60f8ed5a94e7326360585a8d',
          groupId: '5649160f61cf4be0f1800576ba967',
          read: false,
          CDCTime: 87576,
          cpu: '12.95',
          createTime: '2021-07-22T04:00:26.201Z',
          email: null,
          host: 'tapdaas-test-tapdaas-5bdff4bf5-7cgjw',
          javaCpu: 0.1,
          javaMem: '1.96',
          level: 'WARN',
          mem: '78.65',
          msg: 'CDCLag',
          network:
            '&nbsp&nbsp&nbsp&nbspRx : 12.03 Mbit per second<br/>&nbsp&nbsp&nbsp&nbspTx : 1.99 Mbit per second<br/>',
          serverName: '多表全量+增量sync，mysql到mongodb的demo',
          sourceId: '60f61cf4be0f1800576ba967',
          system: 'dataFlow',
          time: '2021-07-22T07:43:26.197Z',
          title: 'CDCLag',
          userId: '60d9346b0b18594e04e14ead',
          username: null,
        },
        {
          id: '60f8eb3494e732636050cb16',
          groupId: '45192460f8eb2cbe0f180057731876',
          read: false,
          createTime: '2021-07-22T03:52:16.571Z',
          email: '<EMAIL>',
          level: 'ERROR',
          msg: 'encounterERRORSkipped',
          serverName: 'test@fly',
          sourceId: '60f8eb2cbe0f180057731876',
          system: 'migration',
          time: '2021-07-22T03:52:16.571Z',
          title: 'encounterERRORSkipped',
          userId: '5fcf01b8f89acdf892e8bf78',
          username: 'admin',
        },
        {
          id: '60f8eb3fbe0f1800577318ca',
          level: 'ERROR',
          system: 'migration',
          msg: 'stoppedByError',
          title: 'stoppedByError',
          serverName: 'test@fly',
          sourceId: '60f8eb2cbe0f180057731876',
          userId: '5fcf01b8f89acdf892e8bf78',
          email: '<EMAIL>',
          username: 'admin',
          mappingTemplate: 'cluster-clone',
          read: false,
          time: '2021-07-22T03:51:27.813Z',
          last_updated: '2021-07-22T03:51:27.814Z',
          createTime: '2021-07-22T03:51:27.814Z',
        },
        {
          id: '60f8eb30be0f180057731886',
          level: 'INFO',
          system: 'migration',
          msg: 'started',
          title: 'started',
          serverName: 'test@fly',
          userId: '5fcf01b8f89acdf892e8bf78',
          email: '<EMAIL>',
          username: 'admin',
          mappingTemplate: 'cluster-clone',
          last_updated: '2021-07-22T07:04:37.926Z',
          createTime: '2021-07-22T03:51:12.032Z',
          read: true,
        },
        {
          id: '60f87cda94e73263604d9f55',
          groupId: '5649060f61cf4be0f1800576ba967',
          read: false,
          CDCTime: 87576,
          cpu: '12.97',
          createTime: '2021-07-21T20:00:26.069Z',
          email: null,
          host: 'tapdaas-test-tapdaas-5b4c65b57f-s8fvz',
          javaCpu: 0.3,
          javaMem: '1.85',
          level: 'WARN',
          mem: '67.72',
          msg: 'CDCLag',
          network:
            '&nbsp&nbsp&nbsp&nbspRx : 12.27 Mbit per second<br/>&nbsp&nbsp&nbsp&nbspTx : 2.12 Mbit per second<br/>',
          serverName: '多表全量+增量sync，mysql到mongodb的demo',
          sourceId: '60f61cf4be0f1800576ba967',
          system: 'dataFlow',
          time: '2021-07-21T20:00:26.069Z',
          title: 'CDCLag',
          userId: '60d9346b0b18594e04e14ead',
          username: null,
        },
        {
          id: '60f87cda94e73263604d9f56',
          groupId: '5649060f61cf4be0f1800576ba967',
          read: false,
          CDCTime: 87576,
          cpu: '12.97',
          createTime: '2021-07-21T20:00:26.069Z',
          email: null,
          host: 'tapdaas-test-tapdaas-5b4c65b57f-s8fvz',
          javaCpu: 0.3,
          javaMem: '1.85',
          level: 'WARN',
          mem: '67.72',
          msg: 'CDCLag',
          network:
            '&nbsp&nbsp&nbsp&nbspRx : 12.27 Mbit per second<br/>&nbsp&nbsp&nbsp&nbspTx : 2.12 Mbit per second<br/>',
          serverName: '多表全量+增量sync，mysql到mongodb的demo',
          sourceId: '60f61cf4be0f1800576ba967',
          system: 'dataFlow',
          time: '2021-07-21T20:00:26.069Z',
          title: 'CDCLag',
          userId: '60d9346b0b18594e04e14ead',
          username: null,
        },
        {
          id: '60f87cda94e73263604d9f54',
          groupId: '5649060f61cf4be0f1800576ba967',
          read: true,
          CDCTime: 87576,
          cpu: '11.80',
          createTime: '2021-07-21T20:00:26.068Z',
          email: null,
          host: 'tapdaas-test-tapdaas-5bdff4bf5-7cgjw',
          javaCpu: 0.4,
          javaMem: '1.96',
          level: 'WARN',
          mem: '99.14',
          msg: 'CDCLag',
          network:
            '&nbsp&nbsp&nbsp&nbspRx : 12.24 Mbit per second<br/>&nbsp&nbsp&nbsp&nbspTx : 2.13 Mbit per second<br/>',
          serverName: '多表全量+增量sync，mysql到mongodb的demo',
          sourceId: '60f61cf4be0f1800576ba967',
          system: 'dataFlow',
          time: '2021-07-22T03:59:26.175Z',
          title: 'CDCLag',
          userId: '60d9346b0b18594e04e14ead',
          username: null,
          last_updated: '2021-07-22T07:41:58.019Z',
        },
        {
          id: '60f86f3294e7326360308be5',
          groupId: '5648960f61cf4be0f1800576ba967',
          read: false,
          CDCTime: 87576,
          cpu: '12.51',
          createTime: '2021-07-21T19:02:10.145Z',
          email: null,
          host: 'tapdaas-test-tapdaas-5b4c65b57f-s8fvz',
          javaCpu: 0.1,
          javaMem: '1.85',
          level: 'WARN',
          mem: '67.65',
          msg: 'CDCLag',
          network:
            '&nbsp&nbsp&nbsp&nbspRx : 11.03 Mbit per second<br/>&nbsp&nbsp&nbsp&nbspTx : 1.82 Mbit per second<br/>',
          serverName: '多表全量+增量sync，mysql到mongodb的demo',
          sourceId: '60f61cf4be0f1800576ba967',
          system: 'dataFlow',
          time: '2021-07-21T19:59:26.147Z',
          title: 'CDCLag',
          userId: '60d9346b0b18594e04e14ead',
          username: null,
        },
        {
          id: '60f86f3294e7326360308be8',
          groupId: '5648960f61cf4be0f1800576ba967',
          read: false,
          CDCTime: 1878461,
          cpu: '12.11',
          createTime: '2021-07-21T19:02:10.145Z',
          email: null,
          host: 'tapdaas-test-tapdaas-5b4c65b57f-s8fvz',
          javaCpu: 0,
          javaMem: '1.85',
          level: 'WARN',
          mem: '63.94',
          msg: 'CDCLag',
          network:
            '&nbsp&nbsp&nbsp&nbspRx : 8.64 Mbit per second<br/>&nbsp&nbsp&nbsp&nbspTx : 1.53 Mbit per second<br/>',
          serverName: '多表全量+增量sync，mysql到mongodb的demo',
          sourceId: '60f61cf4be0f1800576ba967',
          system: 'dataFlow',
          time: '2021-07-21T19:02:10.145Z',
          title: 'CDCLag',
          userId: '60d9346b0b18594e04e14ead',
          username: null,
        },
        {
          id: '60f86f3294e7326360308beb',
          groupId: '5648960f61cf4be0f1800576ba967',
          read: false,
          CDCTime: 1878461,
          cpu: '12.11',
          createTime: '2021-07-21T19:02:10.143Z',
          email: null,
          host: 'tapdaas-test-tapdaas-5b4c65b57f-s8fvz',
          javaCpu: 0,
          javaMem: '1.85',
          level: 'WARN',
          mem: '63.94',
          msg: 'CDCLag',
          network:
            '&nbsp&nbsp&nbsp&nbspRx : 8.64 Mbit per second<br/>&nbsp&nbsp&nbsp&nbspTx : 1.53 Mbit per second<br/>',
          serverName: '多表全量+增量sync，mysql到mongodb的demo',
          sourceId: '60f61cf4be0f1800576ba967',
          system: 'dataFlow',
          time: '2021-07-21T19:02:10.143Z',
          title: 'CDCLag',
          userId: '60d9346b0b18594e04e14ead',
          username: null,
        },
        {
          id: '60f86f21be0f18005771cd1c',
          level: 'ERROR',
          system: 'agent',
          msg: 'connectionInterrupted',
          title: 'connectionInterrupted',
          serverName: '************',
          sourceId: '568c765b-9bc0-4950-ba24-46fd48884980',
          read: false,
          time: '2021-07-21T19:01:53.602Z',
          last_updated: '2021-07-21T19:01:53.614Z',
          createTime: '2021-07-21T19:01:53.614Z',
        },
        {
          id: '60f7df14be0f18005770545d',
          level: 'ERROR',
          system: 'sync',
          msg: 'stoppedByError',
          title: 'stoppedByError',
          serverName: '新任务_210f84a',
          sourceId: '60f0224fbb06d0005990c222',
          userId: '5fcf01b8f89acdf892e8bf78',
          email: '<EMAIL>',
          username: 'admin',
          mappingTemplate: 'custom',
          read: false,
          time: '2021-07-21T08:47:16.243Z',
          last_updated: '2021-07-21T08:47:16.246Z',
          createTime: '2021-07-21T08:47:16.246Z',
        },
      ],
      total: '@integer(0, 100)',
      size: 20,
      totalPage: 1,
    },
    code: 'ok',
    msg: 'ok',
  },
  '/tm/api/Messages/update': { data: { count: 1 }, code: 'ok', msg: 'ok' },
  '/tm/api/Messages/count': { data: '@integer(0, 100)', code: 'ok', msg: 'ok' },
}
