<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>io.tapdata</groupId>
        <artifactId>connectors-common</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>io.debezium</groupId>
    <artifactId>debezium-bucket</artifactId>
    <version>1.5.4.Final</version>
    <name>Debezium Build Aggregator</name>
    <description>Debezium is an open source change data capture platform</description>
    <packaging>pom</packaging>
    <properties>
        <!-- Instruct the build to use only UTF-8 encoding for source code -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <release>8</release>

        <!-- Maven Plugins -->
        <version.compiler.plugin>3.8.1</version.compiler.plugin>
        <version.resources.plugin>3.1.0</version.resources.plugin>
        <version.filtering.plugin>3.1.1</version.filtering.plugin>
        <version.dependency.plugin>3.1.1</version.dependency.plugin>
        <version.enforcer.plugin>3.0.0-M2</version.enforcer.plugin>

        <version.jar.plugin>3.0.2</version.jar.plugin>
        <version.source.plugin>3.1.0</version.source.plugin>
        <version.assembly.plugin>3.1.1</version.assembly.plugin>
        <version.war.plugin>2.5</version.war.plugin>
        <version.google.formatter.plugin>0.4</version.google.formatter.plugin>
        <version.docker.maven.plugin>0.31.0</version.docker.maven.plugin>
        <version.staging.plugin>1.6.8</version.staging.plugin>
        <version.protoc.maven.plugin>3.8.0</version.protoc.maven.plugin>
        <version.javadoc.plugin>3.1.1</version.javadoc.plugin>
        <version.code.formatter>2.11.0</version.code.formatter>
        <version.surefire.plugin>3.0.0-M3</version.surefire.plugin>
        <version.checkstyle.plugin>3.1.1</version.checkstyle.plugin>
        <version.release.plugin>2.5.3</version.release.plugin>
        <version.impsort>1.3.2</version.impsort>
        <version.failsafe.plugin>${version.surefire.plugin}</version.failsafe.plugin>
        <version.checkstyle>8.32</version.checkstyle>
        <version.revapi.plugin>0.11.5</version.revapi.plugin>
        <version.jandex>1.0.8</version.jandex>
        <version.revapi-java.plugin>0.21.0</version.revapi-java.plugin>
        <version.build-helper.plugin>1.9.1</version.build-helper.plugin>

        <!-- Which Maven Central infra should be used -->
        <release.endpoint>https://s01.oss.sonatype.org/</release.endpoint>

        <!-- Scala version used to build Kafka -->
        <version.kafka.scala>2.12</version.kafka.scala>

        <!-- ANTLR -->
        <antlr.version>4.7.2</antlr.version>

        <!-- Quarkus -->
        <quarkus.version>1.12.0.Final</quarkus.version>

        <!-- Databases, should align with database drivers in debezium-bom -->
        <version.mysql.server>5.7</version.mysql.server>
        <version.mongo.server>3.6</version.mongo.server>
        <version.cassandra>3.11.10</version.cassandra>

    </properties>

    <modules>
        <module>debezium-parent</module>
        <module>debezium-api</module>
        <module>debezium-embedded</module>
        <module>debezium-connector-mysql</module>
        <module>debezium-connector-postgres</module>
        <module>debezium-connector-highgo</module>
        <module>debezium-core</module>
        <module>debezium-bom</module>
        <module>debezium-ddl-parser</module>
    </modules>

    <repositories>
        <repository>
            <id>confluent</id>
            <name>Confluent</name>
            <url>https://packages.confluent.io/maven/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>ossrh</id>
            <name>OSS Sonatype Nexus</name>
            <url>${release.endpoint}/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.1.1</version>

                <configuration>
                    <finalName>${connector.file.name}</finalName>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <archive>
                        <manifestEntries>
                            <Implementation-Title>${project.artifactId}</Implementation-Title>
                            <Implementation-Version>${project.version}</Implementation-Version>
                            <Implementation-Vendor>${project.groupId}</Implementation-Vendor>
                        </manifestEntries>
                    </archive>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>

                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>

            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>4.2.1</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-source-plugin</artifactId>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <id>attach-sources</id>-->
            <!--                        <goals>-->
            <!--                            <goal>jar</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
        </plugins>
    </build>
</project>
