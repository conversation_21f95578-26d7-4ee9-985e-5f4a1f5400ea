<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <parent>
    <groupId>io.debezium</groupId>
    <artifactId>debezium-bucket</artifactId>
    <version>1.5.4.Final</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>debezium-parent</artifactId>
    <name>Debezium Parent POM</name>
    <description>Parent POM for all modules and connectors</description>
    <packaging>pom</packaging>
    <properties>

        <!-- Maven Plugins -->
        <version.compiler.plugin>3.8.1</version.compiler.plugin>
        <version.resources.plugin>3.1.0</version.resources.plugin>
        <version.filtering.plugin>3.1.1</version.filtering.plugin>
        <version.dependency.plugin>3.1.1</version.dependency.plugin>
        <version.enforcer.plugin>3.0.0-M2</version.enforcer.plugin>

        <version.jar.plugin>3.0.2</version.jar.plugin>
        <version.source.plugin>3.1.0</version.source.plugin>
        <version.assembly.plugin>3.1.1</version.assembly.plugin>
        <version.war.plugin>2.5</version.war.plugin>
        <version.google.formatter.plugin>0.4</version.google.formatter.plugin>
        <version.docker.maven.plugin>0.31.0</version.docker.maven.plugin>
        <version.staging.plugin>1.6.8</version.staging.plugin>
        <version.protoc.maven.plugin>3.8.0</version.protoc.maven.plugin>
        <version.javadoc.plugin>3.1.1</version.javadoc.plugin>
        <version.code.formatter>2.11.0</version.code.formatter>
        <version.surefire.plugin>3.0.0-M3</version.surefire.plugin>
        <version.checkstyle.plugin>3.1.1</version.checkstyle.plugin>
        <version.release.plugin>2.5.3</version.release.plugin>
        <version.impsort>1.3.2</version.impsort>
        <version.failsafe.plugin>${version.surefire.plugin}</version.failsafe.plugin>
        <version.checkstyle>8.32</version.checkstyle>
        <version.revapi.plugin>0.11.5</version.revapi.plugin>
        <version.jandex>1.0.8</version.jandex>
        <version.revapi-java.plugin>0.21.0</version.revapi-java.plugin>
        <version.build-helper.plugin>1.9.1</version.build-helper.plugin>

        <!-- Dockerfiles -->
        <docker.maintainer>Debezium community</docker.maintainer>

        <!--Skip long running tests by default-->
        <skipLongRunningTests>true</skipLongRunningTests>

        <!-- Don't skip integration tests by default -->
        <skipITs>false</skipITs>

        <!-- Do not skip formatting source code by default -->
        <format.skip>false</format.skip>

        <!-- Set formatting default goals -->
        <format.formatter.goal>format</format.formatter.goal>
        <format.imports.goal>sort</format.imports.goal>

        <!-- No debug options by default -->
        <debug.argline />

        <!-- No modules options by default -->
        <modules.argline />

        <!-- No test options by default -->
        <test.argline />

        <!-- Assembly configuration -->
        <assembly.descriptor>connector-distribution</assembly.descriptor>

        <!-- Needed for pre jdk 9 -->
        <useSystemClassLoader>true</useSystemClassLoader>

        <!-- Skip the API checks by default. Let the modules opt in. -->
        <revapi.skip>true</revapi.skip>

    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.debezium</groupId>
                <artifactId>debezium-bom</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--<build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${version.failsafe.plugin}</version>
                    <configuration>
                        <argLine>${debug.argline} ${modules.argline} ${test.argline}</argLine>
                        <useSystemClassLoader>${useSystemClassLoader}</useSystemClassLoader>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.googlecode.maven-java-formatter-plugin</groupId>
                    <artifactId>maven-java-formatter-plugin</artifactId>
                    <version>${version.google.formatter.plugin}</version>
                </plugin>
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${version.docker.maven.plugin}</version>
                </plugin>
                <plugin>
                    <groupId>com.github.os72</groupId>
                    <artifactId>protoc-jar-maven-plugin</artifactId>
                    <version>${version.protoc.maven.plugin}</version>
                </plugin>
                <plugin>
                    <groupId>net.revelc.code.formatter</groupId>
                    <artifactId>formatter-maven-plugin</artifactId>
                    <version>${version.code.formatter}</version>
                    <dependencies>
                        <dependency>
                            <groupId>io.debezium</groupId>
                            <artifactId>debezium-ide-configs</artifactId>
                            <version>${project.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <configFile>/eclipse/debezium-formatter.xml</configFile>
                        <skip>${format.skip}</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>net.revelc.code</groupId>
                    <artifactId>impsort-maven-plugin</artifactId>
                    <version>${version.impsort}</version>
                    <configuration>
                        <groups>java.,javax.,org.,com.,io.</groups>
                        <staticGroups>*</staticGroups>
                        <staticAfter>false</staticAfter>
                        <skip>${format.skip}</skip>
                        <removeUnused>true</removeUnused>
                    </configuration>
                </plugin>
               <plugin>
                    <groupId>org.jboss.jandex</groupId>
                    <artifactId>jandex-maven-plugin</artifactId>
                    <version>${version.jandex}</version>
                </plugin>
                <plugin>
                    <groupId>org.revapi</groupId>
                    <artifactId>revapi-maven-plugin</artifactId>
                    <version>${version.revapi.plugin}</version>
                    <dependencies>
                        <dependency>
                            <groupId>io.debezium</groupId>
                            <artifactId>debezium-revapi</artifactId>
                            <version>${project.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>org.revapi</groupId>
                            <artifactId>revapi-java</artifactId>
                            <version>${version.revapi-java.plugin}</version>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>net.revelc.code.formatter</groupId>
                <artifactId>formatter-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>${format.formatter.goal}</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>net.revelc.code</groupId>
                <artifactId>impsort-maven-plugin</artifactId>
                <configuration>
                    <removeUnused>true</removeUnused>
                </configuration>
                <executions>
                    <execution>
                        <id>sort-imports</id>
                        <goals>
                            <goal>${format.imports.goal}</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>attach-test-sources</id>
                        <goals>
                            <goal>test-jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.googlecode.maven-java-formatter-plugin</groupId>
                <artifactId>maven-java-formatter-plugin</artifactId>
                <configuration>
                    <configFile>${project.basedir}/support/eclipse-formatting.xml</configFile>
                    <lineEnding>LF</lineEnding>
                </configuration>
            </plugin>
            <plugin>
            &lt;!&ndash;
                   Build a test-jar for each project, so that src/test/* resources and
                   classes can be used in other projects. Also customize how the jar
                   files are assembled.
               &ndash;&gt;
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <executions>
                    <execution>
                        <id>test-jar</id>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <systemProperties combine.children="append">
                        <property>
                            <name>java.io.tmpdir</name>
                            <value>${project.build.directory}</value>
                        </property>
                        <property>
                            <name>skipLongRunningTests</name>
                            <value>${skipLongRunningTests}</value>
                        </property>
                        <property>
                            <name>dbz.test.data.dir</name>
                            <value>${project.build.directory}/data</value>
                        </property>
                    </systemProperties>
                    <argLine>-Djava.awt.headless=true ${debug.argline} ${modules.argline}</argLine>
                    &lt;!&ndash;runOrder>alphabetical</runOrder&ndash;&gt;
                    <useFile>false</useFile>
                    <enableAssertions>true</enableAssertions>
                    <forkCount>${forkCount}</forkCount>
                    <reuseForks>${reuseForks}</reuseForks>
                    <useSystemClassLoader>${useSystemClassLoader}</useSystemClassLoader>
                </configuration>
            </plugin>
            <plugin>
                &lt;!&ndash; Serves as support for configuring Revapi &ndash;&gt;
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>parse-version</id>
                        <goals>
                            &lt;!&ndash; This defines the ${parsedVersion.*} properties used in the Revapi config. &ndash;&gt;
                            <goal>parse-version</goal>
                        </goals>
                        <phase>validate</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.revapi</groupId>
                <artifactId>revapi-maven-plugin</artifactId>
                <configuration>
                    <failOnMissingConfigurationFiles>false</failOnMissingConfigurationFiles>
                    &lt;!&ndash; Consider changes from the latest .Final version, not from the latest non-snapshot. &ndash;&gt;
                    <versionFormat>\d+\.\d+\.\d+\.Final</versionFormat>
                    <ignoreSuggestionsFormat>xml</ignoreSuggestionsFormat>
                    <analysisConfigurationFiles>
                        <configurationFile>
                            &lt;!&ndash; common API checking configuration &ndash;&gt;
                            <resource>revapi/revapi-configuration.xml</resource>
                        </configurationFile>
                        <configurationFile>
                            &lt;!&ndash; API changes recorded in the support/revapi module &ndash;&gt;
                            <resource>revapi/debezium-api-changes.xml</resource>
                            <roots>
                                &lt;!&ndash;
                                    The XML file has "<analysisConfiguration>" root node, underneath which
                                    there are nodes named after each version.
                                    This way we only need a single file for all releases of Debezium.
                                &ndash;&gt;
                                <root>analysisConfiguration/version-${parsedVersion.majorVersion}.${parsedVersion.minorVersion}.${parsedVersion.incrementalVersion}</root>
                            </roots>
                        </configurationFile>
                    </analysisConfigurationFiles>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>-->
    <profiles>
        <!--
           ###################################################################
           Enable remotely debugging on the command line
           ###################################################################

           To use, specify "-Ddebug=true" on the Maven command line
           (typically used when running a single test). Then, start
           remote debugger (on port 1044) and connect.
       -->
        <profile>
            <id>debug</id>
            <activation>
                <property>
                    <name>debug</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <!-- Useful for remotely debugging the unit tests run by Surefire ... -->
                <debug.argline>-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=y,address=1044</debug.argline>
            </properties>
        </profile>
    </profiles>
</project>
