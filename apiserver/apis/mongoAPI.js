'use strict';

const API = require('./crudAPI');

class MongoAPI extends API {

  constructor (dataSource, apiConfig) {
    super(dataSource, apiConfig);
  }

  _generateModel () {
    const model = super._generateModel();
    return Object.assign(model, this._generateDownloadConfig(model.tableName));
  }

  _generateRepository () {
    const repo = super._generateRepository();
    return Object.assign(repo, this._generateDownloadConfig(repo.tableName));
  }

  _generateController () {
    const controller = super._generateController();
    if(!controller){
      return controller
    }
    Object.assign(controller, this._generateDownloadConfig(controller.tableName));
    if (controller.downloadApi) {
      const prefix = controller.apiPrefix;
      const roles = controller.api.findPage.roles || [];

      controller.api.downloadById = {
        type: 'preset',
        name: 'downloadById',
        path: `${prefix}/download`,
        summary: 'download file by id',
        roles
      };
      controller.api.download = {
        type: 'preset',
        name: 'download',
        path: `${prefix}/download`,
        summary: 'download file by filter, only return first file if multi files found',
        roles
      };
      controller.api.upload = {
        type: 'preset',
        name: 'upload',
        path: `${prefix}/upload`,
        summary: 'upload files',
        roles
      };
    }
    return controller;
  }

  _convertFields () {
    const fields = super._convertFields();
    const idField = fields.find(field => field.field_name === '_id');
    if (!fields.length || !idField) {
      fields.filter(field => field.primary_key_position > 0).forEach(field => {
        field.primary_key_position = 0;
      });
      fields.push({
        field_name: '_id',
        data_type: 'string',
        primary_key_position: 1
      });
    }
    return fields;
  }

  _generateDownloadConfig (tableName) {
    if(!tableName){
      return {};
    }
    const isDownloadAPI = tableName.endsWith('.files');
    return {
      downloadApi: isDownloadAPI,
      uploadApi: isDownloadAPI,
      bucketName: isDownloadAPI ? tableName.substring(0, tableName.indexOf('.files')) : 'fs'
    };
  }
}

module.exports = MongoAPI;
