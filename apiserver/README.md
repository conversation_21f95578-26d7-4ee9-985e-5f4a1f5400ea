# 1. <PERSON><PERSON>(MongoDB OpenAPI)基于 LoopBack 实现快速将MongoDB Atlas中的数据以RESTful API接口方式发布。
使用MongoDB Atlas + Moa，开发者无需设置应用服务器基础架构即可快速构建应用程序后台。
Moa 通过简单配置即可将MongoDB Atlas中的数据以OpenAPI的方式发布并浏览使用或者提供给开发者使用。


### 传统开发 & 使用 Moa 开发
| 传统方式构建后台 | 使用Maoa | 
| --- | --- | 
| 列出后台接口需求及规范 | 列出后台接口需求及规范 |
| 组织程序员开发后端程序 | 安装API Server 开源模块，本地部署统一认证 |
| 测试功能 | 在Moa平台配置 API，统一配置管理 | 
| 调试bug | 发布API，快速交付 |
| 准备硬件服务器 |  |
| 部署应用程序 |  |
| 部署数据库 |  |
| 配置高可用，权限等设置 |   |
| 测试并上线 |   |

# 2 架构
Moa 由API云端设计器、API Server、MongoDB Atlas三部分组成：

 - API云端设计器：统一配置管理API发布，权限认证
 - API Server ：用户本地环境运行的开源组件，用于执行API发布，提供 RESTful API 接口
 - MongoDB Atlas ：存储配置数据、作为 API Server 的数据源
    
![架构](img/architect.png "架构")

# 3. 如何使用 Moa

## 3.1 准备 Atlas cluster
 - 注册Atlas账号并导入数据 [Login or Register Atlas](https://cloud.mongodb.com/user#/atlas/login)、[MongoDB Atlas](https://docs.atlas.mongodb.com/)、[Import Data Into Cluster](https://docs.atlas.mongodb.com/import/)
 - 配置 Whitelist

## 3.2 注册 Moa 账号
注册地址： http://openapi.mongodb.expert

## 3.3 下载 API Server (分支 moa)
```bash
# 检出分支 moa 的代码
git clone --single-branch --branch=moa https://github.com/tapd8/apig.git

# 安装依赖
cd apig
npm install

```

## 3.4 创建 API Server ID 和 JWT认证密钥
 - 登录 Moa 打开 【API 服务器】 
 - 创建 API Server，下载配置文件
 
 	- 应用名称： 自定义应用名称
 	- 客户端 URL：API Server 运行起来后的访问URL，例如：http://127.0.0.1:3080
 	- JWT 密钥：客户端请求发布API接口时使用的的密钥
 	- 创建完成后，下载配置文件： config.js


## 3.5 配置启动 API Server

 - 将下载的 config.js 复制到 API Server 工程的根目录，替换默认 config.js
```bash

# 启动 API Server
npm start

```

## 3.6 配置发布 API

### 3.6.1 创建 MongoDB Atlas 数据源

 - 打开【连接】

### 3.6.2 创建/设计API

 - 打开【数据发布】
 - 点击【新建】按钮，创建标准增删查改 API 和 创建 自定义查询 API
 
### 3.6.3 发布 API

 - 打开【数据发布】
 - 点击【发布】按钮

## 3.7 浏览查询数据

 - 打开【数据浏览】
 - 选择已经发布的 API 查看数据

## 3.8 调用 OpenAPI 接口，完成业务开发

客户端应用调用 Moa 发布的 API 需使用 [OAuth 2.0](https://oauth.net/2/) 授权访问 API Server， 流程如下：
![OAuth](img/OAuth.png)

### 3.8.1 调用 Moa OAuth 认证接口获取 JWT Token （ Client Credentials grant type）
 - POST(x-www-form-urlencoded) 方法请求 [http://openapi.mongodb.expert/oauth/token](http://openapi.mongodb.expert/oauth/token) ，提交参数如下：
```
grant_type: client_credentials					// 固定值
client_id: b5fd01c5996a634320ebf53cbab5fadf			// API Server ID
client_secret: 795a357ff5c2cc895b5a2b8e0a0e883a			// API Server JWT Secret 
scope: api-server						/ 固定值
username: <EMAIL>					// 用户登录邮箱
```
 
 - 响应如下：
 ```json
{
"access_token": "eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6ImI1ZmQwMWM1OTk2YTYzNDMyMGViZjUzY2JhYjVmYWRmIiwidXNlcl9pZCI6IjVjZTBiYzUxMzNmZDI5NGI2YTI1YTYzYyIsImNyZWF0ZWRBdCI6MTU1ODM2ODg4NDk0NCwicm9sZXMiOlsiNWNlMGJjNTEzM2ZkMjk0YjZhMjVhNjNjIl0sImV4cGlyZWRhdGUiOjE1NTgzNzA2ODQ5NDR9.fKSCRs2pCNdYYVCYjM-W1OfmQO057EKKOTO1n89Q998",
"expires_in": 1209600,
"scope": "api-server",
"refresh_token": "eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6ImI1ZmQwMWM1OTk2YTYzNDMyMGViZjUzY2JhYjVmYWRmIiwidXNlcl9pZCI6IjVjZTBiYzUxMzNmZDI5NGI2YTI1YTYzYyIsImNyZWF0ZWRBdCI6MTU1ODM2ODg4NDk0NSwicm9sZXMiOlsiNWNlMGJjNTEzM2ZkMjk0YjZhMjVhNjNjIl0sImV4cGlyZWRhdGUiOjE1NTgzNzA2ODQ5NDV9.3p0we5y-XaUKJXVH8_yk6tjd9z-gV7uKK1xiflhjRrk",
"token_type": "Bearer"
}
```
 
### 3.8.2 调用 Moa OAuth 认证接口获取 JWT Token （Password grant type）
 - 浏览器请求
 
 http://openapi.mongodb.expert/oauth/authorize?client_id=6f68ca8e9f53f4e6f994bceb7c4a7f4d&response_type=token&grant_type=password&scope=api-server&redirect_uri=http://47.75.94.238:3080

参数说明
```
 client_id: 6f68ca8e9f53f4e6f994bceb7c4a7f4d			// API Server ID
 response_type: token									// 固定值
 grant_type： password									// 固定值
 scope: api-server										// 固定值
 redirect_uri: http://47.75.94.238:3080					// 授权后的跳转地址
```
 - 用户输入用户密码授权后跳转地址如下：
 
 http://47.75.94.238:3080/#access_token=eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6IjZmNjhjYThlOWY1M2Y0ZTZmOTk0YmNlYjdjNGE3ZjRkIiwidXNlcl9pZCI6IjVjZTNhNjRjODQ5MjMwMDMwOWMyMWEyMSIsImNyZWF0ZWRBdCI6MTU1OTE4NDk1MjY5Miwicm9sZXMiOlsiNWNlM2E2NGM4NDkyMzAwMzA5YzIxYTIxIl0sImV4cGlyZWRhdGUiOjE1NTkxODY3NTI2OTJ9.O6l-LQTH4ohGgxuLMQhND_H7-YZVG244tnQybtZY2Wo&expires_in=1209600&scope=api-server&token_type=Bearer

响应参数
```
access_token: eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6IjZmNjhjYThlOWY1M2Y0ZTZmOTk0YmNlYjdjNGE3ZjRkIiwidXNlcl9pZCI6IjVjZTNhNjRjODQ5MjMwMDMwOWMyMWEyMSIsImNyZWF0ZWRBdCI6MTU1OTE4NDk1MjY5Miwicm9sZXMiOlsiNWNlM2E2NGM4NDkyMzAwMzA5YzIxYTIxIl0sImV4cGlyZWRhdGUiOjE1NTkxODY3NTI2OTJ9.O6l-LQTH4ohGgxuLMQhND_H7-YZVG244tnQybtZY2Wo
expires_in: 1209600
scope: api-server
token_type: Bearer
```
 
### 3.8.3 调用 API 接口获取数据 
 - 查询分页
 	```
	
	 // 请求
	 GET http://127.0.0.1:3083/api/v1/User?filter[order]=name+DESC&filter[limit]=10&filter[skip]=0
	 // 请求头
	 token: eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6ImI1ZmQwMWM1OTk2YTYzNDMyMGViZjUzY2JhYjVmYWRmIiwidXNlcl9pZCI6IjVjZTBiYzUxMzNmZDI5NGI2YTI1YTYzYyIsImNyZWF0ZWRBdCI6MTU1ODM2ODg4NDk0NCwicm9sZXMiOlsiNWNlMGJjNTEzM2ZkMjk0YjZhMjVhNjNjIl0sImV4cGlyZWRhdGUiOjE1NTgzNzA2ODQ5NDR9.fKSCRs2pCNdYYVCYjM-W1OfmQO057EKKOTO1n89Q998
	
	 // 响应
	 {
	 	"data":[{
	 		"_id":"5c7df3f47178c48ec19e5bc5",
	 		"name":"王大为",
	 		"age":10,"desc":["Good","Boy","Happly"],"email":"<EMAIL>",
	 		"metadata":{"home_page":"http://www.baidu.com"},"username":"lrf"
	 	},{
	 		...
	 	}],
	 	"total":{
	 		"count":21
	 	}
	 }
	```

 - 创建记录
 - 修改记录
 - 删除记录
 
## 3.9 查看 OpenAPI 接口文档

 - 打开 【数据浏览】
 - 选择 【API Server】，指定要查看 API Server
 - 点击 【API 文档】 打开 API 文档页面


# 4 API Server 原理
## 4.1发布一流程
```

管理端发布(modules)  -> Api Server 轮询配置(/api/modules/apiDefinition)  -> 有更新 -> 执行发布

# 发布过程：

先缓存 API 定义配置 -> 校验(格式、数据库连接) -> 执行API代码的生成 -> 重启工作进程

```
