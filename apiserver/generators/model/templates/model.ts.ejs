<% if (isModelBaseBuiltin) { -%>
import {<%= modelBaseClass %>, model, property} from '@loopback/repository';
<% } else { -%>
import {model, property} from '@loopback/repository';
import {<%= modelBaseClass %>} from '.';
<% } -%>

<% if(dataSourceType === 'oracle'){ -%>
  @model({name: '<%= tableName || name %>', title: '<%= title || tableName || name %>', settings: { oracle: {schema: '<%= schema %>' ,table: '<%= tableName %>'}}})
<% } else { -%>
  @model({name: '<%= tableName || name %>', title: '<%= title || tableName || name %>', settings: { postgresql: {table: '<%= tableName %>'},strict: false, allowExtendedOperators: true}})
<% } -%>
export class <%= className %> extends <%= modelBaseClass %> {
<% if(dataSourceType === 'mongodb'){ -%>
      <% Object.entries(properties).forEach(([key, val]) => { -%>
        @property({
        <% if (val.tsType === 'Decimal128') { -%>
        type: 'string',
        mongodb: {
        dataType: 'Decimal128',
        }
        <%_ }else{ -%>
        <%_ Object.entries(val).forEach(([propKey, propVal]) => { -%>
        <%_ if (!['tsType'].includes(propKey)) { -%>
          <%= propKey %>: <%- propVal %>,
        <%_ } -%>
        <%_ }) -%>
        <%_ } -%>
        })
        '<%= key %>'<%if (!val.required) {%>?<% } %>: <%= val.tsType === 'Decimal128' ? 'string' : val.tsType %> | null;
      <% }) -%>
  <% } else if(dataSourceType === 'oracle'){ -%>
    <% Object.entries(properties).forEach(([key, val]) => { -%>
      @property({
      <%_ Object.entries(val).forEach(([propKey, propVal]) => { -%>
      <%_ if (!['tsType'].includes(propKey)) { -%>
        <%= propKey %>: <%- propVal %>,
      <%_ } -%>
      <%_ }) -%>
        oracle:{
        columnName:'<%= key %>'
        }
      })
      '<%= key %>'<%if (!val.required) {%>?<% } %>: <%= val.tsType === 'Decimal128' ? 'string' : val.tsType %> | null;
    <% }) -%>
  <% } else { -%>
      <% Object.entries(properties).forEach(([key, val]) => { -%>
        @property({
        <% if (val.tsType === 'Decimal128') { -%>
        type: 'string',
        mongodb: {
        dataType: 'Decimal128',
        }
        <%_ }else{ -%>
        <%_ Object.entries(val).forEach(([propKey, propVal]) => { -%>
          <%_ if (!['tsType'].includes(propKey)) { -%>
          <%= propKey %>: <%- propVal %>,
          <%_ } -%>
        <%_ }) -%>
        <%_ } -%>
        <% if(dataSourceType === 'postgresql'){ -%>
          postgresql:{
            columnName:'<%= key %>'
          }
        <% } -%>
        })
        '<%= key %>'<%if (!val.required) {%>?<% } %>: <%= val.tsType === 'Decimal128' ? 'string' : val.tsType %> | null;
      <% }) -%>
<% } -%>
  constructor(data?: Partial<<%= className %>>) {
    super(data);
  }
}

