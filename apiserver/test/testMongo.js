let mongodb = require('mongodb');

new mongodb.MongoClient('mongodb://127.0.0.2/tapdata').connect((err, client) => {

	if( err ){
		console.error(err);
	} else {
		console.log("Connected successfully to server");

		let db = client.db();
		db.collection('Logs').find({
			date: {
				$gt: new Date('2019-12-11 00:00:00')
			}
		}, (err, data) => {
			data.toArray( (err, records) => {
				console.log(records);
				client.close();
			} )
		});

	}

});

