/**
 * <AUTHOR>
 * @date 2/26/19
 * @description
 */

const request = require('request');
const Conf = require('conf');
const config = new Conf();


request.post({
	url: config.get('tapDataServer.tokenUrl'),
	form: {
		accesscode: config.get('tapDataServer.accessCode')
	}
}, (err, response, body) => {
	if (response.statusCode === 200) {
		if(body) {
			try{
				body = JSON.parse(body);
			}catch (e) {
				log.error('parse config error: \n', e);
				return;
			}
			if ("ok" === body.code) {
				body = body.data;
				console.log(JSON.parse(body).id)
			}
		}else{
			console.log(body.msg)
		}
	} else {
		console.log(err)
	}
});

