/**
 * <AUTHOR>
 * @date 12/11/19
 * @description
 */
exports.getNonLocalIPv4Address = function(){
	const networkInterfaces = require('os').networkInterfaces();
	let ipAddress = [];
	Object.keys(networkInterfaces).forEach(ifName => {
		let networkInterface = networkInterfaces[ifName];
		if( networkInterface && Array.isArray(networkInterface) ){
			networkInterface.forEach( ipConfig => {
				if( ipConfig.family === 'IPv4' && ipConfig.address !== '127.0.0.1'){
					ipAddress.push(ipConfig.address);
				}
			});
		}
	});
	return ipAddress
};
