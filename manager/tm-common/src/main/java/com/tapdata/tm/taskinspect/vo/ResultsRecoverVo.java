package com.tapdata.tm.taskinspect.vo;

import lombok.Data;

/**
 * 上报差异修复结果
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/3/28 18:54 Create
 */
@Data
public class ResultsRecoverVo {
    private String sourceTable;
    private String rowId;

    public static ResultsRecoverVo of(String table, String rowId) {
        ResultsRecoverVo vo = new ResultsRecoverVo();
        vo.setSourceTable(table);
        vo.setRowId(rowId);
        return vo;
    }
}
